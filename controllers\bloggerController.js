const catchAsync = require("./../utils/catchAsync");
const User = require("./../models/userModel");
const Blogger = require("./../models/bloggerModel");
const Post = require("./../models/postModel");

exports.create = catchAsync(async (req, res, next) => {
  for (let blogger of req.body.data) {
    let fingBlogger = await Blogger.findOne({ user: blogger.user._id });
    if (fingBlogger !== null) {
      await Blogger.findByIdAndUpdate(blogger._id, blogger);
    } else {
      await Blogger.create(blogger);
    }
  }
  res.status(201).json({
    status: "success",
    data: {},
  });
});

exports.getAll = catchAsync(async (req, res, next) => {
  let resultsPerPage = req.body.resultsPerPage;
  let page = req.body.page;
  page = page - 1;

  let data = await Blogger.find()
    .populate({
      path: "user",
      select: [
        "id",
        "_id",
        "name",
        "surname",
        "isVerified",
        "photoColor",
        "untrusted",
        "earnCount",
        "likesCount",
        "commentsCount",
        "postCount",
        "email",
      ],
    })
    .populate({
      path: "posts",
      select: [
        "id",
        "_id",
        "giftsCount",
        "likesCount",
        "commentsCount",
        "user",
      ],
    })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ _id: -1 });

  let count = await Blogger.countDocuments();

  res.status(200).json({
    status: "success",
    data: {
      data: data,
      count: count,
    },
  });
});

exports.getAllPostUserId = catchAsync(async (req, res, next) => {
  let resultsPerPage = req.body.resultsPerPage;
  let page = req.body.page;
  page = page - 1;

  let filter = {
    isImageContent: true,
    isActive: true,
    isPayPerView: false,
    user: req.body.userId,
  };

  let data = await Post.find(filter, {
    images: 0,
    user: 0,
    blurImages: 0,
    backgroundImages: 0,
    comment: 0,
    fontSize: 0,
    isActive: 0,
    isEditedCropImage: 0,
    isImageContent: 0,
    isPayPerView: 0,
    isPromotion: 0,
    isShareLink: 0,
    isStatusContent: 0,
    isVideoContent: 0,
    linkUrl: 0,
    title: 0,
    updatedAt: 0,
  })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ _id: -1 });

  res.status(200).json({
    status: "success",
    data: {
      data: data,
    },
  });
});

exports.findByIdAndDelete = catchAsync(async (req, res, next) => {
  let fingBlogger = await Blogger.findOne({ user: req.body.userId });
  if (fingBlogger !== null) {
    await Blogger.findByIdAndDelete(fingBlogger._id);
  }
  res.status(201).json({
    status: "success",
  });
});

exports.search = catchAsync(async (req, res, next) => {
  if (req.body.searchText !== "") {
    let resultsPerPage = req.body.resultsPerPage;
    let page = req.body.page;
    page = page - 1;
    req.body.searchText = req.body.searchText.trim();

    let name = req.body.searchText.split(" ")[0];
    let surname =
      req.body.searchText.split(" ")[1] === undefined
        ? name
        : req.body.searchText.split(" ")[1];

    let nameRegex = name.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");
    let surnameRegex = surname.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");

    nameRegex = new RegExp(nameRegex, "i");
    surnameRegex = new RegExp(surnameRegex, "i");

    let filter = {
      $or: [
        { "user.name": { $regex: nameRegex } },
        { "user.surname": { $regex: surnameRegex } },
      ],
    };

    Blogger.find(filter)
      .populate({
        path: "user",
        select: [
          "id",
          "_id",
          "name",
          "surname",
          "isVerified",
          "photoColor",
          "untrusted",
        ],
      })
      .populate({
        path: "posts",
        select: [
          "id",
          "_id",
          "giftsCount",
          "likesCount",
          "commentsCount",
          "user",
        ],
      })
      .limit(resultsPerPage)
      .skip(resultsPerPage * page)
      .sort({ _id: -1 })
      .then(async (result) => {
        // console.log("result: ",result)
        let countList = await Blogger.find(filter, { user: 0 });
        let count = countList.length;
        res.status(200).json({
          status: "success",
          data: {
            data: result,
            count: count,
          },
        });
      });
  } else {
    res.status(200).json({
      status: "success",
      resultSearch: result,
    });
  }
});
