import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, ActivationEnd, NavigationEnd, NavigationStart, Router, RoutesRecognized } from '@angular/router';
import { OnlineStatusService, OnlineStatusType } from 'ngx-online-status';
import { map, Observable, Subscription } from 'rxjs';
import { User } from '../shared/models/user';
import { AuthService } from '../shared/services/auth.service';
import { ChatRoomService } from '../shared/services/chat-room.service';
import { LoadJsService } from '../shared/services/load-js.service';
import { NavigationService } from '../shared/services/navigation.service';
import { SocketService } from '../shared/services/socket.service';

@Component({
  selector: 'app-main-route',
  templateUrl: './main-route.component.html',
  styleUrls: [
    './main-route.component.css']
})
export class MainRouteComponent implements OnInit {

  status: OnlineStatusType;
  OnlineStatusType = OnlineStatusType;
  private userSub: Subscription;
  user: User;
  userIsAuthenticated = false
  params_lang_code = 'en'
  socketChat
  newMessagesStatus = false

  constructor(
    private socketService: SocketService,
    private chatRoomService:  ChatRoomService,
    private onlineStatusService: OnlineStatusService,
    public navigation: NavigationService,
    private activatedRoute : ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    public loadJsService: LoadJsService) {
      this.socketChat = this.socketService.setupChatSocketConnection();
      this.onlineStatusService.status.subscribe((status: OnlineStatusType) => {
        this.status = status;
      });

      this.navigation.startSaveHistory();
      this.router.events.subscribe( 
        (path: ActivationEnd) => {
              if(path.snapshot?.params.lang){
                  this.params_lang_code = path.snapshot.params.lang
              }
            if(path.snapshot?.url[1]){
              if(path.snapshot?.url[1].path.includes('/auth')){
                this.checkAuth(path.snapshot.url[1].path)
              }
          }
        }
      );
  }



  ngOnInit() {
    this.checkAuth('')
  }

  // ngAfterViewChecked(){
  //   this.checkAuth()
  // }

  checkAuth(url){
    this.authService.user.pipe().subscribe(appUser => {
      this.user = appUser
      if(this.user != null){
        this.userIsAuthenticated = true
      }else{
        this.userIsAuthenticated = false
        if(!url.includes('/auth')){
          this.router.navigate([this.params_lang_code+'/auth'])
        }
      }
    })
    if(this.user){
      this.getChatRooms()
    }
  }
  

  ngOnDestroy() {
    if (this.userSub) {
      this.userSub.unsubscribe();
    }
    
  }

  getChatRooms(){
    if(this.user?._id){
      this.socketChat.emit('getRooms', {
        userId: '630a2e5d4989aae851a657e4',
        resultsPerPage: 15
      });
      this.socketChat.on('getRooms', async (data: any) => {
        if(data?.userId === '630a2e5d4989aae851a657e4' ){
          // console.log("data?.userId : ",data?.userId )
          if (data?.updateManyOkId?.toString() === '630a2e5d4989aae851a657e4') {
            let newRoomSocket = data?.newRoomSocket === true ? true :  false
            this.chatRoomService.changeChatRooms({ 
              userId: data?.userId,
              newRoomSocket : newRoomSocket,
              updateManyOkId: data?.updateManyOkId?.toString(),
              chatRoomIdDeleted: data?.chatRoomIdDeleted
            })
          } else {
            if (data?.userId === '630a2e5d4989aae851a657e4') {
              if (data.rooms.length > 0) {
                let rooms = await data.rooms.map((e) => {
                  for (let user of e.users) {
                    if (user._id !== '630a2e5d4989aae851a657e4') {
                      e['anotherUser'] = user;
                      return e;
                    }
                  }
                });
                let newRoomSocket = data?.newRoomSocket === true ? true :  false
                
                if(newRoomSocket){
                  this.newMessagesStatus = true
                }else{
                  for(let room of rooms){
                    if (room?.usersUnReadTotal) {
                      for (let total of room?.usersUnReadTotal) {
                        if (room?.anotherUser?._id.toString() !== total._userId.toString()) {
                            if(total.unReadTotal > 0){
                              this.newMessagesStatus = true
                            }
                        }
                      }
                    }
                  }
                }
                this.chatRoomService.changeChatRooms({ 
                  userId: data?.userId,
                  rooms : rooms,
                  newRoomSocket : newRoomSocket
                })
                // this.searchMessageLoading = false;
              }else{
                let newRoomSocket = data?.newRoomSocket === true ? true :  false
                this.chatRoomService.changeChatRooms({ 
                  userId: data?.userId,
                  rooms : [],
                  newRoomSocket : newRoomSocket
                })
              }
            }
          }
        }
        // this.searchMessageLoading = false;
      });

    }

  }

}
