<div class="projects-section exchange-section">
  <div
    style="
      grid-template-rows: 1fr 2fr 1fr;
      border-radius: 20px;
      overflow: hidden;
      gap: 1.5em;
      min-height: 530px;
    "
    class="disp-grid"
  > 
    <div
      *ngIf="!disableWithdrawButton"
      style="position: relative; justify-content: start"
      class="disp-flex a-i-center"
    >
      <img
        (click)="openOptionsModal(optionsContent)"
        style="
          position: absolute;
          height: 25px;
          width: 25px;
          top: 0;
          right: 0;
          margin: 5px 5px 0 0;
          z-index: 1;
        "
        src="/assets/icons/options.svg"
        alt=""
      />
      <button
        (click)="openUserSearchModal(searchContent)"
        style="
          position: absolute;
          height: 30px;
          width: 30px;
          top: 100%;
          right: 0;
          z-index: 1;
          transform: translateY(-100%);
          display: flex;
          align-items: center;
          border-radius: 50%;
          border: none;
          justify-content: center;
        "
        title="Add New Project"
      >
        <svg
          class="btn-icon"
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="3"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="feather feather-plus"
        >
          <line x1="12" y1="5" x2="12" y2="19" />
          <line x1="5" y1="12" x2="19" y2="12" />
        </svg>
      </button>
      <img
        style="max-width: 88px"
        src="../assets/exchangeBIG_ANIM_TOP_INFINITE.svg"
        alt=""
      />
    </div>
    <img
      style="margin-top: 60% !important;"
      *ngIf="disableWithdrawButton"
        src="assets/exchangeBIG_ANIM_TOP_INFINITE.svg"
        alt=""
      />
    <div
     *ngIf="!disableWithdrawButton"
     class="exchange-tiles-Wrapper">
      <div *ngIf="userSelected" style="">
        <div class="cardWrapper">
          <div style="display: flex; align-items: center" class="cardRec">
            <div class="cardImage">
              <app-avatar-photo
              [buffer]="userSelected?.photoProfile?.data"
              [userId]="userSelected?._id"
              style="cursor: pointer"
              [circleColor]="userSelected?.photoColor"
              [name]="userSelected?.name"
              [surname]="userSelected?.surname"
              [class]="'atmAvatar'"
              [classAvatarInitials]="'atmAvatarInitials'"
            ></app-avatar-photo>
            </div>
            <div class="cardContent">
              <p
                style="
                  color: white;
                  font-weight: 600;
                  margin-bottom: 0;
                  margin-top: 0;
                  display: flex;
                  align-items: center;
                  line-height: 1;
                "
              >
                {{ userSelected?.name }} {{ userSelected?.surname }}
                <img
                  (click)="userSelected = null"
                  style="height: 17px; width: 17px; margin-left: auto"
                  src="/assets/icons/close.svg"
                  alt=""
                />
              </p>
              <div
                style="
                  display: flex;
                  gap: 1em;
                  line-height: 1.4;
                  align-items: center;
                  height: 22px;
                  margin-top: 10px;
                "
              > 
                <p style="font-size: 10px">
                  Balance
                  <span>{{ userSelected?.coins | number }} </span>credits
                </p>
              </div>

              <div style="display: flex; gap: 5px" class="cardNumbersWrapper">
                <div class="cardNumbers">
                  <span class="articles">Following</span>
                  <span class="number1">{{
                    userSelected?.followingCount
                  }}</span>
                </div>
                <div class="cardNumbers">
                  <span class="followers">Followers</span>
                  <span class="number2">{{
                    userSelected?.followersCount
                  }}</span>
                </div>

                <div class="cardNumbers">
                  <span class="articles">Earned</span>
                  <span class="number1">{{ userSelected?.earnCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        style="
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: center;
          gap: 1em;
        "
      >
        <form
          style="
            gap: 1em;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
          "
          [formGroup]="exchangeForm"
        >
          <div class="exchange-tile shadow1">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
              "
            >
              <span style="font-size: var(--font-size-l)">Credits</span>
              <span *ngIf="userSelected" style="font-size: smaller;opacity: 0.5">
                {{ userSelected?.coins | number }} credits(User balance)</span>
            </div>
            <div style="display: flex; align-items: center">
              <div class="creditsInputWrapper">
                <input
                  (keyup)="typeCreditsAmount($event)"
                  formControlName="creditsAmount"
                  class="creditsInput"
                  type="number"
                  placeholder="eg. 100"
                />
              </div>
            </div>
          </div>
          <div class="exchange-animation" style="">
            <img src="/assets/transactionsmallANIM-infinite.svg" alt="" />
          </div>
          <div class="exchange-tile">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
              "
            >
              <span style="font-size: var(--font-size-l)">{{
                settingsForm?.value?.currency
              }}</span>
              <span style="opacity: 0.5"
                >Fee: {{ settingsForm?.value?.feePercentage }}%</span
              >
            </div>
            <div style="display: flex; align-items: center">
              <div class="creditsInputWrapper">
                <input
                  formControlName="realMoneyAmount"
                  class="creditsInput"
                  type="number"
                  placeholder="eg. 100"
                />
              </div>
            </div>
          </div>
        </form>
      </div>
      <br />
      <p class="credit-to-euro">
        1 credit = {{ settingsForm?.value?.exchangeRate }}
        {{ settingsForm?.value?.currency }}
      </p>
    </div>
    <div
    *ngIf="!disableWithdrawButton"
      style="
        border-top: 1px dotted var(--filter-reset);
        margin: 1em 0 0 0;
        padding: 5px 0 2em 0;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;
      "
    >
      <div
        style="
          display: flex;
          justify-content: space-between;
          width: 100%;
          padding: 0 2em;
        "
      >
        <span>
          <!-- Fees -->
        </span>
        <span>
          <!-- EUR 10 -->
        </span>
      </div>
      <div 
      *ngIf="!disableWithdrawButton"
      style="cursor: pointer" (click)="exchange()" class="exchange-button">
        Exchange
      </div>
    </div>
  </div>
  <div
    style="
      display: grid;
      grid-template-rows: 4em 2em 1fr 4em;
      background-color: var(--sidebar);
      padding: 5px 1em;
      border-radius: 20px;
      height: 76vh;
    "
  >
    <div class="disp-flex a-i-center">
      <span class="no-wrap-1-line">ATM Transactions</span>
      <div class="m-l-auto disp-flex">
        <div>
          <input style="display: none" id="dropdownInput" type="checkbox" />
          <div
            style="
              display: flex;
              justify-content: center;
              height: 35px;
              width: 147px;
            "
            class="dropdown"
          >
            <select
              style="
                display: flex;
                background: transparent;
                border: none;
                color: white;
                font-size: 18px;
                width: 90%;
                font-weight: 600;
              "
              (ngModelChange)='changeStatus($event)' 
              [(ngModel)]="statusSelected">
              <option class="optionStyle colorCancel">All</option>
              <option class="optionStyle colorCancel">Waiting Confirm</option>
              <option class="optionStyle colorCancel">Declined</option>
              <option class="optionStyle colorCancel">Processing</option>
              <option class="optionStyle colorCancel">Canceled</option>
              <option class="optionStyle colorCancel">Waiting</option>
              <option class="optionStyle colorCancel">Success</option>
            </select>
            <label for="dropdownInput" class="overlay"></label>
          </div>
        </div>
        <div class="search-bar">
          <input 
          (keyup)="searchUser($event)"
          type="text" placeholder="Search" />
        </div>
      </div>
    </div>
    <div
      style="
        display: grid;
        grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.5fr;
        background-color: rgba(26, 37, 59, 0.5);
        align-items: center;
        margin-bottom: 5px;
        border-radius: 10px;
        gap: 1em;
      "
    >
      <span style="margin-left: 1em" class="no-wrap-1-line">Order Details</span>
      <span class="no-wrap-1-line">Fee</span>
      <span class="no-wrap-1-line">Withdraw</span>
      <span class="no-wrap-1-line">Type</span>
      <span class="no-wrap-1-line">Status</span>
      <span class="disp-flex j-c-center">
        <!-- Actions -->
      </span>
    </div>
          <!-- List empty  -->
          <div
          *ngIf="data?.length === 0 && !startLoading && !loading" 
            style="
              position: relative;
              display: flex;
              justify-content: center;
              align-items: center;
              flex-direction: column;
            "
          >
            <img
              style="width: 70%; max-width: 299px"
              src="/assets/icons/animatedIconsTable/atm_eptylistanim_infinite.svg"
              alt=""
            />
            <p style="font-size: 16px">List is empty</p>
          </div>
          <!-- List empty end -->
    <ul style="padding-inline-start: 0px; overflow: scroll" class="">
      <ng-container *ngIf="loading">
        <li
          *ngFor="let i of [].constructor(15)"
          style="
            display: grid;
            grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.5fr;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div style="margin-left: 13px" class="disp-flex">
            <div class="disp-grid">
              <span
                class="skeleton1"
                style="
                  font-size: 14px;
                  font-weight: 500;
                  opacity: 0.7;
                  width: 67px;
                  height: 17px;
                  margin-bottom: 4px;
                "
              ></span>
              <span
                class="skeleton2"
                style="
                  font-size: 14px;
                  font-weight: 500;
                  opacity: 0.7;
                  width: 111px;
                  height: 16px;
                "
              ></span>
            </div>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton1"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 67px;
                height: 17px;
                margin-bottom: 4px;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 111px;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton1"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 78px;
                height: 17px;
                margin-bottom: 4px;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 50px;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton1"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 67px;
                height: 17px;
                margin-bottom: 4px;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 111px;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 100%;
                height: 29px;
              "
            ></span>
          </div>
          <div class="disp-grid"></div>
        </li>
      </ng-container>

      <ng-container *ngIf="!loading">
        <li
          *ngFor="
            let dat of data;
            let i = index
          "
          style="
            display: grid;
            grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.5fr;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div class="disp-flex gap-05">
            <div style="min-width: 10px; max-width: 10px; opacity: 1"></div>
            <div class="disp-grid">
              <span class="no-wrap-1-line"
                >{{ dat?.user?.name }} {{ dat?.user?.surname }}</span
              >
              <span
                class="no-wrap-1-line"
                style="font-size: 14px; font-weight: 500; opacity: 0.7"
                >{{ dat?.createdAt | date : "MMM d, y, h:mm a" }}</span
              >
            </div>
          </div>
          <div class="disp-grid">
            <span class="no-wrap-1-line"
              >{{ dat?.feeCreditsAmount }} credits</span
            >
            <span
              class="no-wrap-1-line"
              style="font-size: 14px; font-weight: 500; opacity: 0.7"
              >Total: {{ dat?.creditsAmount }} credits</span
            >
          </div>
          <div class="disp-grid">
            <span class="no-wrap-1-line"
              >{{ dat?.withdrawCreditsAmount }} credits</span
            >
            <span
              class="no-wrap-1-line"
              style="font-size: 14px; font-weight: 500; opacity: 0.7"
              >{{ dat?.withdrawRealMoneyAmount }}
              {{ dat?.withdrawRealMoneyCurrency }}</span
            >
          </div>
          <div class="disp-grid">
            <span class="no-wrap-1-line">{{ dat?.type }}</span>
            <span
              class="no-wrap-1-line"
              style="font-size: 14px; font-weight: 500; opacity: 0.7"
              >{{ dat?.email }}
            </span>
          </div>
          <span
            *ngIf="dat?.status === 'processing'"
            class="status-inProgress no-wrap-1-line"
          >
            Processing
          </span>
          <span
            *ngIf="dat?.status === 'canceled'"
            class="status-canceled no-wrap-1-line"
          >
            Canceled
          </span>
          <span
            *ngIf="dat?.status === 'waiting'"
            class="status-waiting no-wrap-1-line"
          >
            Waiting
          </span>
          <span
            *ngIf="dat?.status === 'succes'"
            class="status-success no-wrap-1-line"
          >
            Success
          </span>
          <span
            *ngIf="dat?.status === 'waitingConfirm'"
            class="status-waiting  no-wrap-1-line">
            Waiting Confirm
          </span>
          <span
            *ngIf="dat?.status === 'declined'"
            class="status-canceled no-wrap-1-line"
          >
            Declined
          </span>
          
          <div class="disp-flex j-c-center">
            <span
              (click)="
                selectAtmTransaction(dat); 
                openStatusChangeModal(statusContent)
              "
              class="no-wrap-1-line"
              >•••</span
            >
          </div>
        </li>
      </ng-container>
    </ul>
    <div class="list-number disp-flex" style="">
      <div
        class="showingInfoWrapper"
        style="
          margin: 0 0 0.75rem;
          display: flex !important;
          align-items: center;
          gap: 1em;
        "
      >
        <span class="showingInfo"
          >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
          {{ pageNo * resultsPerPage - (resultsPerPage - data.length) }} of
          {{ count }}</span>
        <div style="border: 1px solid gray; border-radius: 15px">
          <div>
            <input style="display: none" id="dropdownInput" type="checkbox" />
            <div
              style="
                display: flex;
                justify-content: center;
                height: 45px;
                width: 50px;
              "
              class="dropdown"
            >
              <select
                style="
                  display: flex;
                  background: transparent;
                  border: none;
                  color: white;
                  font-size: 18px;
                  width: 90%;
                  font-weight: 600;
                "
                (ngModelChange)='resultsPerPageChanged($event)' 
                [(ngModel)]="resultsPerPage">
                <option class="optionStyle colorCancel">7</option>
                <option class="optionStyle colorCancel">10</option>
                <option class="optionStyle colorCancel">15</option>
                <option class="optionStyle colorCancel">20</option>
                <option class="optionStyle colorCancel">30</option>
                <option class="optionStyle colorCancel">50</option>
              </select>
              <label for="dropdownInput" class="overlay"></label>
            </div>
          </div>
        </div>
      </div>
      <nav>
        <ul class="pager">
          <li class="pager__item pager__item--prev">
            <a
              *ngIf="pageNo !== 1"
              (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Previous
            </a>
          </li>
          <li *ngIf="pageNo !== 1" class="pager__item">
            <a (click)="setPage(1)" class="pager__link">...</a>
          </li>
          <li
            *ngFor="
              let item of [].constructor(pageNoTotal) | slice : 0 : 5;
              let i = index
            "
            [ngClass]="pageNo + i === pageNo ? 'active' : ''"
            class="pager__item"
          >
            <a
              *ngIf="pageNo + i <= pageNoTotal"
              (click)="setPage(pageNo + i)"
              class="pager__link"
              >{{ pageNo + i }}</a
            >
          </li>
          <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
            <a (click)="setPage(pageNoTotal)" class="pager__link">...</a>
          </li>
          <li
            *ngIf="pageNo !== pageNoTotal"
            class="pager__item pager__item--next"
          >
            <a
              (click)="pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Next
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>

  <ng-template #optionsContent let-modal>
    <div
      style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1"
    >
      <div class="modal-body">
        <!-- header -->
        <div style="display: flex; justify-content: end; width: 100%">
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; margin-left: auto"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->

        <div style="margin: 1em 0">
          <div style="display: flex; flex-direction: column; gap: 2em">
            <form [formGroup]="settingsForm">
              <div style="border: 1px solid gray; border-radius: 15px">
                <label
                  for="currency"
                  style="
                    margin: -11px 0px 0 14px;
                    font-size: 16px;
                    font-weight: 600;
                    background: var(--app-bg);
                    width: fit-content;
                    padding: 0 13px;
                    display: flex;
                  "
                >
                  Currency
                </label>
                <div>
                  <input
                    style="display: none"
                    id="dropdownInput"
                    type="checkbox"
                  />
                  <div
                    style="display: flex; justify-content: center"
                    class="dropdown"
                  >
                    <select
                      style="
                        display: flex;
                        background: transparent;
                        border: none;
                        color: white;
                        font-size: 18px;
                        width: 90%;
                        font-weight: 600;
                      "
                      formControlName="currency"
                    >
                      <option
                        class="optionStyle colorCancel"
                        *ngFor="let currency of currencyTypes"
                        [ngValue]="currency"
                      >
                        {{ currency }}
                      </option>
                    </select>
                    <label for="dropdownInput" class="overlay"></label>
                  </div>
                </div>
              </div>
              <br />
              <br />
              <!-- <div style="border: 1px solid gray; border-radius: 15px">
                <p
                  style="
                    margin: -11px 0px 0 14px;
                    font-size: 16px;
                    font-weight: 600;
                    background: var(--app-bg);
                    width: fit-content;
                    padding: 0 13px;
                  "
                >
                  Exchange rate
                </p>
                <div style="display: flex; align-items: center">
                  <div class="exchangeRateInputWrapper">
                    <input
                      formControlName="exchangeRate"
                      class="exchangeRateInput"
                      type="number"
                      placeholder="eg. 100"
                    />
                  </div>
                </div>
              </div>
              <br />
              <br /> -->
              <div style="border: 1px solid gray; border-radius: 15px">
                <p
                  style="
                    margin: -11px 0px 0 14px;
                    font-size: 16px;
                    font-weight: 600;
                    background: var(--app-bg);
                    width: fit-content;
                    padding: 0 13px;
                  "
                >
                  Fee Percentage
                </p>
                <div style="display: flex; align-items: center">
                  <div class="exchangeRateInputWrapper">
                    <input
                      formControlName="feePercentage"
                      class="exchangeRateInput"
                      type="number"
                      placeholder="eg. 1%"
                    />
                  </div>
                </div>
              </div>
            </form>
            <div
              style="display: flex; justify-content: end; align-items: center"
            >
              <div (click)="modal.close('Close click')" class="saveButton">
                Save
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>

  <ng-template #searchContent let-modal>
    <app-search-content
    [(me)]="me"
    (closeModal)="modal.dismiss('Cross click')"
    (userSelected)="selectUser($event)"
    ></app-search-content>
  </ng-template>

  <ng-template #statusContent let-modal>
    <div
      style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1"
    >
      <div class="modal-body">
        <!-- header -->
        <div style="display: flex; justify-content: end; width: 100%">
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; margin-left: auto"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->

        <div style="margin: 1em 0">
          <div style="display: flex; flex-direction: column; gap: 2em">
            <form [formGroup]="form">
              <div style="border: 1px solid gray; border-radius: 15px">
                <label
                  for="currency"
                  style="
                    margin: -11px 0px 0 14px;
                    font-size: 16px;
                    font-weight: 600;
                    background: var(--app-bg);
                    width: fit-content;
                    padding: 0 13px;
                    display: flex;
                  "
                >
                  Status
                </label>
                <div
                *ngIf="
                atmTransactionSelected?.status !== 'declined' &&
                atmTransactionSelected?.status !== 'waitingConfirm'">
                  <input
                    style="display: none"
                    id="dropdownInput"
                    type="checkbox"
                  />
                  <div
                    style="display: flex; justify-content: center"
                    class="dropdown"
                  >
                    <select
                      style="
                        display: flex;
                        background: transparent;
                        border: none;
                        color: white;
                        font-size: 18px;
                        width: 90%;
                        font-weight: 600;
                      "
                      formControlName="status"
                    >
                      <option
                        class="optionStyle colorCancel"
                        *ngFor="let status of statusTypes"
                        [ngValue]="status"
                      >
                        {{ status }}
                      </option>
                    </select>
                    <label for="dropdownInput" class="overlay"></label>
                  </div>
                </div>
                <div
                *ngIf="atmTransactionSelected?.status === 'waitingConfirm'">
                  <div
                    style="display: flex; justify-content: center"
                    class="dropdown">
                    <h1 style="margin-top: 10px">Waiting Confirm</h1>
                  </div>
                </div>
                <div
                *ngIf="atmTransactionSelected?.status === 'declined'">
                  <div
                    style="display: flex; justify-content: center"
                    class="dropdown">
                    <h1 style="margin-top: 10px">Declined</h1>
                  </div>
                </div>
              </div>
              <br />
              <br />
              <div style="border: 1px solid gray; border-radius: 15px">
                <p
                  style="
                    margin: -11px 0px 0 14px;
                    font-size: 16px;
                    font-weight: 600;
                    background: var(--app-bg);
                    width: fit-content;
                    padding: 0 13px;
                  "
                >
                  Description
                </p>
                <div style="display: flex; align-items: center">
                  <div class="reasonModalTextAreaWrapper">
                    <textarea
                      rows="5"
                      formControlName="detailDescription"
                      class="reasonModalInput"
                      type="text"
                      placeholder="Detail Description"
                    ></textarea>
                  </div>
                </div>
              </div>
            </form>
            <div
              style="display: flex; justify-content: end; align-items: center"
            >
              <div (click)="updateStatus()" class="saveButton">Save</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>

</div>
