import { Component, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-list-roles',
  templateUrl: './list-roles.component.html',
  styleUrls: ['./list-roles.component.css']
})
export class ListRolesComponent implements OnInit {
  constructor(
    private modalService: NgbModal,
  ) { }

  ngOnInit(): void {
  }
  openDelRoleModal(delRoleContent) {
    this.modalService.open(delRoleContent, { centered: true });
  }
  openAddRoleModal(addRoleContent) {
    this.modalService.open(addRoleContent, { centered: true });
  }
}
