import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'filter'
})
export class FilterPipe implements PipeTransform {

  transform(value: any[], args: any[]): any {

    let arg : any = args[0]
    let compare : any = args[1]

    if(JSON.stringify(compare).toLowerCase().includes('all')){
      return value
    }else{
      return value.filter((element) =>{
        return element[arg].includes(compare); 
      });
    }


  }

}
