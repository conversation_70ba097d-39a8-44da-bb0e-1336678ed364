import { Component, OnInit,  TemplateRef} from '@angular/core';
import { LoadJsService } from 'src/app/shared/services/load-js.service';
const PrimaryWhite = '#ffffff';
import { ngxLoadingAnimationTypes } from 'ngx-loading';
import { NavigationService } from 'src/app/shared/services/navigation.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-loading',
  templateUrl: './loading.component.html',
  styleUrls: ['./loading.component.css']
})
export class LoadingComponent implements OnInit {

  public primaryColour = PrimaryWhite;

  public loading = false;
  public ngxLoadingAnimationTypes = ngxLoadingAnimationTypes;
  public loadingTemplate!: TemplateRef<any>;
  
  variables:any = {}
  requestedVariables: any[] = [
    '_Frigoteknika_',
    '_Home_',
    '_Cancel_',
  ]
  lang = 'en'

  constructor(
    private activatedRoute: ActivatedRoute,
    public toastrService: ToastrService,
    public navigation: NavigationService,
    public loadJsService: LoadJsService) {
  }

  ngOnInit(): void {
    this.activatedRoute.params.subscribe( paramMap => {
      if(paramMap['lang']){
        this.lang = paramMap['lang'];
      }
    })
  }



  ngOnDestroy(){
    this.loading = false;
    this.loadJsService.removeScripts()
  }

}
