<div class="projects-section chat-section">
  <div
  style="
    display: flex;
    flex-direction: column;
    overflow: auto;
    flex-grow: 1;
    height: 95%;
    border-radius: 15px;
    overflow-x: hidden;
    gap: 1em;"
  class="">

  <app-search-side
  (selcetUser)="selectUserChatId($event)"
  (searchLoadingAction)="searchSideActivate=$event"
  [(me)]="me"></app-search-side>

    <div
    *ngIf="!searchSideActivate"
    infinite-scroll
    [scrollWindow]="false"
    [scrollWindow]="false"
    (scrolled)="onScrollDown($event)"
      style="
        background-color: var(--app-container);
        flex-grow: 1;
        border-radius: 15px;
        padding: 1em 1.2em;
        overflow-y: scroll;">
      <div
        style="display: flex; flex-direction: column; gap: 2em; height: 100%">
        <div
        *ngIf="rooms?.length == 0 && !searchMessageLoading">
          <div
            style="
              position: relative;
              display: flex;
              justify-content: center;
              align-items: center;
              flex-direction: column;
            "
          >
            <img
              style="width: 70%; max-width: 299px"
              src="/assets/icons/animatedIconsTable/send_a_message_infinite.svg"
              alt=""
            />
            <p style="font-size: 16px">List is empty</p>
          </div>
        </div>
        <ng-container *ngIf="searchMessageLoading">
          <li
          *ngFor="let i of [].constructor(10)"
          style="
              list-style: none; /* remove the bullets/dots from the list */
              display: flex;
              flex-direction: column;
              height: 85px;
              width: 100%;
          "
          >
          <div
              style="display: flex; gap: 5px; width: 100%; align-items: center"
          >
              <span
              class="skeleton1"
              style="height: 40px; width: 40px; border-radius: 50%"
              ></span>
              <div style="display: grid; gap: 4px">
              <span class="skeleton2" style="height: 14px; width: 85px"></span>
              <span class="skeleton2" style="height: 14px; width: 100px"></span>
              </div>
              <span
              class="skeleton2"
              style="
                  margin-left: auto;
                  height: 22px;
                  width: 63px;
                  border-radius: 5px;
              "
              ></span>
          </div>
          </li>
        </ng-container>
        <ng-container *ngIf="!searchMessageLoading">
            <li
            style="
                list-style: none; /* remove the bullets/dots from the list */
                display: flex;
                flex-direction: column;
                gap: 5px;
            "
            *ngFor="let room of rooms">
            <div
                (click)="selectUserChatId(room.anotherUser)"
                style="display: flex; gap: 8px; width: 100%; align-items: center">
                <div
                style="position: relative;">
                  <app-avatar-photo
                  *ngIf="room.anotherUser"
                  [buffer]="room.anotherUser?.photoProfile?.data"
                  [userId]="room.anotherUser?._id"
                  style="cursor: pointer"
                  [circleColor]="room?.anotherUser?.photoColor"
                  [name]="room?.anotherUser?.name"
                  [surname]="room?.anotherUser?.surname"
                  [class]="'userImgBloggers'"
                  [classAvatarInitials]="'initialsClass'"
                  ></app-avatar-photo>
                  <img
                  *ngIf="room?.anotherUser?.isVerified"
                    style="
                      height: 15px;
                      width: 15px;
                      position: absolute;
                      bottom: 0;
                      right: 0;"
                    src="/assets/icons/fiberVerified.svg"
                    alt=""
                  />
                  <img
                  *ngIf="room?.anotherUser?.untrusted"
                    style="
                      height: 15px;
                      width: 15px;
                      position: absolute;
                      bottom: 0;
                      right: 0;"
                    src="/assets/icons/fiberUnverified.svg"
                    alt=""
                  />
                </div>
                <div style="max-width: 177px">
                    <span>{{ room.anotherUser?.name }} {{ room.anotherUser?.surname }}</span>
                    <p 
                    [ngClass]="getUnReadTotal(room) !== 0 ? '' : 'seen'"
                    *ngIf="getLastUnReadText(room) !== ''"
                    style="
                    margin: 0;
                    font-size: 11px;
                    " 
                    class="no-wrap-1-line">
                        {{getLastUnReadText(room) | slice:0:32 }}
                        <ng-container *ngIf="getUnReadTotal(room) > 31">
                          ...
                        </ng-container>
                    </p>
                </div>
                <span
                style="
                    margin-left: auto;
                    border-radius: 5px;
                    font-weight: 500;
                    font-size: 9px;
                    text-align: end;
                    opacity: 0.7;
                ">{{getLastTextDate(room) | humanizeDate }}</span>
            </div>
            </li>
        </ng-container>
        <ng-container 
        *ngIf="moreLoading">
          <li
          *ngFor="let i of [].constructor(3)"
          style="
              list-style: none; /* remove the bullets/dots from the list */
              display: flex;
              flex-direction: column;
              height: 85px;
              width: 100%;
          "
          >
          <div
              style="display: flex; gap: 5px; width: 100%; align-items: center"
          >
              <span
              class="skeleton1"
              style="height: 40px; width: 40px; border-radius: 50%"
              ></span>
              <div style="display: grid; gap: 4px">
              <span class="skeleton2" style="height: 14px; width: 85px"></span>
              <span class="skeleton2" style="height: 14px; width: 100px"></span>
              </div>
              <span
              class="skeleton2"
              style="
                  margin-left: auto;
                  height: 22px;
                  width: 63px;
                  border-radius: 5px;
              "
              ></span>
          </div>
          </li>
        </ng-container>
      </div>
    </div>

  </div>

  <div
    *ngIf="!userChatId"
    class=""
    style="
      display: flex;
      flex-direction: column;
      overflow: hidden;
      flex-grow: 1;
      height: 95%;
      background-color: var(--app-container);
      border-radius: 15px;
      overflow-x: hidden;
    "
  >
   
      <div style="
      height: 100%;
      justify-content: center;
      align-items: center;
      display: flex;
      opacity: .8;
      flex-direction: column;
      gap: 2em;
      ">
        <img style="width: 50%;" src="/assets/icons/animatedIconsTable/chatemptydashboard.svg" alt="">
        <p>Select user and start a conversation</p>
      </div>
  </div>

  <app-user-chat
    style="
    display: flex;
    flex-direction: column;
    overflow: hidden;
    flex-grow: 1;
    height: 95%;
    background-color: var(--app-container);
    border-radius: 15px;
    overflow-x: hidden;"
  [(userChat)]="userChat"
  [(userChatId)]="userChatId"
  *ngIf="userChatId"
  ></app-user-chat>

  
</div>
