
p,
span,
div {
  color: white;
  font-size: 16px;
  font-weight: 600;
}
** full css dropdown interaction */
.dropdown {
  height: 59px;
}
.dropdown-content p {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  cursor: pointer;
  text-indent: 20px;
}
.dropdown-content p:hover {
  box-shadow: inset 0px 8px 16px 0px rgb(0 0 0 / 20%);
  /* border-radius: 15px; */
}
.dropdown-content {
  display: none;
}
.dropdown-content label {
  display: block;
  width: 100%;
  padding: 1rem 0;
}
input:checked ~ .dropdown .overlay {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  z-index: 190;
}
.optionStyle {
  font-size: 16px;
  text-align: center;
  width: 100%;
  background-color: var(--app-bg);
  line-height: 2;
  border: none;
  color: white;
  font-weight: 600;
  height: 40px;
}

@media screen and (max-width: 980px) {
    p,
    span,
    div {
      color: white;
      font-size: 14px;
      font-weight: 600;
    }
  }
  
  @media screen and (max-width: 520px) {
    p,
    span,
    div {
      color: white;
      font-size: 13px;
      font-weight: 600;
    }
  }
  
  @media screen and (max-height: 980px) {
    :root {
      --font-size-xxxl: 35px;
      --font-size-xxl: 25px;
      --font-size-xl: 25px;
      --font-size-l: 20px;
      --font-size-m: 15px;
      --font-size-s: 10px;
      --font-size-xs: 5px;
    }
    p,
    span,
    div {
      color: white;
      font-size: 13px;
      font-weight: 600;
    }
  }