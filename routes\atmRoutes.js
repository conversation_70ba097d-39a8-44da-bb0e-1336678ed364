const express = require("express");
const atmController = require("./../controllers/atmController");

const router = express.Router();

router.route("/").patch(atmController.updateStatus).post(atmController.create);

router.route("/:id").patch(atmController.updateStatus);

router
  .route("/getMyAtmTransactions/:id")
  .post(atmController.getMyAtmTransactions);

router.route("/getAll").post(atmController.getAll);

router.route("/updateStatus").post(atmController.updateStatus);

router.route("/searchATM").post(atmController.searchATM);

// router
// .route('/searchOnlinePayment')
// .post(onlinePaymentController.searchOnlinePayment);
module.exports = router;
