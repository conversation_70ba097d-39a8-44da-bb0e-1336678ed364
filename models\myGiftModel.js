const mongoose = require('mongoose');

const myGiftSchema = new mongoose.Schema(
  {
    gift: Number,
    title : {
      type: String,
      trim: true
    },
    cost: {
      type: Number,
      default: 0
    },
    src: {
      type: String,
      trim: true
    },
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Blog must been created from a User!']
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }

);
myGiftSchema.pre('save', async function( next) {

  await this.populate({
    path: 'user',
  }).execPopulate();;

  next();
});

myGiftSchema.pre(/^find/, function(next) {
  this.populate({
    path: 'user',
  });
  next();
})

const MyGift = mongoose.model('MyGift', myGiftSchema);

module.exports = MyGift;
