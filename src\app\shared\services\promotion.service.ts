import { ToastrService } from 'ngx-toastr';
import { User } from '../models/user';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, of, forkJoin } from 'rxjs';
import { take, map, tap, delay, switchMap } from 'rxjs/operators';
import { from, Observable } from 'rxjs';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';

const BACKEND_URL = environment.base_url + "/promotion/";

@Injectable({
  providedIn: 'root'
})
export class PromotionService {

  constructor(
    private http: HttpClient
  ) { }

  create(
    user,
    cost,
    specificUsersCount,
    allowedToShare,
    followersCountActive,
    followersCountMin,
    followersCountMax,
    followingCountActive,
    followingCountMin,
    followingCountMax,
    postCountActive,
    postCountMin,
    postCountMax,
    earnCountActive,
    earnCountMin,
    earnCountMax,
    timeAllowedToShareMinutes,
    minutesDiffernce,
    startPromoteDateTime,
    linkUrl,
    images:any[]
  ){
    console.log("images: ",images)
    let postData = {
      user: user,
      cost: cost,
      allowedToShare: allowedToShare,
      followersCountActive: followersCountActive,
      linkUrl: linkUrl,
      timeAllowedToShareMinutes: timeAllowedToShareMinutes,
      minutesDiffernce: minutesDiffernce,
      startPromoteDateTime: startPromoteDateTime,
      specificUsersCount: specificUsersCount,
      images : images,
      followingCountActive: followingCountActive,
      postCountActive:postCountActive,
      earnCountActive:earnCountActive
    }

    if(followersCountMin){
      postData['followersCountMin']=followersCountMin
    }
    if(followersCountMax){
      postData['followersCountMax']=followersCountMax
    }

    if(followingCountMin){
      postData['followingCountMin']=followingCountMin
    }
    if(followingCountMax){
      postData['followingCountMax']=followingCountMax
    }
    
    if(postCountMin){
      postData['postCountMin']=postCountMin
    }
    if(postCountMax){
      postData['postCountMax']=postCountMax
    }

    if(earnCountMin){
      postData['earnCountMin']=earnCountMin
    }
    if(earnCountMax){
      postData['earnCountMax']=earnCountMax
    }

    return  this.http.post<any>(BACKEND_URL , postData)
  }

  getAll(page,resultsPerPage){
    let data = {
      page : page,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL + '/getAll',data)
  }
  getAllTargetedUsers(id,page,resultsPerPage){
    let data = {
      page : page,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL + 'getAllTargetedUsers/'+ id,data)
  }


  getPromotionData(_id){
    // let data = {
    //   "user": user
    // }
    return this.http.get<any>(BACKEND_URL + 'getPromotionData/'+_id)
  }


  changeActiveStatus(
    _id,
    status
  ){  

    let data = {
      "_id": _id,
      "active": status
    }

    return  this.http.patch<any>( BACKEND_URL + 'changeActiveStatus/'+_id ,data)
  }


  changePauseStatus(
    _id,
    pause
  ){  

    let data = {
      "_id": _id,
      "pause": pause
    }

    return  this.http.patch<any>( BACKEND_URL + 'changePauseStatus/'+_id ,data)
  }


}
