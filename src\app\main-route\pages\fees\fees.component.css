/* First Screen */
.settingWrapper {
  display: grid;
  grid-template-columns: 300px 300px;
  gap: 2em;
}
@media screen and (max-width: 790px) {
  .settingWrapper {
    grid-template-columns: 1fr;
  }
}

/* Second Screen */
.settingItemsWrapper {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: fit-content;
  grid-gap: 2em;
  gap: 2em !important;
}
.settingsDetailsWrapper {
  grid-template-rows: 1fr 2fr 1fr;
  border-radius: 20px;
  overflow: hidden;
  gap: 1.5em;
  min-height: 518px;
}

@media screen and (max-width: 1137px) {
  .settingItemsWrapper {
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 1137px) {
  .showingInfo {
    display: none;
  }
  .settings-details-section {
    grid-template-columns: 1fr !important;
    overflow: scroll;
  }
  .settingsDetailsWrapper {
    grid-template-rows: 1fr 2fr 1fr;
    border-radius: 20px;
    overflow: hidden;
    gap: 1.5em;
    min-height: 751px;
  }
  .headerDetails {
    display: none !important;
  }
  .headerDetails1 {
    display: flex !important;
    max-height: 80px;
  }
  .settingsDetailsWrapper {
    justify-content: center;
    align-items: center;
  }
}
@media screen and (min-width: 1137px) {
  .headerDetails1 {
    display: none !important;
  }
  .headerDetails {
    display: flex !important;
    max-height: 80px;
  }
}

.fee-amount-input {
  border: 1px solid var(--link-color-hover);
  flex: 1;
  outline: none;
  height: 100%;
  padding: 10px 20px;
  font-size: 16px;
  background-color: var(--search-area-bg);
  color: var(--main-color);
  width: 100%;
}
.fee-amount-input:placeholder {
  color: var(--main-color);
  opacity: 0.6;
}

.settings-details-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  padding: 32px !important;
  gap: 2em;
}
.optionStyle {
  font-size: 16px;
  text-align: center;
  width: 100%;
  background-color: var(--app-bg);
  line-height: 2;
  border: none;
  color: white;
  font-weight: 600;
  height: 40px;
}
p,
span,
div {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

/* new */

.switch {
  position: relative;
  width: 210px;
  height: 40px;
  text-align: center;
  background: var(--app-container);
  transition: all 0.2s ease;
  border-radius: 25px;
}
.switch span {
  position: absolute;
  width: 20px;
  height: 4px;
  top: 50%;
  left: 50%;
  margin: -2px 0px 0px -4px;
  background: #fff;
  display: block;
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
  transition: all 0.2s ease;
  border-radius: 2px;
}
.switch span:after {
  content: "";
  display: block;
  position: absolute;
  width: 4px;
  height: 12px;
  margin-top: -8px;
  background: #fff;
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
  transition: all 0.2s ease;
  border-radius: 2px;
}
input[type=radio] {
  display: none;
}
.switch label {
  cursor: pointer;
  color: rgba(255, 255, 255, 0.5);
  width: 90px;
  line-height: 40px;
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
  transition: all 0.2s ease;
}
label[for=yes] {
  position: absolute;
  left: 0px;
  height: 20px;
}
label[for=no] {
  position: absolute;
  right: 0px;
}
#no:checked ~ .switch {
  background:var(--app-content-secondary-color);
}
#no:checked ~ .switch span {
  background: #fff;
  margin-left: -8px;
}
#no:checked ~ .switch span:after {
  background: #fff;
  height: 20px;
  margin-top: -8px;
  margin-left: 8px;
}
#yes:checked ~ .switch label[for=yes] {
  color: #fff;
}
#no:checked ~ .switch label[for=no] {
  color: #fff;
}