.no-bullets {
  list-style-type: none;
}
.optionStyle {
  font-size: 16px;
  text-align: center;
  max-width: 100px;
  background-color: var(--app-bg);
  line-height: 2;
  border: none;
  color: white;
  font-weight: 600;
  height: 40px;
}

.modalButtonsCancel {
  /* background-color: var(--action-color); */
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--action-color);
  line-height: 2.5;
  border-radius: 10px;
  cursor: pointer;
}
.modalButtonsDeactivate {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--action-color-50);
  line-height: 2.5;
  border-radius: 10px;
  background-color: var(--action-color);
  cursor: pointer;
}
.modalButtonsCancel:hover {
  background-color: var(--action-color-30);

}
.modalButtonsDeactivate:hover {
  background-color: var(--action-color-50);

}
p,
span,
div {
  color: white;
  font-size: 16px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}