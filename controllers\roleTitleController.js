const catchAsync = require('./../utils/catchAsync');

const RoleTitle = require('./../models/roleTitleModel');


exports.create = catchAsync(async (req, res, next) => {

    console.log("req.body: ",req.body)
      let data = await RoleTitle.create(req.body);   
      if(data !== null){
        res.status(201).json({
          status: 'success',
          data: data
         });
      }
});


exports.getAll = catchAsync(async (req, res, next) => {
    
    let data = 
    await  RoleTitle.find({})    
    .sort({ _id: 1 })

    res.status(200).json({
      status: 'success',
      data: {
        data: data
      }
    });
  
  });