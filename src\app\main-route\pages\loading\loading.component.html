<div class="page-wrapper">
        <div class="page-main-header host noselect">
          <div class="nav-right col" >
            <div class="tab-group pull-left">
              <a [routerLink]="['/'+lang +'/home']" class="tab">
                  <button type="button" class="btn btn-primary d-block"> Fiber Al Home </button>
              </a>
            </div>
            <div class="tab-group pull-right">
              <a (click)="navigation.goBack()" class="tab">
                  <button type="button" class="btn btn-primary d-block">Cancel</button>
              </a>
            </div>
          </div>
        </div>

        <div class="page-body-wrapper">


            <div class="page-body">
                <ngx-loading
                  [show]="true"
                  [config]="{
                    animationType: ngxLoadingAnimationTypes.circleSwish,
                    primaryColour: primaryColour,
                    backdropBorderRadius: '3px'
                  }"
                  [template]="loadingTemplate"
                  ></ngx-loading>
            </div>
        
        </div>
</div>
