import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';

const BACKEND_URL = environment.base_url + "/blogger/";

@Injectable({
  providedIn: 'root'
})
export class BloggerService {

  constructor(
    private http: HttpClient
  ) { } 

  
  create(
    data
  ){
    return  this.http.post<any>( BACKEND_URL, {data:data})
  }

  getAll(page,resultsPerPage){
    let data = {
      page : page,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL+'getAll',data)
  }

  getAllPostUserId(page,resultsPerPage,userId){
    let data = {
      userId:userId,
      page : page,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL+'getAllPostUserId',data)
  }
  

  findByIdAndDelete(
    userId
  ){
    return  this.http.post<any>( BACKEND_URL+'findByIdAndDelete', {userId:userId})
  }

  search(searchText,_id,pageNumber,resultsPerPage){
    let data = {
      searchText:searchText,
      userId: _id,
      page : pageNumber,
      resultsPerPage: resultsPerPage,
    }
    return this.http.post<any>(BACKEND_URL + 'search',data)
  }
  
}
