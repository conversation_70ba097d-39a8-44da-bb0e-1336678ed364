const mongoose = require("mongoose");

const postSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      trim: true,
    },
    comment: {
      type: String,
      trim: true,
    },
    linkUrl: {
      type: String,
      trim: true,
    },
    isPayPerView: {
      type: Boolean,
      default: true,
    },
    isStatusContent: {
      type: Boolean,
      default: false,
    },
    isImageContent: {
      type: Boolean,
      default: false,
    },
    isVideoContent: {
      type: Boolean,
      default: false,
      index: true,
    },
    newVideoId: {
      type: String,
      trim: true,
    },
    videoId: {
      type: String,
      trim: true,
    },
    oldVideoId: {
      type: String,
      trim: true,
    },
    isShareLink: {
      type: Boolean,
      default: false,
    },
    isPromotion: {
      type: Boolean,
      default: false,
    },
    promotion: {
      type: mongoose.Schema.ObjectId,
      ref: "Promotion",
    },
    backgroundImages: [
      {
        size: Number,
        name: String,
        data: Buffer,
        contentType: String,
      },
    ],
    images: [
      {
        size: Number,
        name: String,
        data: Buffer,
        contentType: String,
      },
    ],
    blurImages: [
      {
        size: Number,
        name: String,
        data: Buffer,
        contentType: String,
      },
    ],
    user: {
      type: mongoose.Schema.ObjectId,
      ref: "User",
      required: [true, "Post must been created from a User!"],
    },
    isEditedCropImage: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    fontSize: {
      type: Number,
      default: 18,
    },
    fontColor: {
      type: String,
      trim: true,
    },
    likesCount: {
      type: Number,
      default: 0,
    },
    commentsCount: {
      type: Number,
      default: 0,
    },
    giftsCount: {
      type: Number,
      default: 0,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
      index: true,
    },
    // ktau i shtojm ni entitet te rij per ne Desktop App
    isVerification: {
      type: Boolean,
      default: false,
    },
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);
postSchema.index({ updatedAt: 1 });
postSchema.index({ isVideoContent: 1 });

postSchema.pre("save", async function (next) {
  await this.populate({
    path: "user",
    select: [
      "name",
      "surname",
      "isVerified",
      "photoColor",
      "untrusted",
      "isVerified",
    ],
  }).execPopulate();

  next();
});

postSchema.pre(/^find/, function (next) {
  // this.populate({
  //   path: 'user',
  //   select: ['name','surname','isVerified','photoColor','untrusted','isVerified']
  // });
  // this.populate({
  //   path: 'promotion',
  //   select: ['linkUrl','title']
  // });
  next();
});

const Post = mongoose.model("Post", postSchema);

module.exports = Post;
