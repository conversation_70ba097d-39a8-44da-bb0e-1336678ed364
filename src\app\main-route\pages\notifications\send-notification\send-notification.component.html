<div style="overflow: auto;position: relative;" class="projects-section">
  <div style="gap: 1em" class="projects-send-notification">
    <div
      style="
        display: flex;
        flex-direction: column;
        overflow: auto;
        flex-grow: 1;
        height: 95%;
        border-radius: 15px;
        overflow-x: hidden;
        gap: 1em;
      "
    >
      <div
        (click)="openUserSearchModal(searchContent)"
        class="search-bar-send-notification"
      >
        <span>Search and add user</span>
      </div>

      <div style="flex-grow: 1; border-radius: 15px; overflow-y: auto">
        <div style="background: var(--sidebar); border-radius: 14px">
          <div class="pill-users-wrapper">
            <div *ngFor="let user of allUsers" class="pill-users">
              <app-avatar-photo
                [userId]="user?._id"
                [circleColor]="user?.photoColor"
                [name]="user?.name"
                [surname]="user?.surname"
                [class]="'userImgBloggers'"
                [classAvatarInitials]="'initialsClass'"
              ></app-avatar-photo>
              <span class="no-wrap-1-line"
                >{{ user?.name }} {{ user?.surname }}</span
              >
              <img
                (click)="removeUser(user)"
                style="
                  position: absolute;
                  top: 4px;
                  right: 4px;
                  height: 10px;
                  width: 10px;
                  scale: 1.2;
                "
                src="/assets/icons/close.svg"
                alt=""
              />
            </div>
          </div>
        </div>
        <div
          style="
            background: var(--sidebar);
            border-radius: 14px;
            margin-top: 1em;
            padding: 1rem 0.7rem;
            display: flex;
            flex-direction: column;
            gap: 1em;
          "
        >
          <div class="input-send-notification">
            <input
              [(ngModel)]="nativeTitle"
              type="text"
              placeholder="Write Native Title..."
            />
          </div>
        </div>
        <div
          style="
            background: var(--sidebar);
            border-radius: 14px;
            margin-top: 1em;
            padding: 1rem 0.7rem;
            display: flex;
            width: 100%;
            gap: 1em;
            align-items: center;
          "
        >
          <app-avatar-photo
            [userId]="'630a2e5d4989aae851a657e4'"
            [circleColor]="me?.photoColor"
            [name]="'Fiber'"
            [surname]="'Al'"
            [class]="'userImgBloggers'"
            [classAvatarInitials]="'initialsClass'"
          ></app-avatar-photo>
          <div class="input-send-notification">
            <input 
            [(ngModel)]="socketTitle"
            type="text" placeholder="Write Description.." />
          </div>
          <div
            *ngIf="!thumbnail"
            class="notification-sent-content-image-before"
            (click)="fileInputThumbnail.click()"
          ></div>
          <div *ngIf="thumbnail" style="position: relative;">
            <img
              (click)="
              thumbnailFile = null;
              thumbnail=null"
              style="
                position: absolute;
                top: -4px;
                right: -4px;
                height: 10px;
                width: 10px;
                scale: 1.2;
              "
              src="/assets/icons/close.svg"
              alt=""
            />
            <img
              *ngIf="thumbnail"
              [src]="thumbnail"
              class="notification-sent-content-image"
              alt=""
            />
          </div>
        </div>
        <div
        style="
          background: var(--sidebar);
          border-radius: 14px;
          margin-top: 1em;
          padding: 1rem 0.7rem;
          display: flex;
          flex-direction: column;
          width: 100%;
          gap: 1em;
         justify-content: center;
        "
      >
      <div style="display: flex;justify-content: space-between;align-items: center;padding:0 1em;">
        <span>Add direct Link</span>
        <p style="margin: 0 0 0 auto">
          <mat-checkbox 
          (change)="checkboxChange($event)"
          color="dark">
            <span 
            *ngIf="!addDirectLink" 
            style="opacity: 0.7">
              Enable</span>
            <span 
            *ngIf="addDirectLink"
            style="opacity: 0.7">
              Disable</span>
          </mat-checkbox>
        </p>
      </div>
        <div 
        *ngIf="addDirectLink"
        class="input-send-notification">
          <input 
          [(ngModel)]="directLink"
          type="text" placeholder="Write direct link" />
        </div>
      
     
        </div>
        <div
        *ngIf="!addDirectLink" 
          style="
            background: var(--sidebar);
            border-radius: 14px;
            margin-top: 1em;
            padding: 1rem 0.7rem;
            display: flex;
            flex-direction: column;
            gap: 1em;
          "
        >
          <div class="input-send-notificationTA">
            <textarea
              rows="3"
              [(ngModel)]="description"
              type="text"
              placeholder="Write Description..."
            >
            </textarea>
          </div>
        </div>


        <input
          class="input-image"
          #fileInputThumbnail
          accept="image/*"
          type="file"
          (change)="selectThumbnail($event)"
        />
      </div>
    </div>
    <div
      style="
        display: flex;
        border-radius: 20px;
        overflow: hidden;
        gap: 1.5em;
        overflow: auto;
        aspect-ratio: 15 / 10;
      "
    >
      <!-- first screen mobile -->
      <div
        style="
          background-image: url(assets/iphoneWP.svg);
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          aspect-ratio: 7/18;
        "
      >
        <div
          style="
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 93%;
          "
        >
          <div
            style="
              height: 88%;
              width: 100%;
              margin: 20px 0 0 0;
              border-radius: 0 0 35px 35px;
              display: flex;
              flex-direction: column;
              gap: 1em;
              align-items: center;
              max-height: 670px;
            "
          >
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0 6%;
                gap: 5px;
                margin-top: 5px;
                width: 100%;
              "
            >
              <div
                style="
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  flex-direction: column;
                "
              >
                <p style="font-size: 42px; line-height: 1; margin: 1em 0 0 0">
                  {{ today | date : "h:mm" }}
                </p>
                <p style="font-size: 12px">
                  {{ today | date : "EEEE,d MMMM " }}
                </p>
              </div>
            </div>
            <div
              style="
                width: 100%;
                padding: 0 6%;
                display: flex;
                flex-direction: column;
                overflow: auto;
                height: 100%;
              "
            >
              <div
                style="
                  background: #ffffff88;
                  border-radius: 14px;
                  margin-top: 1em;
                  padding: 8px;
                  display: flex;
                  width: 100%;
                  gap: 4px;
                  align-items: center;
                  box-shadow: 0 2px 6px 0 rgba(8, 8, 22, 0.2),
                    0 24px 20px -24px rgba(71, 82, 107, 0.1);
                "
              >
                <app-avatar-photo
                  [userId]="'630a2e5d4989aae851a657e4'"
                  [circleColor]="me?.photoColor"
                  [name]="'Fiber'"
                  [surname]="'Al'"
                  [class]="'notification-sent-content-image-preview'"
                  [classAvatarInitials]="'initialsClass'"
                ></app-avatar-photo>
                <div class="disp-grid">
                  <span
                    class=""
                    style="
                      font-size: 11px;
                      color: black;
                      font-weight: 700;
                      line-height: 1;
                    "
                    >Fiber Al
                  </span>
                  <span
                    class=""
                    style="
                      font-size: 10px;
                      color: black;
                      font-weight: 600;
                      opacity: 0.9;
                      line-height: 1;
                    "
                  >
                    {{ nativeTitle }}
                  </span>
                </div>

                <span
                  class="m-l-auto"
                  style="
                    font-size: 9px;
                    opacity: 0.6;
                    height: 100%;
                    line-height: 1;
                    color: black;
                  "
                  >now
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- second screen mobile -->
      <div
        style="
          background-image: url(assets/iphone.svg);
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          aspect-ratio: 7/18;
        "
      >
        <div
          style="
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 93%;
          "
        >
          <div
            style="
              height: 88%;
              width: 100%;
              margin: 20px 0 0 0;
              border-radius: 0 0 35px 35px;
              display: flex;
              flex-direction: column;
              gap: 1em;
              align-items: center;
              max-height: 670px;
            "
          >
            <div
              style="
                display: flex;
                align-items: center;
                padding: 0 6%;
                gap: 5px;
                margin-top: 5px;
                width: 100%;
              "
            >
              <img
                style="height: 20px; width: 20px; transform: rotate(180deg)"
                src="/assets/icons/next.svg"
                alt=""
              />
            </div>
            <div
              style="
                width: 100%;
                padding: 0 6%;
                display: flex;
                flex-direction: column;
                overflow: auto;
                height: 100%;
              "
            >
              <div
                style="
                  background: var(--bs-gray-900);
                  border-radius: 14px;
                  margin-top: 1em;
                  padding: 8px;
                  display: flex;
                  width: 100%;
                  gap: 4px;
                  align-items: center;
                "
              >
                <app-avatar-photo
                  [userId]="'630a2e5d4989aae851a657e4'"
                  [circleColor]="me?.photoColor"
                  [name]="'Fiber'"
                  [surname]="'Al'"
                  [class]="'notification-sent-content-image-preview'"
                  [classAvatarInitials]="'initialsClass'"
                ></app-avatar-photo>
                <span class="" style="font-size: 11px">
                  {{ socketTitle }}
                </span>
                <span
                  class=""
                  style="font-size: 9px; opacity: 0.6; margin-left: auto"
                  >now
                </span>

                <span 
                *ngIf="!thumbnail"
                style=""  
                class="notification-sent-content-image-preview skeleton1">
                </span>
                
                <span 
                *ngIf="thumbnail" 
                class="notification-sent-content-image-preview">
                  <img
                  [src]="thumbnail"
                  class="notification-sent-content-image-preview"
                  alt=""
                />
                </span>

              </div>  
              <div
                *ngFor="let i of [].constructor(5)"
                style="
                  width: 100%;
                  height: 49px;
                  opacity: 0.6;
                  display: flex;
                  align-items: center;
                  padding: 0 5px;
                  gap: 4px;
                  margin-top: 2px;
                "
                class="skeleton1"
              >
                <div
                  class="notification-sent-sender-image-preview skeleton2"
                ></div>
                <div style="flex-grow: 1; height: 10px" class="skeleton2"></div>
                <div
                  class="notification-sent-content-image-preview skeleton2"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- third screen mobile -->
      <div
        [ngStyle]="{'opacity': !addDirectLink ? '' : '0' }"
        style="
          opacity: 0;
          background-image: url(assets/iphone.svg);
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          aspect-ratio: 7/18;
        "
      >
        <div
          style="
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 93%;
          "
        >
          <div
            style="
              height: 88%;
              width: 100%;
              margin: 20px 0 0 0;
              border-radius: 0 0 35px 35px;
              display: flex;
              flex-direction: column;
              gap: 1em;
              align-items: center;
              max-height: 670px;
            "
          >
            <div
              style="
                display: flex;
                align-items: center;
                padding: 0 6%;
                gap: 5px;
                margin-top: 5px;
                width: 100%;
              "
            >
              <img
                style="height: 20px; width: 20px; transform: rotate(180deg)"
                src="/assets/icons/next.svg"
                alt=""
              />
              <span style="text-align: center; width: 80%"
                >Notification Detail</span
              >
            </div>
            <div
              style="
                width: 100%;
                padding: 0 6%;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                gap: 1em;
              "
            >
              <div
                style="
                  width: 100%;
                  height: 100%;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  gap: 1em;
                "
              >
                <div style="position: relative">
                  <span 
                  *ngIf="!thumbnail" 
                  style="width: 100%;display: flex; object-fit: cover; aspect-ratio: 16/9" 
                  class="skeleton1">
                  <img
                  style="width: 100%; object-fit: cover; aspect-ratio: 16/9;opacity: .0;"
                  src="https://images.unsplash.com/photo-1497445462247-4330a224fdb1?w=500&h=500&fit=crop"
                  class=""
                  alt=""/>
                  </span>

                  <span 
                  *ngIf="thumbnail" 
                  style="width: 100%;display: flex; object-fit: cover; aspect-ratio: 16/9">
                    <img
                    style="width: 100%; object-fit: cover; aspect-ratio: 16/9"
                    [src]="thumbnail"
                    class=""
                    alt=""/>
                  </span>

                  <div
                    style="
                      position: absolute;
                      background: hsla(0, 0%, 0%, 0.103);
                      width: 100%;
                      height: 4em;
                      padding: 3px 12px;
                      overflow: hidden;
                      background-image: linear-gradient(
                        360deg,
                        black,
                        rgba(35, 35, 0, 0)
                      );
                      display: flex;
                      justify-content: space-evenly;
                      align-items: center;
                      bottom: 0;
                      z-index: 6;
                    "
                  ></div>
                </div>

                <div
                  style="
                    align-self: start;
                    margin: 0 10px;
                    width: 90%;
                    display: flex;
                    gap: 0.4rem;
                    align-items: center;
                  "
                >
                  <app-avatar-photo
                    [userId]="'630a2e5d4989aae851a657e4'"
                    [circleColor]="me?.photoColor"
                    [name]="'Fiber'"
                    [surname]="'Al'"
                    [class]="'notification-sent-content-image-preview'"
                    [classAvatarInitials]="'initialsClass'"
                  ></app-avatar-photo>
                  <div style="display: flex; flex-direction: column">
                    <span style="font-size: 15px; font-weight: 700"
                      >Fiber Al</span
                    >
                    <span
                      style="font-weight: 600; opacity: 0.7; font-size: 10px"
                      >{{ today | date : "MMM d, h:mm a" }}</span
                    >
                  </div>
                </div>
                <div
                  style="
                    display: flex;
                    justify-content: center;
                    flex-direction: column;
                    width: 90%;
                  "
                >
                  <span style="font-size: 20px; font-weight: 700;width: 100%;
                  line-break: anywhere;">{{
                    socketTitle
                  }}</span>
                  <span style="font-size: 13px;
                  font-weight: 500;
                  width: 100%;
                  line-break: anywhere;">
                    {{ description }}
                  </span>
                </div>
                <div
                  *ngFor="let i of [].constructor(0)"
                  style="
                    width: 100%;
                    height: 49px;
                    opacity: 0.6;
                    display: flex;
                    align-items: center;
                    padding: 0 5px;
                    gap: 4px;
                    margin-top: 2px;
                  "
                  class="skeleton1"
                >
                  <div
                    class="notification-sent-sender-image-preview skeleton2"
                  ></div>
                  <div
                    style="flex-grow: 1; height: 10px"
                    class="skeleton2"
                  ></div>
                  <div
                    class="notification-sent-content-image-preview skeleton2"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div style="display: flex; gap: 1em">
    <button
    (click)="redirectTo('list-notifications')">Back</button>
    <span 
    (click)="submit()"
    class="buttonReset"> Save </span>
  </div>
  <div
  *ngIf="loading"
  style="
    position: absolute;
    background-color: rgba(0, 0, 0, 0.226);
    background-image: url(/assets/blur.svg);
    background-size: cover;
    background-repeat: repeat;
    height: 100%;
    width: 100%;
    margin-left: -32px;
    margin-top: -32px;
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 20px;
  ">
      <img
        style="width: 80%; max-width: 361px"
        src="/assets/animatedIcons/phone_final.svg"
        alt=""
      />
  </div>
</div>
<ng-template #searchContent let-modal>
  <app-search-content
    [(me)]="me"
    (closeModal)="modal.dismiss('Cross click')"
    (userSelected)="selectUser($event)"
  ></app-search-content>
</ng-template>
