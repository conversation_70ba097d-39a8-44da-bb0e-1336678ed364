const express = require("express");
const bloggerController = require("./../controllers/bloggerController");

const router = express.Router();

router.route("/").post(bloggerController.create);

router.route("/getAll").post(bloggerController.getAll);

router.route("/getAllPostUserId").post(bloggerController.getAllPostUserId);

router.route("/findByIdAndDelete").post(bloggerController.findByIdAndDelete);

router.route("/search").post(bloggerController.search);

module.exports = router;
