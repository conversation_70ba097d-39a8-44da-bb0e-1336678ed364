const mongoose = require("mongoose");

const promotionSchema = new mongoose.Schema({
  linkUrl: {
    type: String,
    trim: true,
  },
  cost: {
    type: Number,
    default: 0,
  },
  allowedToShare: {
    type: Number,
    default: 0,
  },
  sharedCount: {
    type: Number,
    default: 0,
  },
  iPromotionNotificationSendCount: {
    type: Number,
    default: 0,
  },
  nativeNotificationSendCount: {
    type: Number,
    default: 0,
  },
  nativeMissedNotificationSendCount: {
    type: Number,
    default: 0,
  },
  followersCountActive: {
    type: Boolean,
    default: false,
  },
  followersCountMin: {
    type: Number,
  },
  followersCountMax: {
    type: Number,
  },
  followingCountActive: {
    type: Boolean,
    default: false,
  },
  followingCountMin: {
    type: Number,
  },
  followingCountMax: {
    type: Number,
  },
  postCountActive: {
    type: Boolean,
    default: false,
  },
  postCountMin: {
    type: Number,
  },
  postCountMax: {
    type: Number,
  },
  earnCountActive: {
    type: Boolean,
    default: false,
  },
  earnCountMin: {
    type: Number,
  },
  earnCountMax: {
    type: Number,
  },
  timeAllowedToShareMinutes: {
    type: Number,
    default: 5,
  },
  minutesDiffernce: {
    type: Number,
    default: 10,
  },
  startPromoteDateTime: {
    type: Date,
    default: Date.now,
  },
  images: [
    {
      size: Number,
      name: String,
      data: Buffer,
      contentType: String,
    },
  ],
  userIsAllowedToShare: {
    type: String,
    enum: ["once", "daily", "weekly", "monthly"],
    default: "once",
  },
  specificUsersCount: {
    type: Number,
    default: 0,
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: "User",
    required: [true, "Promotion must been created from a User!"],
  },
  pause: {
    type: Boolean,
    default: false,
  },
  active: {
    type: Boolean,
    default: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

promotionSchema.pre("save", async function (next) {
  // await this.populate({
  // path: 'user',
  // }).execPopulate();
  next();
});

promotionSchema.pre(/^find/, function (next) {
  this.populate({
    path: "user",
    select: [
      "id",
      "_id",
      "name",
      "surname",
      "isVerified",
      "photoColor",
      "untrusted",
      "active",
    ],
  });
  next();
});

const Promotion = mongoose.model("Promotion", promotionSchema);

module.exports = Promotion;
