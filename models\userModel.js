const crypto = require('crypto');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
// required: [true, 'Please tell us your surname!']
const { MongooseFindByReference } = require('mongoose-find-by-reference');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    trim: true,
    required: [true, 'Please tell us your name!']
  },
  surname: {
    type: String,
    trim: true
  },
  bio: {
    type: String,
    trim: true,
    default: ''
  },
  email: {
    type: String,
    required: [true, 'Please provide your email'],
    unique: true,
    lowercase: true,
  },
  coins:{
    type: Number,
    default: 0
  },
  followersCount:{
    type: Number,
    default: 0
  },
  followingCount:{
    type: Number,
    default: 0
  },
  postCount:{
    type: Number,
    default: 0
  },
  commentsCount:{
    type: Number,
    default: 0
  },
  likesCount:{
    type: Number,
    default: 0
  },
  earnCount:{
    type: Number,
    default: 0
  },
  photoProfile:{
    size: Number,
    name: String,
    data: Buffer,
    contentType: String
  },
  photoCover:{
    size: Number,
    name: String,
    data: Buffer,
    contentType: String
  },
  photoColor: {
    type: String
  },
  role: {
    type: String,
    enum: ['client', 'admin'],
    default: 'client'
  },
  password: {
    type: String,
    required: [true, 'Please provide a password'],
    minlength: 8,
    select: false
  },
  passwordConfirm: {
    type: String,
    required: [true, 'Please confirm your password']
  },
  passwordChangedAt: Date,
  passwordResetToken: String,
  passwordResetExpires: Date,
  isOnline: {
    type: Boolean,
    default: false
  },
  active: {
    type: Boolean,
    default: true
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  untrusted: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  lastStoryCreated: {
    type: Date,
    default: Date.now
  },
  lastStory:{
    type: mongoose.ObjectId,
    ref: 'Story'
  }, 
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

userSchema.pre('save', async function (next) {
  const n = this.name.split(' ')
  this.name = n
    .map((w) => w.charAt(0).toUpperCase() + w.slice(1).toLowerCase())
    .join(' ')

  const s = this.surname.split(' ')
  this.surname = s
    .map((w) => w.charAt(0).toUpperCase() + w.slice(1).toLowerCase())
    .join(' ')

  next()
})

userSchema.pre('save', async function(next) {
  // Only run this function if password was actually modified
  if (!this.isModified('password')) return next();

  // Hash the password with cost of 12
  this.password = await bcrypt.hash(this.password, 12);
  console.log("save")
  // if(this.photoProfile.data){
  //   this.photoProfile.data =   await Buffer.from(this.photoProfile.data, 'base64').toString('base64')
  // }
  // if(this.photoCover.data){
  //   this.photoCover.data =   await Buffer.from(this.photoCover.data, 'base64').toString('base64')
  // }

  // Delete passwordConfirm field
  this.passwordConfirm = undefined;
  next();
});

userSchema.pre('save', function(next) {
  if (!this.isModified('password') || this.isNew) return next();

  this.passwordChangedAt = Date.now() - 1000;
  next();
});

userSchema.pre(/^find/, async function(next) {


  next();
});

userSchema.methods.correctPassword = async function(
  candidatePassword,
  userPassword
) {
  return await bcrypt.compare(candidatePassword, userPassword);
};

userSchema.methods.changedPasswordAfter = function(JWTTimestamp) {
  if (this.passwordChangedAt) {
    const changedTimestamp = parseInt(
      this.passwordChangedAt.getTime() / 1000,
      10
    );

    return JWTTimestamp < changedTimestamp;
  }

  // False means NOT changed
  return false;
};

userSchema.methods.createPasswordResetToken = function() {
  const resetToken = crypto.randomBytes(32).toString('hex');

  this.passwordResetToken = crypto
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');

  // console.log({ resetToken }, this.passwordResetToken);

  this.passwordResetExpires = Date.now() + 10 * 60 * 1000;

  return resetToken;
};

userSchema.plugin(MongooseFindByReference);

const User = mongoose.model('User', userSchema);

module.exports = User;