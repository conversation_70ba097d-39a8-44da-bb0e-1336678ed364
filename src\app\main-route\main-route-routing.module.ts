import { SendPromotionComponent } from './pages/send-promotion/send-promotion.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { IsAuthenticatedGuard } from '../shared/guards/is-authenticated.guard';

import { MainRouteComponent } from './main-route.component';
import { ListOfAppVersionsComponent } from './pages/app-version/list-of-app-versions/list-of-app-versions.component';
import { NewAppVersionComponent } from './pages/app-version/new-app-version/new-app-version.component';
import { AddNewBankTransactionComponent } from './pages/central-bank/add-new-bank-transaction/add-new-bank-transaction.component';
import { ListOfCentralBankTransactionsComponent } from './pages/central-bank/list-of-central-bank-transactions/list-of-central-bank-transactions.component';
import { HomeComponent } from './pages/home/<USER>';
import { LoadingComponent } from './pages/loading/loading.component';
import { ListOfMainDataComponent } from './pages/main-data/list-of-main-data/list-of-main-data.component';
import { NewMainDataComponent } from './pages/main-data/new-main-data/new-main-data.component';
import { ProfileComponent } from './pages/profile/profile.component';
import { CreatePromotionComponent } from './pages/promotions/create-promotion/create-promotion.component';
import { EditPromotionComponent } from './pages/promotions/edit-promotion/edit-promotion.component';
import { ListOfPromotionsComponent } from './pages/promotions/list-of-promotions/list-of-promotions.component';
import { PormotionDetailsComponent } from './pages/promotions/pormotion-details/pormotion-details.component';
import { TargetedUsersListComponent } from './pages/promotions/targeted-users-list/targeted-users-list.component';
import { ListOfTransactionsComponent } from './pages/transaction/list-of-transactions/list-of-transactions.component';
import { ListOfExchangeComponent } from './pages/exchange/list-of-exchange/list-of-exchange.component';
import { ListOfOnlinePaymentComponent } from './pages/online-payment/list-of-online-payment/list-of-online-payment.component';
import { ListOfTranscationsOfMinistryOfFinanceComponent } from './pages/ministry-of-finance/list-of-transcations-of-ministry-of-finance/list-of-transcations-of-ministry-of-finance.component';
import { ListOfTranscationsOfAtmComponent } from './pages/atm/list-of-transcations-of-atm/list-of-transcations-of-atm.component';
import { SettingsComponent } from './pages/settings/settings.component';
import { FeesComponent } from './pages/fees/fees.component';
import { BloggersComponent } from './pages/bloggers/bloggers.component';
import { SearchListShownComponent } from './pages/search-list-shown/search-list-shown.component';
import { ChatComponent } from './pages/chat/chat.component';
import { UsersComponent } from './pages/users/users.component';
import { SendNotificationComponent } from './pages/notifications/send-notification/send-notification.component';
import { ListPromotionsComponent } from './pages/list-promotions/list-promotions.component';
import { ListNotificationComponent } from './pages/notifications/list-notification/list-notification.component';
import { ListGiftsComponent } from './pages/gifts/list-gifts/list-gifts.component';
import { CreateGiftComponent } from './pages/gifts/create-gift/create-gift.component';
import { ListOfBannedSuggestedUsersComponent } from './pages/bannedSuggestedUsers/list-of-banned-suggested-users/list-of-banned-suggested-users.component';
import { ListRolesComponent } from './pages/list-roles/list-roles.component';
import { PermissionsComponent } from './pages/permissions/permissions.component';
import { ListVersionComponent } from './pages/versions/list-version/list-version.component';
import { AddVersionComponent } from './pages/versions/add-version/add-version.component';
import { SuggestedComponent } from './pages/suggested/suggested.component';

const routes: Routes = [
  {
    canLoad: [IsAuthenticatedGuard],
    path: '',
    component: MainRouteComponent,
    children: [
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/home',
        component: HomeComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/profile',
        component: ProfileComponent,
      },
      // {
      //   path: ':lang/auth/:action',
      //   component: AuthComponent,
      // },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/edit-promotion/:promotionId',
        component: EditPromotionComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/promotion-details/:promotionId',
        component: PormotionDetailsComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/targeted-users-list/:promotionId',
        component: TargetedUsersListComponent,
      },

      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/list-of-promotions',
        component: ListOfPromotionsComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/new-promotion',
        component: CreatePromotionComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/list-of-app-versions',
        component: ListOfAppVersionsComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/new-app-version',
        component: NewAppVersionComponent,
      },

      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/new-main-data',
        component: NewMainDataComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/list-main-data',
        component: ListOfMainDataComponent,
      },

      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/list-of-central-bank-transactions',
        component: ListOfCentralBankTransactionsComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/add-new-bank-transaction',
        component: AddNewBankTransactionComponent,
      },

      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/list-of-exchange',
        component: ListOfExchangeComponent,
      },

      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/list-of-online-payment',
        component: ListOfOnlinePaymentComponent,
      },

      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/list-of-transcations',
        component: ListOfTransactionsComponent,
      },

      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/list-of-transcations-of-ministry-of-finance',
        component: ListOfTranscationsOfMinistryOfFinanceComponent,
      },

      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/list-of-transcations-of-atm',
        component: ListOfTranscationsOfAtmComponent,
      },

      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/settings',
        component: SettingsComponent,
      },

      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/fees',
        component: FeesComponent,
      },

      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/bloggers',
        component: BloggersComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/search-list-shown',
        component: SearchListShownComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/chat',
        component: ChatComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/users',
        component: UsersComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/send-notification',
        component: SendNotificationComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/send-promotion',
        component: SendPromotionComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/list-promotions',
        component: ListPromotionsComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/list-notifications',
        component: ListNotificationComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/list-gifts',
        component: ListGiftsComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/create-gift',
        component: CreateGiftComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/edit-gift/:id',
        component: CreateGiftComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/list-of-banned-suggested-users',
        component: ListOfBannedSuggestedUsersComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/list-roles',
        component: ListRolesComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/permissions',
        component: PermissionsComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/list-version',
        component: ListVersionComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/add-version',
        component: AddVersionComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/add-version/:id',
        component: AddVersionComponent,
      },
      {
        canLoad: [IsAuthenticatedGuard],
        path: ':lang/suggested',
        component: SuggestedComponent,
      },

      {
        path: '',
        redirectTo: 'en/home',
        pathMatch: 'full',
      },
      {
        path: '**',
        redirectTo: 'sq/home',
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MainRouteRoutingModule {}
