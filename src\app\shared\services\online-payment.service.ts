import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
const BACKEND_URL = environment.base_url  + "/onlinePayment/"

@Injectable({
  providedIn: 'root'
})
export class OnlinePaymentService {

  constructor(
    private http: HttpClient
  ) {
  }

  getAll(page,resultsPerPage){
    let data = {
      page : page,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL+'getAll',data)
  }

  

  searchOnlinePayment(searchText,_id,pageNumber,resultsPerPage){
    let data = {
      userId: _id,
      searchText : searchText,
      page : pageNumber,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL + 'searchOnlinePayment',data)
  }

}
