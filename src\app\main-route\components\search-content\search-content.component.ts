import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { User } from 'src/app/shared/models/user';
import { AtmService } from 'src/app/shared/services/atm.service';
import { UserService } from 'src/app/shared/services/user.service';

@Component({
  selector: 'app-search-content',
  templateUrl: './search-content.component.html',
  styleUrls: ['./search-content.component.css'],
})
export class SearchContentComponent implements OnInit {
  @Input() me: User;
  @Output() userSelected: EventEmitter<any> = new EventEmitter();
  @Output() closeModal: EventEmitter<any> = new EventEmitter();

  resultSearchPageNumber = 1;
  timeout: any = null;
  resultSearch: any = null;
  searchText = '';
  searchLoading = false;
  searchMoreLoading = false;

  constructor(private userService: UserService) {}

  ngOnInit(): void {}

  searchUser(searchText) {
    clearTimeout(this.timeout);
    var $this = this;
    this.timeout = setTimeout(function () {
      if (searchText.keyCode != 13) {
        $this.searchLoading = true;
        $this.searchSocket(searchText.target.value);
      } else {
        $this.resultSearch = null;
        $this.searchLoading = false;
      }
    }, 1000);
  }
  public searchSocket(searchText) {
    this.searchText = searchText.toString();
    if (this.searchText == '') {
      this.searchLoading = false;
      this.resultSearch = null;
      this.resultSearchPageNumber = 1;
      return;
    }
    this.resultSearch = [];
    this.resultSearchPageNumber = 1;
    this.getAllResultSearch(1, null);
  }
  getAllResultSearch(pageNumber, event) {
    if (this.searchText == '') return;
    this.userService
      .searchUser(this.searchText, this.me._id, pageNumber)
      .subscribe(
        (result) => {
          if (result.status == 'success') {
            this.resultSearch = [...this.resultSearch, ...result.resultSearch];
            this.searchLoading = false;
            this.searchMoreLoading = false;
          }
        },
        (respond_error) => {
          this.searchMoreLoading = false;
          this.searchLoading = false;
        }
      );
  }
  async selectUser(user) {
    this.userSelected.emit(user);
  }

  modalDismiss() {
    this.closeModal.emit();
  }

  onScrollDown(ev: any) {
    console.log('onScrollDown');
    this.searchMoreLoading = true;
    this.resultSearchPageNumber = this.resultSearchPageNumber + 1;
    this.getAllResultSearch(this.resultSearchPageNumber, null);
    // console.log("scrolled down!!", ev);
    // this.loadData(null)
  }
}
