<div style="overflow: auto" class="projects-section">
  <div
    style="
      display: grid;
      grid-template-rows: 4em 2em 1fr 4em;
      background-color: var(--sidebar);
      padding: 5px 1em;
      border-radius: 20px;
      height: fit-content;
      max-height: 95%;
    "
  >
    <div class="disp-flex a-i-center">
      <span class="no-wrap-1-line">App Versions</span>
      <div (click)="redirectTo('/add-version')" class="m-l-auto disp-flex">
        <div class="plusButton">+</div>
      </div>
    </div>
    <div
      style="
        display: grid;
        grid-template-columns: 1fr 1fr 1.5fr 0.3fr;
        padding: 0 1em;
        background-color: rgba(26, 37, 59, 0.5);
        align-items: center;
        margin-bottom: 5px;
        border-radius: 10px;
        gap: 1em;
      "
    >
      <span style="margin-left: 1em" class="">Name</span>
      <span style="text-align: center" class="">Version</span>
      <span style="text-align: center" class="">Devices</span>
      <span style="text-align: center" class=""> Action </span>
    </div>
    <ul style="padding-inline-start: 0px; overflow: auto" class="">
      <ng-container *ngIf="loading">
        <li
          *ngFor="let i of [].constructor(10)"
          class="skeleton1"
          style="
            display: grid;
            grid-template-columns: 1fr 1fr 1.5fr 0.3fr;
            padding: 0 1em;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 177px;
                height: 17px;
                margin-bottom: 4px;
              "
            ></span>
          </div>
          <div class="disp-grid j-c-center">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 77px;
                height: 17px;
                margin-bottom: 4px;
              "
            ></span>
          </div>
          <div class="disp-grid j-c-center">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 77px;
                height: 17px;
                margin-bottom: 4px;
              "
            ></span>
          </div>

          <div class="disp-grid j-c-center">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 28px;
                height: 17px;
                margin-bottom: 4px;
              "
            ></span>
          </div>
        </li>
      </ng-container>
      <ng-container *ngIf="!loading">
        <li
          *ngFor="let appVersion of data"
          style="
            display: grid;
            grid-template-columns: 1fr 1fr 1.5fr 0.3fr;
            padding: 0 1em;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div class="disp-grid">
            <span class="no-wrap-1-line">{{ appVersion?.description }}</span>
          </div>
          <div class="disp-grid">
            <span style="text-align: center" class="no-wrap-1-line">{{
              appVersion?.current
            }}</span>
          </div>
          <div class="disp-flex j-c-center gap-1">
            <img
              *ngIf="appVersion?.appStore"
              style="width: 30px"
              src="/assets/icons/play-store.svg"
              alt=""
            />
            <img
              *ngIf="appVersion?.googlePlay"
              style="width: 30px"
              src="/assets/icons/app-store.svg"
              alt=""
            />
          </div>
          <div
            (click)="redirectTo('/add-version/' + appVersion?._id)"
            style="gap: 5px"
            class="disp-flex j-c-center"
          >
            <img style="width: 20px" src="/assets/icons/edit.svg" alt="" />
          </div>
        </li>
      </ng-container>
    </ul>
    <div class="list-number disp-flex">
      <div
        class="showingInfoWrapper"
        style="
          margin: 0 0 0.75rem;
          display: flex !important;
          align-items: center;
          gap: 1em;
        "
      >
        <span class="showingInfo"
          >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
          {{ pageNo * resultsPerPage - (resultsPerPage - data.length) }} of
          {{ count }}</span
        >
        <div style="border: 1px solid gray; border-radius: 15px">
          <div>
            <input style="display: none" id="dropdownInput" type="checkbox" />
            <div
              style="
                display: flex;
                justify-content: center;
                height: 45px;
                width: 50px;
              "
              class="dropdown"
            >
              <select
                style="
                  display: flex;
                  background: transparent;
                  border: none;
                  color: white;
                  font-size: 18px;
                  width: 90%;
                  font-weight: 600;
                "
                (ngModelChange)="resultsPerPageChanged($event)"
                [(ngModel)]="resultsPerPage"
              >
                <option class="optionStyle colorCancel">5</option>
                <option class="optionStyle colorCancel">10</option>
                <option class="optionStyle colorCancel">15</option>
                <option class="optionStyle colorCancel">20</option>
                <option class="optionStyle colorCancel">30</option>
                <option class="optionStyle colorCancel">50</option>
              </select>
              <label for="dropdownInput" class="overlay"></label>
            </div>
          </div>
        </div>
      </div>
      <nav>
        <ul class="pager">
          <li class="pager__item pager__item--prev">
            <a
              *ngIf="pageNo !== 1"
              (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Previous
            </a>
          </li>
          <li *ngIf="pageNo !== 1" class="pager__item">
            <a (click)="setPage(1)" class="pager__link">...</a>
          </li>
          <li
            *ngFor="
              let item of [].constructor(pageNoTotal) | slice : 0 : 5;
              let i = index
            "
            [ngClass]="pageNo + i === pageNo ? 'active' : ''"
            class="pager__item"
          >
            <a
              *ngIf="pageNo + i <= pageNoTotal"
              (click)="setPage(pageNo + i)"
              class="pager__link"
              >{{ pageNo + i }}</a
            >
          </li>
          <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
            <a (click)="setPage(pageNoTotal)" class="pager__link">...</a>
          </li>
          <li
            *ngIf="pageNo !== pageNoTotal"
            class="pager__item pager__item--next"
          >
            <a
              (click)="pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Next
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</div>
