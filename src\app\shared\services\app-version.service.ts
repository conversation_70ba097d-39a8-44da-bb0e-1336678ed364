import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';

const BACKEND_URL = environment.base_url  + "/update/" ;

@Injectable({
  providedIn: 'root'
})
export class AppVersionService {

  constructor(
    private http: HttpClient
  ) { }

  create(
    data
  ){
    return  this.http.post<any>( BACKEND_URL, data)
  }

  getAll(){
    return this.http.get<any>(BACKEND_URL)
  }

  getAllVersions(page,resultsPerPage){
    let data = {
      page : page,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL+'/getAllVersions/',data)
  }

  getData(id){
    return this.http.get<any>(BACKEND_URL+'/getData/'+id)
  }

  edit(data,id){
    return this.http.post<any>(BACKEND_URL+'/edit/'+id,data)
  }
  
}
