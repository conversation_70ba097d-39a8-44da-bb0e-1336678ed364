.input-image{
  visibility:hidden;
}
.projects-send-promotion {
  /* padding: 32px !important; */
  display: grid;
  grid-template-columns: 1.3fr 1fr !important;
}
.input-image-send-promotion {
  display: flex;
  flex-direction: column;
  width: 100%;
  justify-content: center;
  align-items: center;
  border-radius: 13px;
  border: 4px dotted var(--sidebar);
  gap: 5px;
  padding: 1em;
}

.input-send-promotion {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 4px;
}
.input-send-promotion span {
  /* margin-left: 5px; */
}
.input-send-promotion input {
  width: 100%;
  height: 100%;
  min-height: 40px;
  border: none;
  background-color: var(--sidebar);
  border-radius: 15px;
  font-family: var(--body-font);
  font-size: 14px;
  font-weight: 500;
  padding: 0 16px 0 16px;
  box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
  text-align: center;
  color: #fff;
}
.send-promotion-input {
  width: 100%;
  height: 100%;
  min-height: 40px;
  border: none;
  background-color: var(--sidebar);
  border-radius: 15px;
  font-family: var(--body-font);
  font-size: 14px;
  font-weight: 500;
  padding: 0 16px 0 16px;
  box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
  color: #fff;
}
.promotion-sent-image {
  width: 50%;
  border-radius: 15px;
  object-fit: contain;
}

.promotion-sent-content-image {
  height: 40px;
  width: 40px;
  border-radius: 10px;
}

.promotion-sent-image-preview {
  height: 100%;
  width: 100%;
  border-radius: 10px;
  position: absolute;
  object-fit: cover;
}

.promotion-sent-image-preview-home {
  height: 70%;
  width: 100%;
  border-radius: 10px 10px 0 0;
  position: absolute;
  object-fit: cover;
}
::ng-deep .mat-step-header .mat-step-icon-selected {
  background-color: var(--action-color);
}
::ng-deep .mat-stepper-vertical {
  background-color: var(--projects-section);
  border: none;
  overflow: auto;
  border-radius: 20px;
}
mat-slider {
  width: 300px;
}
::ng-deep .custom-slider .ngx-slider .ngx-slider-tick {
  border-radius: 0;
  background: #d1fff7;
}
::ng-deep .custom-slider .ngx-slider .ngx-slider-selection {
  background: var(--action-color);
}
::ng-deep .custom-slider .ngx-slider .ngx-slider-tick.ngx-slider-selected {
  background: var(--action-color);
}
::ng-deep .mat-stepper-vertical-line::before {
  content: "";
  position: absolute;
  left: 0;
  border-left-width: 2px;
  border-left-style: solid;
  border-left-color: var(--action-color-50);
}
::ng-deep .mat-step-header .mat-step-label.mat-step-label-active{
  color: white;
}

button {
  background-color: var(--action-color-50);
  border: 1px solid var(--action-color);
  color: white;
  border-radius: 10px;
  height: 30px;
  width: 80px;
}
.buttonReset {
  background-color: var(--action-color);
  color: white;
  border-radius: 10px;
  height: 30px;
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
}
.test {
  font-size: 30px;
}
::ng-deep .mat-step-header .mat-step-label.mat-step-label-active{
  color: white;
}
p,
span,
div {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
