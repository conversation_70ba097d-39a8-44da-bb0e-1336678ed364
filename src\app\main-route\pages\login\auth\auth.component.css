@import url("https://fonts.googleapis.com/css?family=Raleway:400,700");

/* .container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
} */

.screen {
  background: linear-gradient(90deg, #32dbc6, #32dbc6);
  position: relative;
  height: 600px;
  width: 360px;
}

.screen__content {
  z-index: 1;
  position: relative;
  height: 100%;
}

.screen__background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  -webkit-clip-path: inset(0 0 0 0);
  clip-path: inset(0 0 0 0);
}

.screen__background__shape {
  transform: rotate(45deg);
  position: absolute;
}

.screen__background__shape1 {
  height: 520px;
  width: 520px;
  background: #166058;
  top: -50px;
  right: 120px;
  border-radius: 0 72px 0 0;
}

.screen__background__shape2 {
  height: 220px;
  width: 220px;
  background: #098d7e;
  top: -172px;
  right: 0;
  border-radius: 32px;
}

.screen__background__shape3 {
  height: 540px;
  width: 190px;
  background: linear-gradient(270deg, #099988, rgb(27, 181, 152));
  top: -24px;
  right: 0;
  border-radius: 32px;
}

.screen__background__shape4 {
  height: 400px;
  width: 200px;
  background: #20b5a3;
  top: 420px;
  right: 50px;
  border-radius: 60px;
}

.login {
  width: 320px;
  padding: 30px;
  padding-top: 56px;
}

.login__field {
  padding: 20px 0px;
  position: relative;
}

.login__icon {
  position: absolute;
  top: 30px;
  color: #75b5ab;
}

.login__input {
  border: none;
  border-bottom: 2px solid #d9f3f7;
  background: none;
  padding: 10px;
  padding-left: 24px;
  font-weight: 700;
  width: 75%;
  transition: 0.2s;
  color: rgb(255, 255, 255);
}
.login__input::placeholder {
  color: rgba(255, 255, 255, 0.492);
}

.login__input:active,
.login__input:focus,
.login__input:hover {
  outline: none;
  border-bottom-color: #41dbdb;
}

.login__submit {
  background: #fff;
  font-size: 14px;
  margin-top: 30px;
  padding: 16px 20px;
  border-radius: 26px;
  border: 1px solid #d4d3e8;
  text-transform: uppercase;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5em;
  width: 100%;
  color: #48999d;
  box-shadow: 0px 2px 2px #67aeb1;
  cursor: pointer;
  transition: 0.2s;
}

.login__submit:active,
.login__submit:focus,
.login__submit:hover {
  border-color: #18e1de;
  outline: none;
}

.button__icon {
  font-size: 24px;
  color: #14c9c3;
}

.social-login {
  position: absolute;
  height: 140px;
  width: 160px;
  text-align: center;
  bottom: 0px;
  right: 0px;
  color: #fff;
}

.social-icons {
  display: flex;
  align-items: center;
  justify-content: center;
}

.social-login__icon {
  padding: 20px 10px;
  color: #fff;
  text-decoration: none;
  text-shadow: 0px 0px 8px #1c82a2;
}

.social-login__icon:hover {
  transform: scale(1.5);
}
p,
span,
div {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
