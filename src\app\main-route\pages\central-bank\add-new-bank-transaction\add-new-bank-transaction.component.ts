import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { AuthService } from 'src/app/shared/services/auth.service';
import { CentralBankTransactionsService } from 'src/app/shared/services/central-bank-transactions.service';
import { FiberBankTransactionService } from 'src/app/shared/services/fiber-bank-transaction.service';
import { FilterService } from 'src/app/shared/services/filter.service';
import { LoadJsService } from 'src/app/shared/services/load-js.service';
import { MainDataService } from 'src/app/shared/services/main-data.service';
import { TransactionService } from 'src/app/shared/services/transaction.service';
import { UserService } from 'src/app/shared/services/user.service';

@Component({
  selector: 'app-add-new-bank-transaction',
  templateUrl: './add-new-bank-transaction.component.html',
  styleUrls: ['./add-new-bank-transaction.component.css']
})
export class AddNewBankTransactionComponent implements OnInit {

  lang = 'en'
  params_lang = ''

  form = new UntypedFormGroup({});

  routerLinkHome = '/' + this.lang + '/home'
  exchangeTypes = [
    'loan-to-central-bank',
    'tax-collection'
  ]

  loading = false
  languages: any[] = []

  _korisnikId: any = undefined
  me: User;

  centralBankTotalAmount = 0
  theMinisterOfFinanceTotalaAmount = 0

  constructor(
    private router: Router, 
    private authService: AuthService,
    private userService: UserService,
    private toastrService: ToastrService,
    private filterService: FilterService,
    private mainDataService: MainDataService,
    private centralBankTransactionsService: CentralBankTransactionsService,
    private activatedRoute: ActivatedRoute,
    public loadJsService: LoadJsService,
    private formBuilder: UntypedFormBuilder ) { 
      this.loading = true
  }

  ngOnInit(): void {
    this.authService.user.pipe().subscribe( async appUser => {
      this.me = appUser
    })
      this.activatedRoute.params.subscribe(async params => {    
        if(params['lang']){
          this.lang = params['lang'];   
        }
        this.prepareForm()

        this.getBy('central-bank-total-amount')
      });
  }


  async prepareForm(){ 

    this.form = this.formBuilder.group({
      total: new UntypedFormControl(0),
      type: new UntypedFormControl('loan-to-central-bank'),
    });
    
    this.loadJsService.loadScripts()
    this.loading = false
  }

  // the-minister-of-finance-total-amount

  getBy(variableName){
    this.mainDataService.getByVariableName(variableName).subscribe( 
      async result =>{
        if(result.status == "success"){
            if(result?.data?.mainData[0]?.variableName == 'central-bank-total-amount'){
              this.centralBankTotalAmount = result?.data?.mainData[0]?.value
            }

            if(result?.data?.mainData[0]?.variableName == 'the-minister-of-finance-total-amount'){
              this.theMinisterOfFinanceTotalaAmount = result?.data?.mainData[0]?.value
            }
            // this.user = result.data.data
        }
      },
      respond_error => {
       this.toastrService.error(
         respond_error?.error.message
          , respond_error?.name);         
       }
    )
  }



  create(){
    if(this.form.value.total === 0 || this.form.value.total === null){
      this.toastrService.error( "Transaction value need to be more than 0 coins" );
      this.form.value.total = 0
      return
    }
    if(this.form.value.total < this.theMinisterOfFinanceTotalaAmount){
      this.toastrService.error( "The Minister Of Finance does not have actual amount of credits for moment" );
      return
    }
    let reason = 'Central Bank is getting loan of '+ this.form.value.total +' credits. Executed by ' + this.me.name + ' ' + this.me.surname
    
    if(this.form.value.type === 'tax-collection'){
      reason = 'The Minister Of Finance transfer '+ this.form.value.total +'credits of their budget (tax-collection) to Central Bank. Executed by' + this.me.name+ ' ' + this.me.surname
    } 

    this.centralBankTransactionsService.create(   
      {
        total:this.form.value.total,
        reason:reason,
        isSenderFiberBank:true,
        isReceiverFiberBank:true
      },
      {
        total:this.form.value.total,
        type : this.form.value.type,
        createdBy: this.me._id
      }
    ).subscribe( respond  => {
            if( respond.status = "success"){
  
              this.router.navigate(["/" + this.lang + "/list-of-central-bank-transactions"]).then(() => {});
    
              this.toastrService.success( "Successfully created" );
              this.form.reset();
            }
        },
        respond_error => {
          this.toastrService.error(
            respond_error?.error.message
             , respond_error?.name); 
        }

      ); 

  }

  onChange(event){
    //  = event
    if( event === 'tax-collection'){
      this.getBy('the-minister-of-finance-total-amount')
    }
    if( event === 'loan-to-central-bank'){
      this.getBy('central-bank-total-amount')
    }
  }

  ngOnDestroy(){
    this.loadJsService.removeScripts()
  }

}
