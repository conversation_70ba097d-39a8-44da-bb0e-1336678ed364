.input-image{
  visibility:hidden;
}
.projects-section {
  padding: 32px;
}
.projects-send-notification {
  /* padding: 32px !important; */
  display: grid;
  grid-template-columns: 400px 1fr !important;
}
.search-bar-send-notification {
  height: 55px;
  display: flex;
  width: 100%;
}
.search-bar-send-notification span {
  width: 100%;
  height: 100%;
  border: none;
  background-color: var(--sidebar);
  border-radius: 15px;
  font-family: var(--body-font);
  font-size: 14px;
  font-weight: 500;
  padding: 0 40px 0 16px;
  box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 56.966 56.966' fill='%23717790c7'%3e%3cpath d='M55.146 51.887L41.588 37.786A22.926 22.926 0 0046.984 23c0-12.682-10.318-23-23-23s-23 10.318-23 23 10.318 23 23 23c4.761 0 9.298-1.436 13.177-4.162l13.661 14.208c.571.593 1.339.92 2.162.92.779 0 1.518-.297 2.079-.837a3.004 3.004 0 00.083-4.242zM23.984 6c9.374 0 17 7.626 17 17s-7.626 17-17 17-17-7.626-17-17 7.626-17 17-17z'/%3e%3c/svg%3e");
  background-size: 14px;
  background-repeat: no-repeat;
  background-position: 96%;
  color: #fff;
  display: flex;
  align-items: center;
}
.pill-users-wrapper {
  display: flex;
  flex-wrap: wrap;
  position: relative;
  padding: 1.4rem 0;
  border-radius: 14px;
}
.pill-users {
  position: relative;
  flex: 1 0 6.7rem;
  margin: 0.7rem;
  color: #fff;
  cursor: pointer;
  border-radius: 10px;
  overflow: hidden;
  background-color: var(--action-color-30);
  padding: 7px 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2px;
}
.pill-users-image {
  height: 30px;
  width: 30px;
  border-radius: 50%;
}

.input-send-notification {
  height: 40px;
  display: flex;
  width: 100%;
}
.input-send-notificationTA {
  height: 80px;
  display: flex;
  width: 100%;
}
.input-send-notification input {
  width: 100%;
  height: 100%;
  border: none;
  background-color: var(--sidebar);
  border-radius: 15px;
  font-family: var(--body-font);
  font-size: 14px;
  font-weight: 500;
  padding: 0 40px 0 16px;
  box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
  /* background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 56.966 56.966' fill='%23717790c7'%3e%3cpath d='M55.146 51.887L41.588 37.786A22.926 22.926 0 0046.984 23c0-12.682-10.318-23-23-23s-23 10.318-23 23 10.318 23 23 23c4.761 0 9.298-1.436 13.177-4.162l13.661 14.208c.571.593 1.339.92 2.162.92.779 0 1.518-.297 2.079-.837a3.004 3.004 0 00.083-4.242zM23.984 6c9.374 0 17 7.626 17 17s-7.626 17-17 17-17-7.626-17-17 7.626-17 17-17z'/%3e%3c/svg%3e");
  background-size: 14px;
  background-repeat: no-repeat;
  background-position: 96%; */
  color: #fff;
}
.input-send-notificationTA textarea {
  width: 100%;
  height: 100%;
  border: none;
  background-color: var(--sidebar);
  border-radius: 15px;
  font-family: var(--body-font);
  font-size: 14px;
  font-weight: 500;
  padding: 16px;
  box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
  color: #fff;
}
.notification-sent-sender-image {
  height: 40px;
  width: 40px;
  border-radius: 50%;
}
.notification-sent-content-image-before{
  height: 40px;
  width: 40px;
  min-width: 40px;
  border-radius: 10px;
  border: 2.5px dotted var(--projects-section); 
  background-image: url('/assets/icons/photo-camera.svg');
  background-position: center;
  background-size: 20px;
  background-repeat: no-repeat;
  object-fit: cover;
  position: relative;
}
.notification-sent-content-image {
  height: 40px;
  width: 40px;
  min-width: 40px;
  border-radius: 10px;
  background-size: 20px;
  position: relative;
  object-fit: cover;
}

.notification-sent-sender-image-preview {
  height: 30px;
  width: 30px;
  border-radius: 50%;
}

.notification-sent-content-image-preview {
  height: 30px;
  width: 30px;
  border-radius: 10px;
}
::ng-deep .mat-step-header .mat-step-icon-selected {
  background-color: var(--action-color);
}
::ng-deep .mat-stepper-vertical {
  background-color: var(--projects-section);
  border: none;
  overflow: auto;
  border-radius: 20px;
}
mat-slider {
  width: 300px;
}
::ng-deep .custom-slider .ngx-slider .ngx-slider-tick {
  border-radius: 0;
  background: #d1fff7;
}
::ng-deep .custom-slider .ngx-slider .ngx-slider-selection {
  background: var(--action-color);
}
::ng-deep .custom-slider .ngx-slider .ngx-slider-tick.ngx-slider-selected {
  background: var(--action-color);
}
::ng-deep .mat-stepper-vertical-line::before {
  content: "";
  position: absolute;
  left: 0;
  border-left-width: 2px;
  border-left-style: solid;
  border-left-color: var(--action-color-50);
}
mat-slider {
  width: 300px;
}
::ng-deep .custom-slider .ngx-slider .ngx-slider-tick {
  border-radius: 0;
  background: #d1fff7;
}
::ng-deep .custom-slider .ngx-slider .ngx-slider-selection {
  background: var(--action-color);
}
::ng-deep .custom-slider .ngx-slider .ngx-slider-tick.ngx-slider-selected {
  background: var(--action-color);
}
::ng-deep .mat-stepper-vertical-line::before {
  content: "";
  position: absolute;
  left: 0;
  border-left-width: 2px;
  border-left-style: solid;
  border-left-color: var(--action-color-50);
}

button {
  background-color: var(--action-color-50);
  border: 1px solid var(--action-color);
  color: white;
  border-radius: 10px;
  height: 30px;
  width: 80px;
}
.buttonReset {
  background-color: var(--action-color);
  color: white;
  border-radius: 10px;
  height: 30px;
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
}
.test {
  font-size: 30px;
}
.send-promotion-input {
  width: 100%;
  height: 100%;
  min-height: 40px;
  border: none;
  background-color: var(--sidebar);
  border-radius: 15px;
  font-family: var(--body-font);
  font-size: 14px;
  font-weight: 500;
  padding: 0 16px 0 16px;
  box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
  color: #fff;
}
.input-send-promotion input {
  width: 100%;
  height: 100%;
  min-height: 40px;
  border: none;
  background-color: var(--sidebar);
  border-radius: 15px;
  font-family: var(--body-font);
  font-size: 14px;
  font-weight: 500;
  padding: 0 16px 0 16px;
  box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
  text-align: center;
  color: #fff;
}

p,
span,
div {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
