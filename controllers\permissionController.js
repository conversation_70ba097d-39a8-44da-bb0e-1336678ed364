const catchAsync = require('./../utils/catchAsync');

const Permission = require('./../models/permissionModel');


exports.create = catchAsync(async (req, res, next) => {

    console.log("req.body: ",req.body)
      let data = await Permission.create(req.body);   
      if(data !== null){
        res.status(201).json({
          status: 'success'
         });
      }
});


exports.getAllByCategoryId = catchAsync(async (req, res, next) => {
    
    let updates = 
    await  Permission.find({
        category: req.body.category
    })    
    .sort({ _id: 1 })

  
    res.status(200).json({
      status: 'success',
      data: {
        data: updates
      }
    });
  
  });