<div style="overflow: scroll" class="projects-section">
  <div
    style="
      /* display: none !important; */
      display: grid;
      grid-template-rows: 4em 2em 1fr 4em;
      background-color: var(--sidebar);
      padding: 5px 1em;
      border-radius: 20px;
      height: fit-content;
      max-height: 95%;
    "
  >
    <div class="disp-flex a-i-center">
      <span class="no-wrap-1-line">Recent Transactions</span>
      <div class="m-l-auto disp-flex">
        <div class="search-bar">
          <input 
          (keyup)="searchUser($event)"
          type="text" placeholder="Search" />
        </div>
        <!-- <button class="add-btn" title="Add New Project">
          <svg
            class="btn-icon"
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="3"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="feather feather-plus"
          >
            <line x1="12" y1="5" x2="12" y2="19" />
            <line x1="5" y1="12" x2="19" y2="12" />
          </svg>
        </button> -->
      </div>
    </div>
    <div
      style="
        display: grid;
        grid-template-columns: 1.5fr 1fr 1fr 1fr 2fr 0.3fr;
        background-color: rgba(26, 37, 59, 0.5);
        align-items: center;
        margin-bottom: 5px;
        border-radius: 10px;
        gap: 1em;
      "
    >
      <span style="margin-left: 1em" class="no-wrap-1-line-200"
        >Order Details</span
      >
      <span class="no-wrap-1-line-200">Sender</span>
      <span class="no-wrap-1-line-200">Receiver</span>
      <span class="no-wrap-1-line-200"> Created At </span>
      <span class="no-wrap-1-line-200"> Reason </span>
      <span class="no-wrap-1-line-200"> Action </span>
    </div>
    <ul style="padding-inline-start: 0px; overflow: scroll" class="">
      <!-- Skeleton ENd kjo loading nuk punon ne fillim kur hin  -->
      <ng-container *ngIf="startLoading">
        <li
          *ngFor="let i of [].constructor(19)"
          style="
            display: grid;
            grid-template-columns: 1.5fr 1fr 1fr 1fr 2fr 0.3fr;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div style="gap: 7px" class="disp-flex">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 40px;
                height: 40px;
                border-radius: 50%;
              "
            ></span>
            <div class="disp-grid">
              <span
                class="skeleton1"
                style="
                  font-size: 14px;
                  font-weight: 500;
                  opacity: 0.7;
                  width: 177px;
                  height: 17px;
                  margin-bottom: 4px;
                "
              ></span>
              <span
                class="skeleton2"
                style="
                  font-size: 14px;
                  font-weight: 500;
                  opacity: 0.7;
                  width: 111px;
                  height: 16px;
                "
              ></span>
            </div>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton1"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 177px;
                height: 17px;
                margin-bottom: 4px;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 111px;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton1"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 177px;
                height: 17px;
                margin-bottom: 4px;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 111px;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton1"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 177px;
                height: 17px;
                margin-bottom: 4px;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 111px;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton1"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 100%;
                height: 19px;
                margin-bottom: 4px;
              "
            ></span>
          </div>
          <div class="disp-flex j-c-center">
            <span
              class="skeleton1"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 28px;
                height: 10px;
                margin-bottom: 4px;
              "
            ></span>
          </div>
        </li>
      </ng-container>
      <!-- Skeleton ENd -->

      <!-- List empty  -->
      <div
      *ngIf="data?.length === 0 && !startLoading"
        style="
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
        "
      >
        <img
          style="width: 70%; max-width: 299px"
          src="/assets/icons/animatedIconsTable/list_empty_transaction_infinite.svg"
          alt=""
        />
        <p style="font-size: 16px">List is empty</p>
      </div>
      <!-- List empty end -->

      <!-- List empty  -->
      <div
        *ngIf="data?.length === 0 && !startLoading"
        style="
          position: relative;
          min-height: 365px;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          height: 50%;
        "
      >
        <div>
          <img
            style="width: 70%; max-width: 299px"
            src="../../../../../assets/icons/animatedIconsTable/list_empty_transaction_infinite.svg"
            alt=""
          />
          <p style="font-size: 16px">Transaction list is empty</p>
        </div>
      </div>
      <!-- List empty end -->
      <li
        *ngFor="
          let dat of data | searchFilter : searchForm.value.search;
          let i = index
        "
        style="
          display: grid;
          grid-template-columns: 1.5fr 1fr 1fr 1fr 2fr 0.3fr;
          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
        "
      >
        <div style="gap: 7px" class="disp-flex">
          <ng-container *ngIf="dat?.isWithdrawCreditsTransaction">
            <img
              style="min-width: 30px; max-width: 30px; opacity: 1"
              src="/assets/icons/transactionIcons/withdrawcreditstransaction.svg"
              alt=""
            />
          </ng-container>
          <ng-container *ngIf="dat?.isOnlinePayment">
            <img
              style="min-width: 30px; max-width: 30px; opacity: 1"
              src="/assets/icons/transactionIcons/withdrawcreditstransaction.svg"
              alt=""
            />
          </ng-container>
          <ng-container *ngIf="dat?.isBuyingCreditsTransaction">
            <img
              style="min-width: 30px; max-width: 30px; opacity: 1"
              src="/assets/icons/transactionIcons/buyingcreditstransaction.svg"
              alt=""
            />
          </ng-container>
          <ng-container *ngIf="dat?.isBuyingGiftTransaction">
            <img
              style="min-width: 30px; max-width: 30px; opacity: 1"
              src="/assets/icons/transactionIcons/buyinggifttrans.svg"
              alt=""
            />
          </ng-container>
          <ng-container *ngIf="dat?.isSellingGiftTransaction">
            <img
              style="min-width: 30px; max-width: 30px; opacity: 1"
              src="/assets/icons/transactionIcons/selling_gift_tr.svg"
              alt=""
            />
          </ng-container>
          <ng-container *ngIf="dat?.isPayPerViewTransaction">
            <img
              style="min-width: 30px; max-width: 30px; opacity: 1"
              src="/assets/icons/transactionIcons/ppv_tr.svg"
              alt=""
            />
          </ng-container>
          <ng-container *ngIf="dat?.isPromotionSharingTransaction">
            <img
              style="min-width: 30px; max-width: 30px; opacity: 1"
              src="/assets/icons/transactionIcons/share_promo_tr.svg"
              alt=""
            />
          </ng-container>
          <ng-container *ngIf="dat?.isTransferTransaction">
            <img
              style="min-width: 30px; max-width: 30px; opacity: 1"
              src="/assets/icons/transactionIcons/transfer_tr.svg"
              alt=""
            />
          </ng-container>

          <div class="disp-grid">
            <span class="no-wrap-1-line">
              <ng-container *ngIf="dat?.isWithdrawCreditsTransaction">
                Withdraw Credits Transaction
              </ng-container>
              <ng-container *ngIf="dat?.isOnlinePayment">
                Online Payment
              </ng-container>
              <ng-container *ngIf="dat?.isBuyingCreditsTransaction">
                Buying Credits Transaction
              </ng-container>
              <ng-container *ngIf="dat?.isBuyingGiftTransaction">
                Buying Gift Transaction
              </ng-container>
              <ng-container *ngIf="dat?.isSellingGiftTransaction">
                Selling Gift Transaction
              </ng-container>
              <ng-container *ngIf="dat?.isPayPerViewTransaction">
                Pay Per View Transaction
              </ng-container>
              <ng-container *ngIf="dat?.isPromotionSharingTransaction">
                Promotion Sharing Transaction
              </ng-container>
              <ng-container *ngIf="dat?.isTransferTransaction">
                Transfer Transaction
              </ng-container>
              <ng-container *ngIf="dat?.isSenderFiberBank">
                Sender is Fiber Bank
              </ng-container>
              <ng-container *ngIf="dat?.isReceiverFiberBank">
                Receiver is Fiber Bank
              </ng-container>
              <ng-container *ngIf="dat?.isSenderMinistryOfFinance">
                Sender is Ministry Of Finance
              </ng-container>
              <ng-container *ngIf="dat?.isReceiverMinistryOfFinance">
                Receiver is Ministry Of Finance
              </ng-container>
            </span>
            <span
              class="no-wrap-1-line"
              style="font-size: 14px; font-weight: 500; opacity: 0.7"
              >{{ dat?.total }} Credits</span
            >

            <!--! <span class="no-wrap-1-line"
              >Withdraw Credits Transaction 20 Credits</span
            >
            <span
              class="no-wrap-1-line"
              style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >
              20 Credits</span
            > -->
          </div>
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line"
            >{{ dat?.senderId?.name }} {{ dat?.senderId?.surname }}</span
          >
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >{{ dat?.senderId?.email }}</span
          >
          <!-- !<span class="no-wrap-1-line">amer Abdullai</span>
          <span class="no-wrap-1-line"><EMAIL></span> -->
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line"
            >{{ dat?.receiverId?.name }} {{ dat?.receiverId?.surname }}</span
          >
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >{{ dat?.receiverId?.email }}</span
          >
          <!-- !<span class="no-wrap-1-line">Amer Abdullai</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            ><EMAIL></span
          > -->
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">{{
            dat?.createdAt | date : "h:mm a"
          }}</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >{{ dat?.createdAt | date : "MMMM d, y" }}</span
          >
          <!-- !<span class="no-wrap-1-line">10 days ago</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >dec 03 2023</span
          > -->
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">{{ dat?.reason }}</span>
        </div>
        <div class="disp-flex j-c-center">
          <span
            (click)="selectTransaction(dat); openOptionsModal(optionsContent)"
            class="no-wrap-1-line"
            >•••</span
          >
        </div>
      </li>
    </ul>
    <div class="list-number disp-flex" style="">
      <div
        class="showingInfoWrapper"
        style="
          margin: 0 0 0.75rem;
          display: flex !important;
          align-items: center;
          gap: 1em;
        "
      >
        <span class="showingInfo"
          >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
          {{ pageNo * resultsPerPage - (resultsPerPage - data.length) }} of
          {{ count }}</span
        >
        <div style="border: 1px solid gray; border-radius: 15px">
          <div>
            <input style="display: none" id="dropdownInput" type="checkbox" />
            <div
              style="
                display: flex;
                justify-content: center;
                height: 45px;
                width: 50px;
              "
              class="dropdown"
            >
              <select
                style="
                  display: flex;
                  background: transparent;
                  border: none;
                  color: white;
                  font-size: 18px;
                  width: 90%;
                  font-weight: 600;
                "
                (ngModelChange)='resultsPerPageChanged($event)' 
                [(ngModel)]="resultsPerPage">
              <option class="optionStyle colorCancel">7</option>
              <option class="optionStyle colorCancel">10</option>
              <option class="optionStyle colorCancel">15</option>
              <option class="optionStyle colorCancel">20</option>
              <option class="optionStyle colorCancel">30</option>
              <option class="optionStyle colorCancel">50</option>
              </select>
              <label for="dropdownInput" class="overlay"></label>
            </div>
          </div>
        </div>
      </div>
      <nav>
        <ul class="pager">
          <li class="pager__item pager__item--prev">
            <a
              *ngIf="pageNo !== 1"
              (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Previous
            </a>
          </li>
          <li *ngIf="pageNo !== 1" class="pager__item">
            <a (click)="setPage(1)" class="pager__link">...</a>
          </li>
          <li
            *ngFor="
              let item of [].constructor(pageNoTotal) | slice : 0 : 5;
              let i = index
            "
            [ngClass]="pageNo + i === pageNo ? 'active' : ''"
            class="pager__item"
          >
            <a
              *ngIf="pageNo + i <= pageNoTotal"
              (click)="setPage(pageNo + i)"
              class="pager__link"
              >{{ pageNo + i }}</a
            >
          </li>
          <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
            <a (click)="setPage(pageNoTotal)" class="pager__link">...</a>
          </li>
          <li
            *ngIf="pageNo !== pageNoTotal"
            class="pager__item pager__item--next"
          >
            <a
              (click)="pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Next
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
  <ng-template #optionsContent let-modal>
    <div
      style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1"
    >
      <div class="modal-body">
        <!-- header -->
        <div style="display: flex; justify-content: end; width: 100%">
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; margin-left: auto"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->
        <div
          style="
            background: var(--app-bg);
            min-height: 350px;
            height: 100%;
            max-height: fit-content;
            width: 100%;
            display: grid;
            justify-content: center;
            align-items: center;
          "
        >
          <div
            style="display: flex; flex-direction: column; align-items: center"
          >
            <div style="padding: 0 2em; width: 100%">
              <div
                style="
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
              >
                <p style="font-size: 18px; font-weight: 700; margin: 1em 0 0">
                  Transaction Detail
                </p>
              </div>
            </div>
            <div
              style="
                padding: 2em 2em;
                width: 100%;
                display: flex;
                flex-direction: column;
                gap: 1em;
              "
            >
              <div style="border: 1px solid gray; border-radius: 15px">
                <p
                  style="
                    margin: -11px 0px 0 14px;
                    font-size: 16px;
                    font-weight: 600;
                    background: var(--app-bg);
                    width: fit-content;
                    padding: 0 13px;
                  "
                >
                  Order Details
                </p>
                <div
                  style="
                    width: 100%;
                    padding: 0em 2em 1em;
                    text-align: center;
                    border-radius: 10px;
                    display: flex;
                    flex-direction: column;
                    gap: 1em;
                  "
                  class=""
                >
                  <div style="width: 100%">
                    <p
                      style="
                        text-align: start;
                        padding: 0 1em;
                        margin: 1em 0 0 0;
                      "
                    >
                      Type of transaction
                    </p>
                    <p
                      style="
                        box-shadow: 0px 2px 2px #67aeb1;
                        border-radius: 10px;
                        margin: 0;
                        line-height: 2.5;
                      "
                    >
                      <ng-container
                        *ngIf="
                          selectedTransaction?.isWithdrawCreditsTransaction
                        "
                      >
                        Withdraw Credits Transaction
                      </ng-container>
                      <ng-container
                        *ngIf="selectedTransaction?.isOnlinePayment"
                      >
                        Online Payment
                      </ng-container>
                      <ng-container
                        *ngIf="selectedTransaction?.isBuyingCreditsTransaction"
                      >
                        Buying Credits Transaction
                      </ng-container>
                      <ng-container
                        *ngIf="selectedTransaction?.isBuyingGiftTransaction"
                      >
                        Buying Gift Transaction
                      </ng-container>
                      <ng-container
                        *ngIf="selectedTransaction?.isSellingGiftTransaction"
                      >
                        Selling Gift Transaction
                      </ng-container>
                      <ng-container
                        *ngIf="selectedTransaction?.isPayPerViewTransaction"
                      >
                        Pay Per View Transaction
                      </ng-container>
                      <ng-container
                        *ngIf="
                          selectedTransaction?.isPromotionSharingTransaction
                        "
                      >
                        Promotion Sharing Transaction
                      </ng-container>
                      <ng-container
                        *ngIf="selectedTransaction?.isTransferTransaction"
                      >
                        Transfer Transaction
                      </ng-container>
                      <ng-container
                        *ngIf="selectedTransaction?.isSenderFiberBank"
                      >
                        Sender is Fiber Bank
                      </ng-container>
                      <ng-container
                        *ngIf="selectedTransaction?.isReceiverFiberBank"
                      >
                        Receiver is Fiber Bank
                      </ng-container>
                      <ng-container
                        *ngIf="selectedTransaction?.isSenderMinistryOfFinance"
                      >
                        Sender is Ministry Of Finance
                      </ng-container>
                      <ng-container
                        *ngIf="selectedTransaction?.isReceiverMinistryOfFinance"
                      >
                        Receiver is Ministry Of Finance
                      </ng-container>
                    </p>
                  </div>

                  <div style="display: flex; gap: 1em">
                    <div style="width: 100%">
                      <p style="text-align: start; margin: 0 1em">Amount</p>
                      <p
                        style="
                          box-shadow: 0px 2px 2px #67aeb1;
                          border-radius: 10px;
                          margin: 0;
                          line-height: 2.5;
                        "
                      >
                        {{ selectedTransaction?.total }} credits
                      </p>
                    </div>
                    <div style="width: 100%">
                      <p style="text-align: start; margin: 0 1em">Time</p>
                      <p
                        style="
                          box-shadow: 0px 2px 2px #67aeb1;
                          border-radius: 10px;
                          margin: 0;
                          line-height: 2.5;
                        "
                      >
                        {{ selectedTransaction?.createdAt | date : "short" }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div style="display: flex; gap: 1em">
                <div
                  style="
                    border: 1px solid gray;
                    border-radius: 15px;
                    width: 100%;
                  "
                >
                  <p
                    style="
                      margin: -11px 0px 0 14px;
                      font-size: 16px;
                      font-weight: 600;
                      background: var(--app-bg);
                      width: fit-content;
                      padding: 0 13px;
                    "
                  >
                    Sender
                  </p>
                  <div
                    style="
                      width: 100%;
                      padding: 0em 2em 1em;
                      text-align: center;
                      border-radius: 10px;
                      display: flex;
                      flex-direction: column;
                      gap: 1em;
                    "
                    class=""
                  >
                    <div style="width: 100%">
                      <p
                        style="
                          text-align: start;
                          padding: 0 1em;
                          margin: 1em 0 0 0;
                        "
                      >
                        Name
                      </p>
                      <p
                        style="
                          box-shadow: 0px 2px 2px #67aeb1;
                          border-radius: 10px;
                          margin: 0;
                          line-height: 2.5;
                        "
                      >
                        {{ selectedTransaction?.senderId?.name }}
                        {{ selectedTransaction?.senderId?.surname }}
                      </p>
                    </div>
                    <div style="width: 100%">
                      <p
                        style="
                          text-align: start;
                          padding: 0 1em;
                          margin: 1em 0 0 0;
                        "
                      >
                        Email
                      </p>
                      <p
                        style="
                          box-shadow: 0px 2px 2px #67aeb1;
                          border-radius: 10px;
                          margin: 0;
                          line-height: 2.5;
                        "
                      >
                        {{ selectedTransaction?.senderId?.email }}
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  style="
                    border: 1px solid gray;
                    border-radius: 15px;
                    width: 100%;
                  "
                >
                  <p
                    style="
                      margin: -11px 0px 0 14px;
                      font-size: 16px;
                      font-weight: 600;
                      background: var(--app-bg);
                      width: fit-content;
                      padding: 0 13px;
                    "
                  >
                    Receiver
                  </p>
                  <div
                    style="
                      width: 100%;
                      padding: 0em 2em 1em;
                      text-align: center;
                      border-radius: 10px;
                      display: flex;
                      flex-direction: column;
                      gap: 1em;
                    "
                    class=""
                  >
                    <div style="width: 100%">
                      <p
                        style="
                          text-align: start;
                          padding: 0 1em;
                          margin: 1em 0 0 0;
                        "
                      >
                        Name
                      </p>
                      <p
                        style="
                          box-shadow: 0px 2px 2px #67aeb1;
                          border-radius: 10px;
                          margin: 0;
                          line-height: 2.5;
                        "
                      >
                        {{ selectedTransaction?.receiverId?.name }}
                        {{ selectedTransaction?.receiverId?.surname }}
                      </p>
                    </div>
                    <div style="width: 100%">
                      <p
                        style="
                          text-align: start;
                          padding: 0 1em;
                          margin: 1em 0 0 0;
                        "
                      >
                        Email
                      </p>
                      <p
                        style="
                          box-shadow: 0px 2px 2px #67aeb1;
                          border-radius: 10px;
                          margin: 0;
                          line-height: 2.5;
                        "
                      >
                        {{ selectedTransaction?.receiverId?.email }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div style="border: 1px solid gray; border-radius: 15px">
                <p
                  style="
                    margin: -11px 0px 0 14px;
                    font-size: 16px;
                    font-weight: 600;
                    background: var(--app-bg);
                    width: fit-content;
                    padding: 0 13px;
                  "
                >
                  Reason
                </p>
                <div
                  style="
                    width: 100%;
                    padding: 0em 2em 1em;
                    text-align: center;
                    border-radius: 10px;
                    display: flex;
                    flex-direction: column;
                    gap: 1em;
                  "
                  class=""
                >
                  <div style="width: 100%">
                    <p
                      style="
                        text-align: start;
                        padding: 0 1em;
                        margin: 1em 0 0 0;
                      "
                    >
                      Reason for transaction
                    </p>
                    <p
                      style="
                        box-shadow: 0px 2px 2px #67aeb1;
                        border-radius: 10px;
                        margin: 0;
                        line-height: 2;
                      "
                    >
                      {{ selectedTransaction?.reason }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
</div>
