import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';

const BACKEND_URL = environment.base_url  + "/mainData/"

@Injectable({
  providedIn: 'root'
})
export class MainDataService {

  constructor(
    private http: HttpClient
  ) { }

  create(
    data
  ){
    return  this.http.post<any>( BACKEND_URL, data)
  }

  getAll(page,resultsPerPage){
    let data = {
      page : page,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL+'getAll',data)
  }


  getByVariableName(variableName){
    let data = {
      variableName : variableName
    }
    return this.http.post<any>(BACKEND_URL+'getByVariableName',data)
  }

}
