                <!-- Container-fluid starts-->
                <div class="container-fluid" >
                    <div class="page-header">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="page-header-left">
                                    <h3> Create main data	
                                        <small> Fiber Admin Panel </small>
                                    </h3>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <ol class="breadcrumb pull-right">
                                    <li class="breadcrumb-item">
                                        <a>
                                            <i data-feather="home"></i>
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item"> Main Data  </li>
                                    <li class="breadcrumb-item active"> Create main data </li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Container-fluid Ends-->

                                <!-- Container-fluid starts-->
                                <div class="container-fluid" >
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="card tab2-card">
                                                <div class="card-body">
                                                    <div class="tab-content" id="myTabContent">
                                                        <div class="tab-pane fade active show" id="account" role="tabpanel"
                                                            aria-labelledby="account-tab">
                                                            <form 
                                                            [formGroup]="form"
                                                            class="needs-validation user-add">
                                                                <div class="form-group row">
                                                                    <label 
                                                                        class="col-xl-3 col-md-4"><span>*</span>  Variable name </label>
                                                                    <div class="col-xl-8 col-md-7">
                                                                        <input 
                                                                            formControlName="variableName" 
                                                                            class="form-control" type="text">
                                                                    </div>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                    <div class="tab-group pull-right">
                                                        <!-- -->
                                                        <button   
                                                        (click)="submit()"
                                                        type="button" class="btn btn-primary d-block tab"> Save </button>
                                                        <a [routerLink]="['/'+ 'en' +'/list-main-data']" class="tab">
                                                            <button type="button" class="btn btn-primary d-block"> Cancel </button>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Container-fluid Ends-->