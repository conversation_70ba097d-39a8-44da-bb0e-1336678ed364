import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'replace'
})
export class ReplacePipe implements PipeTransform {

  transform(value: any, strToReplace: string, replacementStr: string): number {

    value = value.toString()

    if(!value || ! strToReplace || ! replacementStr)
    {
      Math.floor(value)
      return value;
    }
    Math.floor(value)
    return value.replace(new RegExp(strToReplace, 'g'), replacementStr);
  }

}
