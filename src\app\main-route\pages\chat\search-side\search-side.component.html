
  <div class="disp-flex">
    <div style="height: 4em; max-width: 100%" class="search-bar">
      <input
        (keyup)="searchUser($event)"
        style="background-color: var(--app-container); border-radius: 15px"
        type="text"
        placeholder="Search"
      />
    </div>
  </div>
  <div
    *ngIf="searchSideActivate"
    style="
      background-color: var(--app-container);
      flex-grow: 1;
      border-radius: 15px;
      padding: 1em 2em;
      overflow-y: scroll;
    ">
    <div
      style="display: flex; flex-direction: column; gap: 2em; height: 100%" >
      <ng-container *ngIf="searchLoading">
          <li
          *ngFor="let i of [].constructor(15)"
          style="
              list-style: none; /* remove the bullets/dots from the list */
              display: flex;
              flex-direction: column;
              height: 85px;
              width: 100%;
          "
          >
          <div
              style="display: flex; gap: 5px; width: 100%; align-items: center"
          >
              <span
              class="skeleton1"
              style="height: 40px; width: 40px; border-radius: 50%"
              ></span>
              <div style="display: grid; gap: 4px">
              <span class="skeleton2" style="height: 14px; width: 85px"></span>
              <span class="skeleton2" style="height: 14px; width: 100px"></span>
              </div>
              <span
              class="skeleton2"
              style="
                  margin-left: auto;
                  height: 22px;
                  width: 63px;
                  border-radius: 5px;
              "
              ></span>
          </div>
          </li>
      </ng-container>
      <ng-container *ngIf="resultSearch?.length && !searchLoading">
          <li
          style="
              list-style: none; /* remove the bullets/dots from the list */
              display: flex;
              flex-direction: column;
              gap: 5px;
          "
          *ngFor="let user of resultSearch">
          <div
              (click)="selectUser(user)"
              style="display: flex; gap: 5px; width: 100%; align-items: center"
          >
          <div
          style="position: relative;">
              <app-avatar-photo
              [buffer]="user?.photoProfile?.data"
              [userId]="user?._id"
              style="cursor: pointer"
              [circleColor]="user?.photoColor"
              [name]="user?.name"
              [surname]="user?.surname"
              [class]="'userImgBloggers'"
              [classAvatarInitials]="'initialsClass'"
              ></app-avatar-photo>
                <img
                *ngIf="user?.isVerified"
                  style="
                    height: 15px;
                    width: 15px;
                    position: absolute;
                    bottom: 0;
                    right: 0;"
                  src="/assets/icons/fiberVerified.svg"
                  alt=""
                />
                <img
                *ngIf="user?.untrusted"
                  style="
                    height: 15px;
                    width: 15px;
                    position: absolute;
                    bottom: 0;
                    right: 0;"
                  src="/assets/icons/fiberUnverified.svg"
                  alt=""
                />
          </div>
              <div style="max-width: 200px">
                  <span>{{ user?.name }} {{ user?.surname }}</span>
              </div>
          </div>
          </li>
      </ng-container>

    </div>
  </div>



