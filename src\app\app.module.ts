import { NgxSliderModule } from 'ngx-slider-v2';
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ToastrModule } from 'ngx-toastr';
import { LoadingComponent } from './main-route/pages/loading/loading.component';
import { NgxLoadingModule } from 'ngx-loading';
import { SharedModule } from './shared/shared.module';
import { OnlineStatusModule } from 'ngx-online-status';
import { OwlDateTimeModule, OwlNativeDateTimeModule } from 'ng-pick-datetime';
import { AuthComponent } from './main-route/pages/login/auth/auth.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';

import { MatStepperModule } from '@angular/material/stepper';

@NgModule({
  declarations: [AppComponent, AuthComponent, LoadingComponent],
  imports: [
    OnlineStatusModule,
    BrowserModule,
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    AppRoutingModule,
    NgxLoadingModule.forRoot({}),
    HttpClientModule,
    BrowserAnimationsModule,
    HttpClientModule,
    OwlDateTimeModule,
    OwlNativeDateTimeModule,
    ToastrModule.forRoot({
      timeOut: 3000,
      progressBar: false,
      enableHtml: true,
    }),
    NgbModule,
    MatStepperModule,
    NgxSliderModule,
  ],
  providers: [],
  bootstrap: [AppComponent],
})
export class AppModule {}
