const express = require("express");
const promotionController = require("./../controllers/promotionController");

const router = express.Router();
// var multipart = require('connect-multiparty');
// var multipartMiddleware = multipart();

// Protect all routes after this middleware
// router.use(authController.protect);

//

router.route("/").post(
  // multipartMiddleware,
  // promotionController.resizePhoto,
  promotionController.create
);

router.route("/getAll").post(promotionController.getAll);

router.route("/getFilteredUsers").post(promotionController.getFilteredUsers);

router
  .route("/changeActiveStatus/:id")
  .patch(promotionController.changeActiveStatus);

router
  .route("/changePauseStatus/:id")
  .patch(promotionController.changePauseStatus);

router.route("/getPromotionData/:id").get(promotionController.getPromotionData);

router
  .route("/getAllTargetedUsers/:id")
  .post(promotionController.getAllTargetedUsers);

// router.use(authController.restrictTo('admin'));

module.exports = router;
