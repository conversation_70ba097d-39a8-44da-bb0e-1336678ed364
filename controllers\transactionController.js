const catchAsync = require('./../utils/catchAsync');
const Transaction = require('./../models/transactionModel');

exports.create = catchAsync(async (req, res, next) => {
    
const doc = await Transaction.create(req.body);

    res.status(201).json({
    status: 'success',
    data: {
        data: doc
    }
    });

});


exports.getAll = catchAsync(async (req, res, next) => {
    let resultsPerPage =  req.body.resultsPerPage  >= 5 ? req.body.resultsPerPage : 5;
    let page = req.body.page >= 1 ? req.body.page : 1;
    page = page - 1

    let data =
     await Transaction.find()
     .populate({
      path: 'senderId',
      select: ['id','_id','name','surname','isVerified','photoColor','untrusted','earnCount','likesCount','commentsCount','postCount','email']
    })
    .populate({
      path: 'receiverId',
      select: ['id','_id','name','surname','isVerified','photoColor','untrusted','earnCount','likesCount','commentsCount','postCount','email']
    })
    .limit( resultsPerPage)
    .skip( resultsPerPage * page)
    .sort({ _id: -1 })
    
    let count = await Transaction.countDocuments()

    res.status(200).json({
      status: 'success',
      data: {
        data: data,
        count: count
      }
    });
  
});





exports.searchTransaction = catchAsync(async (req, res, next) => {
  if(req.body.searchText !== ''){
      let resultsPerPage =   req.body.resultsPerPage 
      let page = req.body.page 
      page = page - 1
      req.body.searchText = req.body.searchText.trim()

      let name = req.body.searchText.split(" ")[0]; 
      let surname = req.body.searchText.split(" ")[1] === undefined ? name : req.body.searchText.split(" ")[1]; 

      let nameRegex = name.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
      let surnameRegex = surname.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
      
      nameRegex = new RegExp(nameRegex, 'i') 
      surnameRegex = new RegExp(surnameRegex, 'i') 

      Transaction.find({
          $or: [
          { 'senderId.name':  { $regex: nameRegex } },
          { 'senderId.surname':  { $regex: surnameRegex } },
          { 'receiverId.name':  { $regex: nameRegex } },
          { 'receiverId.surname':  { $regex: surnameRegex } },
        ]})
        .populate({
          path: 'senderId',
          select: ['id','_id','name','surname','isVerified','photoColor','untrusted','earnCount','likesCount','commentsCount','postCount','email']
        })
        .populate({
          path: 'receiverId',
          select: ['id','_id','name','surname','isVerified','photoColor','untrusted','earnCount','likesCount','commentsCount','postCount','email']
        })
        .limit(resultsPerPage)
        .skip(resultsPerPage * page)
        .sort({ _id: -1 })
        .then(  async result => {
          // console.log("nameRegex: ",result)

          // console.log("result: ",result)
          let countList = await Transaction.find({
            $or: [
              { 'senderId.name':  { $regex: nameRegex } },
              { 'senderId.surname':  { $regex: surnameRegex } },
              { 'receiverId.name':  { $regex: nameRegex } },
              { 'receiverId.surname':  { $regex: surnameRegex } },
          ]},{senderId:0,receiverId:0})
          let count = countList.length
          res.status(200).json({
            status: 'success',
            data: {
              data: result,
              count: count
            }
          });
        })


    }else{
      res.status(200).json({
        status: 'success',
        resultSearch: result 
      });
    }
});