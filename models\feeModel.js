// review / rating / createdAt / ref to tour / ref to user
const mongoose = require('mongoose');

const feeSchema = new mongoose.Schema(
  {
    comment: {
      type: String,
      trim: true
    },
    post: {
      type: mongoose.Schema.ObjectId,
      ref: 'Post',
      required: [true, 'Comment must belong to a post.']
    },
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Comment must been created from a User!']
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  } 
);



const Fee = mongoose.model('Fee', feeSchema);

module.exports = Fee;
