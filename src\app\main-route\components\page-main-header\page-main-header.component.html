<div class="app-header-left">
  <img style="height: 50px" src="../assets/fiberLogo.svg" alt="" />
  <p
    style="
      overflow: visible;
      font-size: 30px;
      font-weight: 800;
      color: #32dbc6;
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI',
        Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue',
        sans-serif;
    "
    class="app-name no-wrap-1-line"
  >
    Fiber Al
  </p>
  <!-- <div class="search-wrapper m-l-auto">
    <input class="search-input" type="text" placeholder="Search" />
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      fill="none"
      stroke="currentColor"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      class="feather feather-search"
      viewBox="0 0 24 24"
    >
      <defs></defs>
      <circle cx="11" cy="11" r="8"></circle>
      <path d="M21 21l-4.35-4.35"></path>
    </svg>
  </div> -->
</div>
<div class="app-header-right">
  <!-- <button class="add-btn" title="Add New Project">
    <svg
      class="btn-icon"
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="3"
      stroke-linecap="round"
      stroke-linejoin="round"
      class="feather feather-plus"
    >
      <line x1="12" y1="5" x2="12" y2="19" />
      <line x1="5" y1="12" x2="19" y2="12" />
    </svg>
  </button> -->
  <button class="notification-btn">
    <!-- <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
      class="feather feather-bell"
    >
      <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" />
      <path d="M13.73 21a2 2 0 0 1-3.46 0" />
    </svg> -->
  </button>
  <button (click)="displayDropDown = !displayDropDown" class="profile-btn">
    <app-img
      *ngIf="user?.photoProfile?.data"
      [(buffer)]="user.photoProfile.data"
      [class]="''"
    ></app-img>
    <span style="font-size: 16px">{{ user?.name }} {{ user?.surname }}</span>
  </button>
  <div
    *ngIf="displayDropDown"
    style="
      position: absolute;
      top: 80%;
      right: 3%;
      background: var(--filter-reset);
      padding: 2em;
      z-index: 1;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      gap: 1em;
      z-index: 9999999;
    "
  >
    <div
      style="
        display: flex;
        align-items: center;
        gap: 1em;
        box-shadow: -2px 2px 2px #67aeb1;
        border-radius: 15px;
        padding: 0.5em;
      "
    >
      <app-img
        *ngIf="user?.photoProfile?.data"
        [(buffer)]="user.photoProfile.data"
        [class]="'profileImgModalTop'"
      ></app-img>

      <span style="font-size: 16px">{{ user?.name }} {{ user?.surname }}</span>
    </div>
    <div
      (click)="displayDropDown = !displayDropDown; signOut()"
      style="display: flex; align-items: center; gap: 1em"
    >
      <img
        style="height: 25px; width: 25px"
        src="/assets/icons/logout.svg"
        alt=""
      />
      <p style="font-size: 16px;margin: 0;">Logout</p>
    </div>
  </div>
</div>

<!-- <div class="nav-right col" >
    <ul class="nav-menus">
        <li>
            <a class="text-dark"  (click)="navigation.goBack()">
                <i data-feather="arrow-left-circle"></i>
            </a>
        </li>
        <li>
            <a class="text-dark"  (click)="refreshComponent()">
                <i data-feather="refresh-cw"></i>
            </a>
        </li>
        <li>
            <a class="text-dark"  (click)="navigation.goForward()">
                <i data-feather="arrow-right-circle"></i>
            </a>
        </li>
        <li class="onhover-dropdown">
            <a class="txt-dark">
                <h6>{{selectedLang.code  | uppercase }}&nbsp;&nbsp;&nbsp;</h6>
            </a>
            <ul class="language-dropdown onhover-show-div p-20">
                <li>
                    <a (click)="changeLang('en')" style="cursor: pointer;" >
                        <img width="21" height="19" src="assets/flags/en-100.png" >                                        
                        English
                    </a>
                </li>
            </ul>
        </li>


        <li class="onhover-dropdown">
            <div class="media align-items-center">
                <img *ngIf="!user?.photoProfile?.data" class="align-self-center pull-right img-50 blur-up lazyloaded"
                    src="assets/user2.jpg" alt="header-user">
                    <app-img
                    *ngIf="user?.photoProfile?.data"
                        [(buffer)]="user.photoProfile.data"
                        [class]="'align-self-center pull-right img-50 blur-up lazyloaded'"
                    ></app-img>
                <div class="dotted-animation">
                    <span class="animate-circle"></span>
                    <span class="main-circle"></span>
                </div>
            </div>
            <ul class="profile-dropdown onhover-show-div p-20 profile-dropdown-hover">
                <li>
                    <a (click)="redirectTo('/profile')">
                        <i data-feather="user"></i> My profile
                    </a>
                </li>
                <li>
                    <a (click)="signOut()">
                        <i data-feather="log-out"></i>Logout 
                    </a>
                </li>
            </ul>
        </li>
    </ul>
    <div class="d-lg-none mobile-toggle pull-right">
        <i data-feather="more-horizontal"></i>
    </div>
</div> -->
