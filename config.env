NODE_ENV = development
PORT = 8000
DATABASE = mongodb://fiber_usr:<password>@************:27017/fiber_db?retryWrites=true&w=majority
DATABASE_PASSWORD = Fiber.AL
BUCKET_NAME = back-sight
AWS_ACCESS_KEY_ID =  ********************
AWS_SECRET_ACCESS_KEY = sSQB8MseqTXJYPbUu730K+fVRrxWd2Bjm73mb54w
JWT_SECRET = back-app-sight-app-token-app-password
JWT_EXPIRES_IN = 90d
JWT_COOKIE_EXPIRES_IN = 90
SENDGRID_USERNAME = <EMAIL>
SENDGRID_PASSWORD = *********************************************************************
EMAIL_HOST = smtp.mailtrap.io
EMAIL_PORT = 25
EMAIL_USERNAME = a124458539192a
EMAIL_PASSWORD = 1e3caf48074eba
EMAIL_FROM =  <EMAIL> 
WEB_URL = http://localhost:4200/ 