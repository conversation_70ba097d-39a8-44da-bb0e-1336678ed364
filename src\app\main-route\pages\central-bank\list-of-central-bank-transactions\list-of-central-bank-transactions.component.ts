import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Component, OnInit, TemplateRef } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ngxLoadingAnimationTypes } from 'ngx-loading';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { CentralBankTransactionsService } from 'src/app/shared/services/central-bank-transactions.service';
import { LoadJsService } from 'src/app/shared/services/load-js.service';
import { MainDataService } from 'src/app/shared/services/main-data.service';
import { AuthService } from 'src/app/shared/services/auth.service';

@Component({
  selector: 'app-list-of-central-bank-transactions',
  templateUrl: './list-of-central-bank-transactions.component.html',
  styleUrls: ['./list-of-central-bank-transactions.component.css'],
})
export class ListOfCentralBankTransactionsComponent implements OnInit {
  lang = 'en';
  searchForm;

  loading = false;

  data: any[] = [];

  public ngxLoadingAnimationTypes = ngxLoadingAnimationTypes;
  public loadingTemplate: TemplateRef<any>;

  startLoading = false;
  public pageNo: number = 1;
  public pageNoTotal: number = 1;
  count: number = 0;
  resultsPerPage: number = 4;


  resultSearchPageNumber = 1;
  timeout: any = null;
  resultSearch: any = null;
  searchText = '';
  searchLoading = false

  me: User;
  transactionSelected = null

  optionSelected = 'Selling Credits'
  userSelected : any = null

  centralBankTotalAmount = 0
  theMinisterOfFinanceTotalaAmount = 0


  creditsAmount = 0
  realMoneyAmount = 0
  realMoneyCurrency = 'EUR'

  constructor(
    private mainDataService: MainDataService,
    private centralBankTransactionsService: CentralBankTransactionsService,
    private formBuilder: UntypedFormBuilder,
    private toastrService: ToastrService,
    private authService: AuthService,
    public loadJsService: LoadJsService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private modalService: NgbModal,
  ) {
    this.searchForm = this.formBuilder.group({
      search: '',
    });
    this.authService.user.pipe().subscribe((appUser) => {
      this.me = appUser;
    });
  }

  ngOnInit(): void {
    this.loading = true;

    this.activatedRoute.params.subscribe(async (paramMap) => {
      if (paramMap['lang']) {
        this.lang = paramMap['lang'];
      }
    });

    this.activatedRoute.queryParams.subscribe((params) => {
      this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;
    });
    this.getBy('central-bank-total-amount')
    this.getAll();
  }



  getAll() {
    this.centralBankTransactionsService
      .getAll(this.pageNo, this.resultsPerPage)
      .subscribe(
        async (result) => {
          if (result.status == 'success') {
            this.count = result.data.count;
            this.data = result.data.data;
            this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
            this.loading = false;
            this.startLoading = false;
          }
        },
        (respond_error) => {
          this.toastrService.error(
            respond_error?.error.message,
            respond_error?.name
          );
        }
      );
  }

  ngOnDestroy() {
    this.loadJsService.removeScripts();
  }
  redirectTo(uri: string) {
    this.router.navigateByUrl(this.lang + uri).then(() => {
      this.loadJsService.removeScripts();
    });
  }

  openOptionsModal(optionsContent) {
    this.modalService.open(optionsContent, { centered: true });
  }

  openStatusChangeModal(statusContent) {
    this.modalService.open(statusContent, { centered: true });
  }

  openUserSearchModal(searchContent) {
    this.modalService.open(searchContent, { centered: true });
  }

  public setPage(page: number) {
    this.data = [];
    this.loading = true;
    this.router
      .navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: page },
        queryParamsHandling: 'merge', // preserve the existing query params in the route
        skipLocationChange: false, // do trigger navigation
      })
      .finally(() => {
        if(this.searchText === ''){
          this.getAll();
        }else{
          this.getAllResultSearch()
        }
        // this.viewScroller.setOffset([120, 120]);
        // this.viewScroller.scrollToAnchor('deals'); // Anchore Link
      });
  }
  resultsPerPageChanged(event){
    this.loading = true;
    this.startLoading = true;
    this.count =  1
    this.pageNoTotal = 1
    this.data = []
    this.resultsPerPage = Number(event)
    this.setPage(1)
  }




  searchUser(searchText) {
    clearTimeout(this.timeout);
    var $this = this;
    this.timeout = setTimeout(function () {
      if (searchText.keyCode != 13) {
        $this.loading = true
        $this.searchSocket(searchText.target.value);
      }else{
        $this.loading = true
        $this.getAll()
      }
    }, 1000);
  } 
  public searchSocket(searchText) {
    this.searchText = searchText.toString();
    if(this.searchText == ''){
      this.loading = true
      this.setPage(1)
      return
    }
    this.data = []
    this.setPage(1)
  }
  getAllResultSearch() {
    if(this.searchText == '') return
    this.loading = true;
    this.count =  1
    this.pageNoTotal = 1
    this.data = []
    this.centralBankTransactionsService.search(this.searchText, this.me._id,this.pageNo,this.resultsPerPage).subscribe(
      (result) => {
        if (result.status == 'success') {
          this.count = result.data.count;
          this.data = result.data.data;
          this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
          this.loading = false;
          this.startLoading = false;
        }
      },
      (respond_error) => {
        this.loading = false;
        this.startLoading = false;
        // this.toastrService.error(
        //   respond_error?.error.message,
        //   respond_error?.name
        // );
      }
    );
  }


  selectTransaction(data){
    this.transactionSelected = data
  }

  selectChanged(event){

    this.creditsAmount = 0
    this.realMoneyAmount = 0
    this.realMoneyCurrency = 'EUR'
    this.optionSelected = event
    this.userSelected = null
    if( event === 'Tax Collection'){
      this.getBy('the-minister-of-finance-total-amount')
    }
    if( event === 'Loan to Central Bank'){
      this.getBy('central-bank-total-amount')
    }
  }

  getBy(variableName){
    this.mainDataService.getByVariableName(variableName).subscribe( 
      async result =>{
        if(result.status == "success"){
            if(result?.data?.mainData[0]?.variableName == 'central-bank-total-amount'){
              this.centralBankTotalAmount = result?.data?.mainData[0]?.value
            }

            if(result?.data?.mainData[0]?.variableName == 'the-minister-of-finance-total-amount'){
              this.theMinisterOfFinanceTotalaAmount = result?.data?.mainData[0]?.value
            }
            // this.user = result.data.data
        }
      },
      respond_error => {
       this.toastrService.error(
         respond_error?.error.message
          , respond_error?.name);         
       }
    )
  }


  create(){

    if(this.creditsAmount === 0 || this.creditsAmount === null){
      this.toastrService.error( "Transaction value need to be more than 0 coins" );
      this.creditsAmount = 0
      return
    }

    let inflowCredits = false
    let inflowRealMoney = false
    let outflowCredits = false
    let outflowRealMoney = false
    let type = ''
    let reason = ''

    if(this.optionSelected == 'Loan to Central Bank'){
      inflowCredits = true
      inflowRealMoney = true
      type = 'loan-to-central-bank'
      reason = 'Central Bank is getting loan of '+ this.creditsAmount +' credits and '+this.realMoneyAmount+' '+this.realMoneyCurrency+' .Executed by ' + this.me.name + ' ' + this.me.surname
    } 
    if( this.optionSelected == 'Tax Collection'){
      if(this.theMinisterOfFinanceTotalaAmount < this.creditsAmount){
          this.toastrService.error( "The Minister Of Finance does not have actual amount of credits for moment" );
          return
      }
      inflowCredits = true
      type = 'tax-collection'
      reason = 'The Minister Of Finance transfer '+ this.creditsAmount +'credits of their budget (tax-collection) to Central Bank. Executed by' + this.me.name+ ' ' + this.me.surname
    } 
    if( this.optionSelected ==  'Selling Credits'){
      if(this.centralBankTotalAmount < this.creditsAmount){
        this.toastrService.error( "Central Bank does not have actual amount of credits for moment" );
        return
      }
      inflowRealMoney = true
      outflowCredits = true
      type = 'selling-credits'
      reason = 'Central Bank is selling '+ this.creditsAmount +' credits to '+this.userSelected.name+' '+this.userSelected.surname +' for '+this.realMoneyAmount+' '+this.realMoneyCurrency+' .Executed by ' + this.me.name + ' ' + this.me.surname
    } 

    this.centralBankTransactionsService.create(   
      {
        total:this.creditsAmount,
        reason:reason,
        isSenderFiberBank:true,
        isReceiverFiberBank:true
      },
      {   
        reason:reason,
        inflowCredits: inflowCredits, 
        inflowRealMoney: inflowRealMoney, 
        outflowCredits: outflowCredits,  
        outflowRealMoney: outflowRealMoney,  
        creditsAmount: this.creditsAmount,
        realMoneyAmount: this.realMoneyAmount,
        realMoneyCurrency: this.realMoneyCurrency,
        total:this.creditsAmount,
        userSelected:this.userSelected,
        type : type,
        createdBy: this.me._id
      }
    ).subscribe( respond  => {
            if( respond.status === "success"){
              if(type === 'loan-to-central-bank'){
                this.centralBankTotalAmount = this.centralBankTotalAmount + this.creditsAmount
              }
              if(type === 'selling-credits'){
                this.centralBankTotalAmount = this.centralBankTotalAmount - this.creditsAmount
              }
              respond.centralBank.createdBy = this.me
              this.data = [respond.centralBank,...this.data]
              this.toastrService.success( "Successfully created" );
            }
        },
        respond_error => {
          this.toastrService.error(
            respond_error?.error.message
             , respond_error?.name); 
        }

      ); 

  }

  async selectUser(user){
    // this.modalService.dismissAll()
    this.userSelected = user
  }
   
}
