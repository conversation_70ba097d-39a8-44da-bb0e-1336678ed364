const PromotionAvailable = require('./../models/promotionAvailableModel');
const catchAsync = require('./../utils/catchAsync');
const AppError = require('./../utils/appError');
const factory = require('./handlerFactory');

exports.changeActiveStatus = catchAsync(async (req, res, next) => {
    // console.log("req.body: ",req.body)
    let user = null
     user = await PromotionAvailable.findByIdAndUpdate(req.body._id, {isActive : req.body.isActive}, {new: true});
    if(user !== null){
      res.status(200).json({
        status: 'success',
        data: user
      });
    }
  });

  
exports.create = factory.createOne(PromotionAvailable);
