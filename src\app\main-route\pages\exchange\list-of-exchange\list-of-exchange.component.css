.exchange-section {
  display: grid;
  grid-template-columns: 1fr 3fr;
  padding: 32px !important;
  max-height: 1000px;
}
.exchange-tiles-Wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 1em;
}

.exchange-tile {
  height: 50%;
  background-color: var(--filter-reset);
  border-radius: 20px;
  display: grid;
  padding: 2em;

  /* box */
}
.exchange-animation {
  position: absolute;
  height: 20%;
  width: 20%;
  max-height: 80px;
  max-width: 80px;
  min-height: 50px;
  min-width: 50px;
  left: 50%;
  transform: translateX(-50%);
}
.exchange-animation > img {
  height: 100%;
  width: 100%;
  object-fit: contain;
}
.credit-to-euro {
  position: absolute;
  top: 101%;
  margin: 0;
  text-align: center;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0.5;
}
.exchange-button {
  background: var(--action-color);
  font-size: 20px;
  padding: 0.5em 2em;
  width: 100%;
  justify-content: center;
  display: flex;
  align-items: center;
  border: 1px solid var(--action-color);
  border-radius: 10px;
  margin: 0.3em 0 0;
}

.creditsInputWrapper {
  border-radius: 20px;
  background-color: var(--search-area-bg);
  padding-right: 12px;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 60%;
  max-width: 480px;
  color: var(--light-font);
  box-shadow: 0 2px 6px 0 rgba(136, 148, 171, 0.2),
    0 24px 20px -24px rgba(71, 82, 107, 0.1);
  overflow: hidden;
}
.dark .creditsInputWrapper {
  box-shadow: none;
}
.creditsInput {
  border: none;
  flex: 1;
  outline: none;
  height: 100%;
  padding: 0 20px;
  font-size: 20px;
  background-color: var(--search-area-bg);
  color: var(--main-color);
  font-weight: 600;
  width: 100%;
}
.creditsInput:placeholder {
  color: var(--main-color);
  opacity: 0.6;
}

@media screen and (max-width: 1248px) {
  .showingInfoWrapper {
    display: none !important;
  }
  .list-number {
    justify-content: center !important;
  }
}
@media screen and (max-width: 992px) {
  .creditsInputWrapper {
    width: 100%;
  }
  .pager__item {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }
  .pager__item.active,
  .pager__item:first-of-type,
  .pager__item:last-of-type,
  .pager__item:nth-of-type(2),
  .pager__item:nth-last-of-type(2) {
    position: initial;
    top: initial;
    left: initial;
  }
  .pager__item.active + li {
    position: initial;
    top: initial;
    left: initial;
  }
}

@media screen and (max-width: 747px) {
  .exchange-section {
    grid-template-columns: 1fr !important;

    grid-template-rows: 1fr 1fr !important;
    overflow: scroll;
  }
}
p,
span,
div {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
