import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { AppVersionService } from 'src/app/shared/services/app-version.service';
import { AuthService } from 'src/app/shared/services/auth.service';

@Component({
  selector: 'app-list-version',
  templateUrl: './list-version.component.html',
  styleUrls: ['./list-version.component.css']
})
export class ListVersionComponent implements OnInit {

  loading = false
  lang = 'en'
  user: User;
  data = []

  startLoading = false
  public pageNo: number = 1;
  public pageNoTotal: number = 1;
  count : number = 0;
  resultsPerPage : number = 5;

  constructor(
    private activatedRoute: ActivatedRoute,
    private authService: AuthService,
    private toastrService: ToastrService,
    private appVersionService: AppVersionService,
    private modalService: NgbModal,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.activatedRoute.queryParams.subscribe(params => {
      this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;
    })

    this.getAll()
  }

  openDelRoleModal(delRoleContent) {
    this.modalService.open(delRoleContent, { centered: true });
  }
  openAddRoleModal(addRoleContent) {
    this.modalService.open(addRoleContent, { centered: true });
  }

  getAll(){
    this.loading = true
    this.appVersionService.getAllVersions(
      this.pageNo,
      this.resultsPerPage
    ).subscribe( 
     async result =>{
        if(result.status == "success"){
          console.log("result: ",result)
              this.data = result.data.data
              this.count = result.data.count
              this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1 
              this.loading = false
          }

      },
      respond_error => {
       this.toastrService.error(
         respond_error?.error.message
          , respond_error?.name);         
       }
    )
  }
  public setPage(page: number) {
    this.data = []
    this.startLoading = true
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { page: page },
      queryParamsHandling: 'merge', // preserve the existing query params in the route
      skipLocationChange: false  // do trigger navigation
    }).finally(() => {
      this.getAll()
      // this.viewScroller.setOffset([120, 120]);
      // this.viewScroller.scrollToAnchor('deals'); // Anchore Link
    });
  }
  resultsPerPageChanged(event){
    this.loading = true;
    this.startLoading = true;
    this.count =  1
    this.pageNoTotal = 1
    this.data = []
    this.resultsPerPage = Number(event)
    this.setPage(1)
  }

  redirectTo(uri:string){
    this.router.navigateByUrl(this.lang+ uri)
      .then(() =>{});
  }

}
