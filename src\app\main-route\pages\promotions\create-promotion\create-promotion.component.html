<!-- Container-fluid starts-->
<div class="container-fluid">
  <div class="page-header">
    <div class="row">
      <div class="col-lg-6">
        <div class="page-header-left">
          <h3>
            Create promotion
            <small> Fiber Admin Panel </small>
          </h3>
        </div>
      </div>
      <div class="col-lg-6">
        <ol class="breadcrumb pull-right">
          <li class="breadcrumb-item">
            <a>
              <i data-feather="home"></i>
            </a>
          </li>
          <li class="breadcrumb-item">Pormotions</li>
          <li class="breadcrumb-item active">Create promotion</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<ngx-loading
  [(show)]="loading"
  [config]="{
    animationType: ngxLoadingAnimationTypes.circleSwish,
    primaryColour: '#ffffff',
    backdropBorderRadius: '3px'
  }"
></ngx-loading>

<div class="container-fluid">
  <div class="row">
    <div class="col-sm-12">
      <div class="card tab2-card">
        <div class="card-body">
          <ul class="nav nav-tabs tab-coupon" id="myTab" role="tablist">
            <li (click)="activeTab = 'promotionDetails'" class="nav-item">
              <a
                [ngClass]="
                  activeTab === 'promotionDetails' ? 'active show' : ''
                "
                class="nav-link"
              >
                Promotion Details
              </a>
            </li>
            <li (click)="activeTab = 'filters'" class="nav-item">
              <a
                [ngClass]="activeTab === 'filters' ? 'active show' : ''"
                class="nav-link"
              >
                Filters
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

<div *ngIf="activeTab === 'filters'" class="container-fluid">
  <div class="row product-adding">
    <div class="col-xl-6">
      <div class="card">
        <div class="card-header">
          <h5>Filters</h5>
        </div>

        <div class="card-body">
          <div class="digital-add needs-validation">
            <div class="form-group">
              <label class="col-form-label">
                <span>*</span> Followers Count
              </label>
              <div
                class="m-checkbox-inline mb-0 custom-radio-ml d-flex radio-animated"
              >
                <label class="d-block" for="edo-ani">
                  <input
                    name="followersCountActive"
                    [value]="true"
                    [(ngModel)]="followersCountActive"
                    class="radio_animated"
                    type="radio"
                  />
                  Enable
                </label>
                <label class="d-block" for="edo-ani1">
                  <input
                    name="followersCountActive"
                    (change)="
                      followersCountMin = null;
                      followersCountMax = null;
                      getUserFiltered('', 'followersCountMin');
                      getUserFiltered('', 'followersCountMax')
                    "
                    [value]="false"
                    [(ngModel)]="followersCountActive"
                    class="radio_animated"
                    type="radio"
                  />
                  Disable
                </label>
              </div>
              <div
                *ngIf="followersCountActive"
                style="display: flex; gap: 2em; font-size: 30px"
                class="form-group"
              >
                <input
                  [(ngModel)]="followersCountMin"
                  (keyup)="search($event, 'followersCountMin')"
                  class="form-control"
                  type="number"
                  placeholder="From"
                />
                -
                <input
                  [(ngModel)]="followersCountMax"
                  (keyup)="search($event, 'followersCountMax')"
                  class="form-control"
                  type="number"
                  placeholder="To"
                />
              </div>
            </div>

            <div class="form-group">
              <label class="col-form-label">
                <span>*</span> Following Count
              </label>
              <div
                class="m-checkbox-inline mb-0 custom-radio-ml d-flex radio-animated"
              >
                <label class="d-block">
                  <input
                    name="followingCountActive"
                    [value]="true"
                    [(ngModel)]="followingCountActive"
                    class="radio_animated"
                    type="radio"
                  />
                  Enable
                </label>
                <label class="d-block">
                  <input
                    name="followingCountActive"
                    (change)="
                      followingCountMin = null;
                      followingCountMax = null;
                      getUserFiltered('', 'followingCountMin');
                      getUserFiltered('', 'followingCountMax')
                    "
                    [value]="false"
                    [(ngModel)]="followingCountActive"
                    class="radio_animated"
                    type="radio"
                  />
                  Disable
                </label>
              </div>
              <div
                *ngIf="followingCountActive"
                style="display: flex; gap: 2em; font-size: 30px"
                class="form-group"
              >
                <input
                  class="form-control"
                  [(ngModel)]="followingCountMin"
                  (keyup)="search($event, 'followingCountMin')"
                  type="number"
                  placeholder="From"
                />
                -
                <input
                  class="form-control"
                  [(ngModel)]="followingCountMax"
                  (keyup)="search($event, 'followingCountMax')"
                  type="number"
                  placeholder="To"
                />
              </div>
            </div>

            <div class="form-group">
              <label class="col-form-label"> <span>*</span> Post Count </label>
              <div
                class="m-checkbox-inline mb-0 custom-radio-ml d-flex radio-animated"
              >
                <label class="d-block">
                  <input
                    name="postCountActive"
                    [value]="true"
                    [(ngModel)]="postCountActive"
                    class="radio_animated"
                    type="radio"
                  />
                  Enable
                </label>
                <label class="d-block">
                  <input
                    name="postCountActive"
                    (change)="
                      postCountMin = null;
                      postCountMax = null;
                      getUserFiltered('', 'postCountMin');
                      getUserFiltered('', 'postCountMax')
                    "
                    [value]="false"
                    [(ngModel)]="postCountActive"
                    class="radio_animated"
                    type="radio"
                  />
                  Disable
                </label>
              </div>
              <div
                *ngIf="postCountActive"
                style="display: flex; gap: 2em; font-size: 30px"
                class="form-group"
              >
                <input
                  [(ngModel)]="postCountMin"
                  (keyup)="search($event, 'postCountMin')"
                  class="form-control"
                  type="number"
                  placeholder="From"
                />
                -
                <input
                  [(ngModel)]="postCountMax"
                  (keyup)="search($event, 'postCountMax')"
                  class="form-control"
                  type="number"
                  placeholder="To"
                />
              </div>
            </div>

            <div class="form-group">
              <label class="col-form-label"> <span>*</span> Earn Count </label>
              <div
                class="m-checkbox-inline mb-0 custom-radio-ml d-flex radio-animated"
              >
                <label class="d-block" for="edo-ani">
                  <input
                    name="earnCountActive"
                    [value]="true"
                    [(ngModel)]="earnCountActive"
                    class="radio_animated"
                    type="radio"
                  />
                  Enable
                </label>
                <label class="d-block" for="edo-ani1">
                  <input
                    name="earnCountActive"
                    (change)="
                      earnCountMin = null;
                      earnCountMax = null;
                      getUserFiltered('', 'earnCountMin');
                      getUserFiltered('', 'earnCountMax')
                    "
                    [value]="false"
                    [(ngModel)]="earnCountActive"
                    class="radio_animated"
                    type="radio"
                  />
                  Disable
                </label>
              </div>
              <div
                *ngIf="earnCountActive"
                style="display: flex; gap: 2em; font-size: 30px"
                class="form-group"
              >
                <input
                  [(ngModel)]="earnCountMin"
                  (keyup)="search($event, 'earnCountMin')"
                  class="form-control"
                  type="number"
                  placeholder="From"
                />
                -
                <input
                  [(ngModel)]="earnCountMax"
                  (keyup)="search($event, 'earnCountMax')"
                  class="form-control"
                  type="number"
                  placeholder="To"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-6">
      <!-- <div class="card">
                                <div class="card-header">
                                    <h5>Sharing Options</h5>
                                </div>
                                <div class="card-body">
                                    <div class="digital-add needs-validation">
                                        <div class="form-group">
                                            <label class="col-form-label categories-basic">
                                                <span>*</span>
                                                User is allowed to share
                                            </label>
                                            <select 
                                            [(ngModel)]="userIsAllowedToShare"
                                            class="custom-select form-control">
                                                <option value="once">Once</option>
                                                <option value="daily">Daily</option>
                                                <option value="weekly">Weekly</option>
                                                <option value="monthly">Monthly</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div> -->
      <div class="card">
        <div class="card-header">
          <h5>Specific users</h5>
        </div>
        <div class="card-body">
          <div class="digital-add needs-validation">
            <div class="form-group">
              <label class="col-form-label pt-0">
                <span>*</span>
                Total users with filter selected</label
              >
              <input
                value="{{ totalUsersWithFilter - checkboxFalseUsersId.length }}"
                class="form-control"
                disabled="true"
                type="number"
              />
            </div>
            <div class="form-group">
              <label class="col-form-label pt-0">
                <span>*</span>
                Total users with filter</label
              >
              <input
                [(ngModel)]="totalUsersWithFilter"
                class="form-control"
                disabled="true"
                type="number"
              />
            </div>
            <!-- <div class="form-group">
                                            <label 
                                             class="col-form-label pt-0"> 
                                             <span>*</span>
                                             Search user</label>
                                            <input 
                                            class="form-control" 
                                            id="validationCustom05" 
                                            type="text">
                                        </div> -->
            <div class="form-group mb-0">
              <div class="product-buttons">
                <button
                  (click)="openModal('custom-modal-users')"
                  type="button"
                  class="btn btn-primary"
                >
                  Show more
                </button>
                <!-- <button type="button" class="btn btn-light">Discard</button> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div *ngIf="activeTab === 'promotionDetails'" class="container-fluid">
  <div class="row">
    <div class="col-sm-12">
      <div class="card tab2-card">
        <div class="card-body">
          <div class="tab-content">
            <div
              class="tab-pane fade active show"
              aria-labelledby="account-tab"
            >
              <div class="needs-validation user-add">
                <h4>Promotion Details</h4>
                <div class="form-group row">
                  <div class="col-md-2"></div>
                  <div class="col-md-8">
                    <input
                      #fileInput
                      accept="image/*"
                      type="file"
                      (change)="createFormData($event)"
                    />
                    <div *ngIf="img" class="dropzone dz-clickable">
                      <div
                        [style.background]="'url(' + img + ')'"
                        [style.background-size]="'cover'"
                        [style.background-repeat]="'no-repeat'"
                        [style.background-position]="'center center'"
                        class="trimi"
                      >
                        <div
                          style="margin-top: 20%"
                          class="dz-preview dz-processing dz-error dz-complete dz-image-preview"
                        >
                          <div class="dz-image"></div>
                          <div class="dz-details">
                            <div class="dz-size">
                              <span
                                ><strong>{{ imageSize }}</strong> MB</span
                              >
                            </div>
                            <div class="dz-filename">
                              <span>{{ imageName }}</span>
                            </div>
                          </div>
                          <div class="dz-progress">
                            <span class="dz-upload"></span>
                          </div>
                          <div class="dz-error-message">
                            <span>{{ imageName }}</span>
                          </div>
                          <div
                            class="dz-error-mark"
                            (click)="
                              img = null;
                              selectedFile = [];
                              fileInput.value = null
                            "
                          >
                            <i class="fa fa-times-circle"></i>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div
                      *ngIf="!img"
                      (click)="fileInput.click()"
                      class="dropzone digits dz-clickable"
                    >
                      <div class="dz-message needsclick">
                        <i class="fa fa-cloud-upload"></i>
                        <h4 class="mb-0 f-w-600">
                          Drop files here or click to upload.
                        </h4>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="form-group row">
                  <label class="col-xl-3 col-md-4"
                    ><span>*</span> Cost per share
                  </label>
                  <div class="col-xl-8 col-md-7">
                    <input
                      [(ngModel)]="cost"
                      type="number"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="col-xl-3 col-md-4"
                    ><span>*</span> Allowed to share
                  </label>
                  <div class="col-xl-8 col-md-7">
                    <input
                      [(ngModel)]="allowedToShare"
                      type="number"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="col-xl-3 col-md-4"
                    ><span>*</span> Total cost
                  </label>
                  <div class="col-xl-8 col-md-7">
                    <input
                      [(ngModel)]="cost * allowedToShare"
                      disabled="true"
                      type="number"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="col-xl-3 col-md-4"
                    ><span>*</span> Link Url
                  </label>
                  <div class="col-xl-8 col-md-7">
                    <input [(ngModel)]="linkUrl" class="form-control" />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="col-xl-3 col-md-4"
                    ><span>*</span> Time Allowed to Share
                  </label>
                  <div class="col-xl-8 col-md-7">
                    <input
                      type="number"
                      placeholder="Minutes"
                      [(ngModel)]="timeAllowedToShareMinutes"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="col-xl-3 col-md-4"
                    ><span>*</span> Minutes differnce between users allowed to
                    share
                  </label>
                  <div class="col-xl-8 col-md-7">
                    <input
                      type="number"
                      placeholder="Minutes"
                      [(ngModel)]="minutesDiffernce"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="form-group mb-3 row">
                  <label for="validationCustom01" class="col-xl-3 col-sm-4 mb-0"
                    >Start to promote :</label
                  >
                  <div class="col-xl-8 col-sm-7">
                    <input
                      [(ngModel)]="startPromoteDateTime"
                      [owlDateTime]="dt1"
                      [owlDateTimeTrigger]="dt1"
                      placeholder="Date Time"
                    />
                    <owl-date-time #dt1></owl-date-time>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="tab-group pull-right">
            <button
              [class.disabled]="disableSubmit"
              (click)="submit()"
              type="button"
              class="btn btn-primary d-block tab"
            >
              Save
            </button>
            <a [routerLink]="['/' + lang + '/list-of-promotions']" class="tab">
              <button type="button" class="btn btn-primary d-block">
                Cancel
              </button>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Container-fluid Ends-->
