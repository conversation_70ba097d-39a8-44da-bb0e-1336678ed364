import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/services/auth.service';
import { GiftService } from 'src/app/shared/services/gift.service';
import { ImageService } from 'src/app/shared/services/image.service';
import { environment } from 'src/environments/environment';
const BACKEND_Image_URL = environment.imageUrl + '/post/getImage/';

@Component({
  selector: 'app-create-gift',
  templateUrl: './create-gift.component.html',
  styleUrls: ['./create-gift.component.css'],
})
export class CreateGiftComponent implements OnInit {
  thumbnailFile: File = null;
  thumbnail = null;

  thumbnailImageId = null;

  animationFile: File = null;
  animation = null;

  animationImageId = null;

  cost = 0;
  title = '';
  duration = 0;
  oldID = '';
  orderIndex = 0;

  oldThumbnail = null;
  oldAnimation = null;

  editCost = 0;
  editTitle = '';
  editDuration = 0;
  editOldID = '';
  editOrderIndex = 0;

  me = null;
  today = new Date();

  id = '';

  editMode = false;

  constructor(
    private router: Router,
    private toastrService: ToastrService,
    private giftService: GiftService,
    private imageService: ImageService,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute
  ) {
    this.authService.user.pipe().subscribe((appUser) => {
      this.me = appUser;
    });
  }

  ngOnInit(): void {
    this.activatedRoute.params.subscribe(async (paramMap) => {
      this.editMode = false;
      if (paramMap['id']) {
        this.id = paramMap['id'];
        this.getData(this.id);
      }
    });
  }

  async selectThumbnail(event) {
    console.log('thumbnailFile');
    if (event.target.files[0]) {
      this.thumbnailFile = event.target.files[0];
      let array = [];
      array.push(this.thumbnailFile);
      await this.uploadOriginalImage(array);
      var reader = new FileReader();
      reader.readAsDataURL(event.target.files[0]);
      reader.onload = (_event) => {
        this.thumbnail = reader.result;
      };
    }
  }

  async selectAnimation(event) {
    console.log('animationFile');
    if (event.target.files[0]) {
      this.animationFile = event.target.files[0];
      let array = [];
      array.push(this.animationFile);
      await this.uploadGifFile(array);
      var reader = new FileReader();
      reader.readAsDataURL(event.target.files[0]);
      reader.onload = (_event) => {
        this.animation = reader.result;
      };
    }
  }

  getData(id) {
    this.editMode = true;
    this.giftService.getData(id).subscribe(
      async (result) => {
        if (result.status == 'success') {
          console.log('result: ', result);

          this.editCost = result?.data?.cost;
          this.editTitle = result?.data?.title;
          this.editDuration = result?.data?.duration;
          this.editOldID = result?.data?.oldID;
          this.editOrderIndex = result?.data?.orderIndex;

          this.cost = result?.data?.cost;
          this.title = result?.data?.title;
          this.duration = result?.data?.duration;
          this.oldID = result?.data?.oldID;
          this.orderIndex = result?.data?.orderIndex;

          if (result?.data?.thumbnailId) {
            this.thumbnailImageId = result?.data?.thumbnailId;
            this.oldThumbnail = `${BACKEND_Image_URL}${result?.data?.thumbnailId.toString()}`;
            this.thumbnail = `${BACKEND_Image_URL}${result?.data?.thumbnailId.toString()}`;
          }
          if (result?.data?.animationId) {
            this.animationImageId = result?.data?.animationId;
            this.oldAnimation = `${BACKEND_Image_URL}${result?.data?.animationId.toString()}`;
            this.animation = `${BACKEND_Image_URL}${result?.data?.animationId.toString()}`;
          }

          // this.thumbnailFile  = null;
          // this.thumbnail = null;

          // this.animationFile = null;
          // this.animation = null;
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }

  getImageData(id, type) {
    if (type == 'thumbnail') {
      this.thumbnail = `${BACKEND_Image_URL}${id.toString()}`;
    }
    if (type == 'animation') {
      this.animation = `${BACKEND_Image_URL}${id.toString()}`;
    }
    // this.imageService
    // .get(this.postId)
    // .subscribe(
    //   (result) => {
    //     if (result.status == 'success') {
    //       this.postId = null
    //       this.buffer = result.data.image.data
    //       this.loadImage()
    //     }
    //   },
    //   (respond_error) => {
    //   }
    // );
  }

  async edit() {
    // if(
    //       this.thumbnail !==  this.oldThumbnail
    // ){
    //   let array = []
    //   array.push(this.thumbnailFile)
    //   await this.uploadOriginalImage(array)
    // }
    // if(
    //     this.animation !== this.oldAnimation
    // ){
    //   let array = []
    //   array.push(this.animationFile)
    //   await this.uploadGifFile(array)
    // }
    if (
      this.thumbnail !== this.oldThumbnail ||
      this.animation !== this.oldAnimation ||
      this.editCost !== this.cost ||
      this.editTitle !== this.title ||
      this.editDuration !== this.duration ||
      this.editOldID !== this.oldID ||
      this.editOrderIndex !== this.orderIndex
    ) {
      this.giftService
        .edit(this.id, {
          thumbnailId: this.thumbnailImageId,
          animationId: this.animationImageId,
          cost: this.cost,
          title: this.title,
          duration: this.duration,
          oldID: this.oldID,
          orderIndex: this.orderIndex,
          createdBy: this.me?._id,
        })
        .subscribe(
          async (result) => {
            if (result.status == 'success') {
              this.thumbnailImageId = null;
              this.animationImageId = null;

              this.editCost = 0;
              this.editTitle = '';
              this.editDuration = 0;
              this.editOldID = '';
              this.editOrderIndex = 0;

              this.cost = 0;
              this.title = '';
              this.duration = 0;
              this.oldID = '';
              this.orderIndex = 0;

              // this.thumbnailFile  = null;
              // this.thumbnail = null;

              // this.animationFile = null;
              // this.animation = null;

              this.redirectTo('list-gifts');
            }
          },
          (respond_error) => {
            this.toastrService.error(
              respond_error?.error.message,
              respond_error?.name
            );
          }
        );
    }
  }

  create() {
    this.giftService
      .create({
        cost: this.cost,
        title: this.title,
        duration: this.duration,
        oldID: this.oldID,
        orderIndex: this.orderIndex,
        createdBy: this.me?._id,
      })
      .subscribe(
        async (result) => {
          if (result.status == 'success') {
            // this.count = result.data.count;
            // this.data = result.data.data;
            // this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
            this.cost = 0;
            this.title = '';
            this.duration = 0;
            this.oldID = '';
            this.orderIndex = 0;
            this.thumbnailFile = null;
            this.thumbnail = null;

            this.animationFile = null;
            this.animation = null;

            this.redirectTo('list-gifts');
          }
        },
        (respond_error) => {
          this.toastrService.error(
            respond_error?.error.message,
            respond_error?.name
          );
        }
      );
  }

  async uploadOriginalImage(files) {
    console.log('uploadOriginalImage: ');
    await this.imageService.uploadOriginalImage(files).subscribe(
      async (respond) => {
        console.log('uploadOriginalImage: ', respond);
        if ((respond.status = 'success')) {
          console.log('uploadOriginalImage: ', respond.data._id.toString());
          this.thumbnail = `${BACKEND_Image_URL}${respond.data._id.toString()}`;
          this.thumbnailImageId = respond.data._id;
        }
      },
      (respond_error) => {
        let error_message = respond_error.error.message;
        this.toastrService.error(error_message);
      }
    );
  }

  async uploadGifFile(files) {
    await this.imageService.uploadGifFile(files).subscribe(
      async (respond) => {
        console.log('respond: ', respond);
        if ((respond.status = 'success')) {
          console.log('uploadGifFile: ', respond.data._id.toString());
          this.animation = `${BACKEND_Image_URL}${respond.data._id.toString()}`;
          this.animationImageId = respond.data._id;
        }
      },
      (respond_error) => {
        let error_message = respond_error.error.message;
        this.toastrService.error(error_message);
      }
    );
  }

  async dataURItoBlob(dataURI) {
    var byteString = atob(dataURI.split(',')[1]);
    var mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
    var ab = new ArrayBuffer(byteString.length);
    var ia = new Uint8Array(ab);

    // set the bytes of the buffer to the correct values
    for (var i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }

    // write the ArrayBuffer to a blob, and you're done
    var blob = new Blob([ab], { type: mimeString });
    return blob;
  }

  redirectTo(uri: string) {
    this.router.navigateByUrl('/en/' + uri).then(() => {});
  }
}
