const {app, BrowserWindow} = require('electron')
const url = require("url");
const path = require("path");
const express = require('./bin/www'); //your express app

async function starExpress(callback) {
    await express();
}

let mainWindow

    function createWindow () {
      mainWindow = new BrowserWindow({
        width: 1900,
        height: 900,
        minHeight: 900,
        minWidth: 1900,
        webPreferences: {nodeIntegration:true}}
        );

      mainWindow.loadURL('http://localhost:5858/');

      // Open the DevTools.
      // mainWindow.webContents.openDevTools()

      mainWindow.on('closed', function () {
        express.exit();
        mainWindow = null
      })
    }

    app.on('ready', createWindow)

    app.on('window-all-closed', function () {
      // if (process.platform !== 'darwin') app.quit()
    })

    app.on('activate', function () {
      if (mainWindow === null) createWindow()
    })
