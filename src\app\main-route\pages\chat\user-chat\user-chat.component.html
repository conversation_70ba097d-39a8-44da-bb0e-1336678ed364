

<div
  style="
    display: flex;
    position: relative;
    z-index: 2;
    width: 100%;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    background-color: var(--app-container);
    box-shadow: 0 -5px 5px 5px #67aeb1;
    scale: 1.1;
    gap: 1em;
  ">
<app-avatar-photo
*ngIf="userChat"
[buffer]="userChat?.photoProfile?.data"
[userId]="userChat?._id"
style="cursor: pointer"
[circleColor]="userChat?.photoColor"
[name]="userChat?.name"
[surname]="userChat?.surname"
[class]="'userImgBloggers'"
[classAvatarInitials]="'initialsClass'"
></app-avatar-photo>
<div 
*ngIf="userChat"
style="display: grid;">
    <span style="font-size: 16px">
        {{userChat?.name}} {{userChat?.surname}}
    </span>
    <span 
      *ngIf="anotherUser?.isOnline"
        style="font-size: 12px;text-align: center;color: var(--green);">
            Online
    </span>
    <span 
        *ngIf="!anotherUser?.isOnline && anotherUser !== null"
        style="font-size: 12px;text-align: center;opacity: 0.7;">
            Ofline
    </span>
</div>
  
</div>
<div style="flex-grow: 1; overflow: auto">
  <ul
    style="
      display: flex;
      flex-direction: column;
      gap: 1.5em;
      margin: 0;
      padding: 0;
      position: relative;
    "
  >
    <!-- skeleton -->
    <ng-container
    *ngIf="loading">
        <li class="sent-message-wrapper listChatWrapper">
                <p style="margin: 0; display:flex;height: 100px; width: 350px;border-radius: 15px 0px 15px 15px;padding: 1em;" class="skeleton2">
                <span class="skeleton1" style="width: 111px;
                height: 20px;"></span>
                </p>
                <span style="display: flex;
                height: 50px;
                width: 50px;
                border-radius: 50%;" class="skeleton1"></span>
        </li>
        <li class="received-message-wrapper listChatWrapper">
        <p style="margin: 0; display:flex;height: 100px; width: 350px;border-radius: 15px 0px 15px 15px;padding: 1em;" class="skeleton2">
            <span class="skeleton1" style="width: 111px;
            height: 20px;"></span>
            </p>   
        </li>
        <li class="received-message-wrapper listChatWrapper">
            <p style="margin: 0; display:flex;height: 100px; width: 350px;border-radius: 15px 0px 15px 15px;padding: 1em;" class="skeleton2">
                <span class="skeleton1" style="width: 111px;
                height: 20px;"></span>
                </p>  
        </li>
        <li class="sent-message-wrapper listChatWrapper">
            <p style="margin: 0; display:flex;height: 100px; width: 350px;border-radius: 15px 0px 15px 15px;padding: 1em;" class="skeleton2">
                <span class="skeleton1" style="width: 111px;
                height: 20px;"></span>
                </p>    <span style="display: flex;
            height: 50px;
            width: 50px;
            border-radius: 50%;" class="skeleton1"></span>
        </li>
    </ng-container>
    <!-- coversation -->


    <li
      [ngClass]="text.me === '630a2e5d4989aae851a657e4' ? 'sent-message-wrapper listChatWrapper' : 'received-message-wrapper listChatWrapper'"
      *ngFor="let text of messages">
      <div
      [ngClass]="text.me === '630a2e5d4989aae851a657e4' ? 'sent-message' : 'received-message'"
        style="max-width: 100%"

      >
        <p
        *ngIf="text?.adminUser?._id"
          style="
            margin: 0 10px;
            font-size: 15px;
            position: relative;
            text-align: end;
          "
        >
          {{text?.adminUser?.name}}  {{text?.adminUser?.surname}}
        </p>

        <p style="margin: 0; font-size: 13px">
          {{text.text}}
        </p>
        <p
          style="margin: 0; font-size: 13px; text-align: end; opacity: 0.7"
        >
          <!-- {{ message.time }} {{ message.date | date }} -->
          {{ text.createdAt | date: 'MMMM d, EEEE hh:mm a' }}
        </p>
      </div>
      <!-- *ngIf="message.sent === true" -->
      <div
      *ngIf="text?.adminUser?._id"
        style="
          display: flex;
          align-items: center;
          gap: 1em;
          justify-content: end;
        "
      >
      <div
      style="position: relative;">
          <app-avatar-photo
          [buffer]="text?.adminUser?.photoProfile?.data"
          [userId]="text?.adminUser?._id"
          style="cursor: pointer"
          [circleColor]="text?.adminUser?.photoColor"
          [name]="text?.adminUser?.name"
          [surname]="text?.adminUser?.surname"
          [class]="'userImgBloggers'"
          [classAvatarInitials]="'initialsClass'"
          ></app-avatar-photo>
            <img
            *ngIf="text?.adminUser?.isVerified"
              style="
                height: 15px;
                width: 15px;
                position: absolute;
                bottom: 0;
                right: 0;"
              src="/assets/icons/fiberVerified.svg"
              alt=""
            />
            <img
            *ngIf="text?.adminUser?.untrusted"
              style="
                height: 15px;
                width: 15px;
                position: absolute;
                bottom: 0;
                right: 0;"
              src="/assets/icons/fiberUnverified.svg"
              alt=""
            />
      </div>
      </div>
    </li>
    <div #toBottom ></div>
  </ul>
</div>
<div
*ngIf="anotherUser"
  style="
    display: flex;
    width: 100%;
    padding: 10px 20px;
    align-items: center;
    background-color: var(--app-container);
    /* position: sticky; */
    /* bottom: 0;
    left: 0; */
    justify-content: center;
    scale: 1.1;

    box-shadow: inset 0px 5px 5px -5px #67aeb1;
  "
>
  <div
    style="
      width: 80%;
      position: relative;
      height: fit-content;
      margin-top: 0;
      gap: 1em;
    "
    class="search-bar"
  >
    <input
    [(ngModel)]="message"  
    autocomplete="off" 
      name="q"
      type="text"
      size="30"
      placeholder="Type your message..."
      autocomplete="off"
      style="height: 50px; background-image: none !important"
    />
    <span
    (click)="send()"
      style="
        display: flex;
        justify-content: center;
        align-items: center;
        background: var(--action-color);
        line-height: 3;
        height: fit-content;
        width: 15%;
        align-self: center;
        border-radius: 15px;
      "
      >Send</span
    >
  </div>
</div>

