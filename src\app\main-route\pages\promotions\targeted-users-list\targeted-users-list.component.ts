import { Component, OnInit, TemplateRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ngxLoadingAnimationTypes } from 'ngx-loading';
import { ToastrService } from 'ngx-toastr';
import { LoadJsService } from 'src/app/shared/services/load-js.service';
import { PromotionAvailableService } from 'src/app/shared/services/promotion-available.service';
import { PromotionService } from 'src/app/shared/services/promotion.service';

@Component({
  selector: 'app-targeted-users-list',
  templateUrl: './targeted-users-list.component.html',
  styleUrls: ['./targeted-users-list.component.css']
})
export class TargetedUsersListComponent implements OnInit {

  public ngxLoadingAnimationTypes = ngxLoadingAnimationTypes;
  public loadingTemplate: TemplateRef<any>;


  promotionId = ''
  lang = 'en';
  loading = false


  promotionAvailableShared = []
  specificUsers = []
  nativeNotificationUsers = []

  pageNumber = 0
  resultsPerPage = 10
  promotionsAvailableCount
  promotionsAvailable = []

  constructor(
    public loadJsService: LoadJsService,
    private router: Router,
    private toastrService: ToastrService,
    private promotionService: PromotionService,
    private promotionAvailableService: PromotionAvailableService,
    private activatedRoute: ActivatedRoute
  ) { }

  ngOnInit(): void {
    this.loading = true
    this.activatedRoute.params.subscribe( async paramMap => {
      if(paramMap['lang']){
        this.lang = paramMap['lang'];
      }
      if(paramMap['promotionId']){
          this.promotionId = paramMap['promotionId']
          this.getAllTargetedUsers(this.promotionId)
      }
    })
  }

  getAllTargetedUsers(promotionId){ 
    this.pageNumber = this.pageNumber+1
    this.promotionService
    .getAllTargetedUsers(promotionId,this.pageNumber,10)
    .subscribe( async respond  => {
            if( respond.status = "success"){
              this.promotionsAvailableCount = respond.data.promotionsAvailableCount
              console.log("respond: ",respond)
              if(respond.data.promotionsAvailable.length){
                this.promotionsAvailable = respond.data.promotionsAvailable
              }
              this.loading = false
            }
    },respond_error => { this.toastrService.error(respond_error.error.message)}); 
  
  }


  onRemove(item, i, activeStatus){
    this.promotionAvailableService.changeActiveStatus(item._id,activeStatus).subscribe( 
      result =>{
        if(result.status == "success"){
              if(activeStatus === false ){
                this.toastrService.error( "Successfully deactivated" );
               }
               console.log("i: ",i)
               this.promotionsAvailable.splice(this.promotionsAvailable.indexOf(item),1);

        }

    },
    respond_error => {
      this.toastrService.error(
        respond_error?.error.message
         , respond_error?.name);         
      }
    )
  }

}
