const mongoose = require('mongoose');
const { MongooseFindByReference } = require('mongoose-find-by-reference');

const searchListShownSchema = new mongoose.Schema(
  {
      user: {
        type: mongoose.Schema.ObjectId,
        ref: 'User',
        required: [true, 'Search List Shown must been created from a User!'],
        unique: true
      },
      createdAt: {
        type: Date,
        default: Date.now
      }
  }

);

searchListShownSchema.pre('save', async function( next) {
    next();
});
  
searchListShownSchema.pre(/^find/, function(next) {
    next();
  })

searchListShownSchema.plugin(MongooseFindByReference);

const SearchListShown = mongoose.model('SearchListShown', searchListShownSchema);

module.exports = SearchListShown;
