const mongoose = require('mongoose');

const followingSchema = new mongoose.Schema(
  { 
    follower: {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
    },
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

followingSchema.pre('save', async function( next) {

  await this.populate({
    path: 'user',
  });
  await this.populate({
    path: 'follower',
  });
  next();
});



followingSchema.pre(/^find/, function(next) {
  this.populate({
    path: 'follower',
  });
  this.populate({
    path: 'user',
  });
  next();
})

const Following = mongoose.model('Following', followingSchema);

module.exports = Following;
