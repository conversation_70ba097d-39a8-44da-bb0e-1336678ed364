/* First Screen */
.settingWrapper {
  display: grid;
  grid-template-columns: 300px 300px;
  gap: 2em;
}
@media screen and (max-width: 790px) {
  .settingWrapper {
    grid-template-columns: 1fr;
  }
}

/* Second Screen */
.settingItemsWrapper {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: fit-content;
  grid-gap: 2em;
  gap: 2em !important;
}
.settingsDetailsWrapper {
  grid-template-rows: 1fr 2fr 1fr;
  border-radius: 20px;
  overflow: hidden;
  gap: 1.5em;
  min-height: 518px;
}

@media screen and (max-width: 1137px) {
  .settingItemsWrapper {
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 1137px) {
  .showingInfo {
    display: none;
  }
  .settings-details-section {
    grid-template-columns: 1fr !important;
    overflow: scroll;
  }
  .settingsDetailsWrapper {
    grid-template-rows: 1fr 2fr 1fr;
    border-radius: 20px;
    overflow: hidden;
    gap: 1.5em;
    min-height: 751px;
  }
  .headerDetails {
    display: none !important;
  }
  .headerDetails1 {
    display: flex !important;
    max-height: 80px;
  }
  .settingsDetailsWrapper {
    justify-content: center;
    align-items: center;
  }
}
@media screen and (min-width: 1137px) {
  .headerDetails1 {
    display: none !important;
  }
  .headerDetails {
    display: flex !important;
    max-height: 80px;
  }
}

.fee-amount-input {
  border: 1px solid var(--link-color-hover);
  flex: 1;
  outline: none;
  height: 100%;
  padding: 10px 20px;
  font-size: 16px;
  background-color: var(--search-area-bg);
  color: var(--main-color);
  width: 100%;
}
.fee-amount-input:placeholder {
  color: var(--main-color);
  opacity: 0.6;
}

.settings-details-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  padding: 32px !important;
  gap: 2em;
}
.optionStyle {
  font-size: 16px;
  text-align: center;
  width: 100%;
  background-color: var(--app-bg);
  line-height: 2;
  border: none;
  color: white;
  font-weight: 600;
  height: 40px;
}
p,
span,
div {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
