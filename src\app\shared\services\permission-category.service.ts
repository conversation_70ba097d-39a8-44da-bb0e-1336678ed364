import { ToastrService } from 'ngx-toastr';
import { User } from '../models/user';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, of, forkJoin } from 'rxjs';
import { take, map, tap, delay, switchMap } from 'rxjs/operators';
import { from, Observable } from 'rxjs';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';

const BACKEND_URL = environment.base_url + "/permissionCategory/";

@Injectable({
  providedIn: 'root'
})
export class PermissionCategoryService {

  constructor(
    private http: HttpClient
  ) { }

  create(
    data
  ){
    return  this.http.post<any>( BACKEND_URL , data)
  }

  getAll(
    data
  ){
    return  this.http.post<any>( BACKEND_URL + '/getAll/' , data)
  }
  
}
