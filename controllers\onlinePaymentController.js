const catchAsync = require('./../utils/catchAsync');
const CentralBank = require('./../models/centralBankModel');
const Transaction = require('./../models/transactionModel');
const OnlinePayment = require('./../models/onlinePaymentModel');
const MainData = require('./../models/mainDataModel');
const User = require('./../models/userModel');
const OperationLog = require('./../models/operationLogModel');

exports.create = catchAsync(async (req, res, next) => {

let transaction =  null
let centralBank =  null
let onlinePayment =  null
let operationLog =  null
let mainData =  null

let userUpdated = {
    coins: 0
}
let centralBankTotalAmount =  0
let theMinisterOfFinanceTotalaAmount =  0


let onlinePaymentTotalCreditsAmount =  0
let onlinePaymentTotalRealMoneyAmount =  0


let centralBankTotalCreditsAmountBefore =  0
let centralBankTotalCreditsAmountAfter =  0

// console.log("req.body.transaction: ",req.body.transaction)
// console.log("req.body.centralBank: ",req.body.centralBank)
// console.log("req.body.onlinePayment: ",req.body.onlinePayment)

if(req.body.transaction){
  transaction = await Transaction.create(req.body.transaction);
}
if(req.body.centralBank){
    

      mainData = await MainData.find({variableName: ['central-bank-total-amount','online-payment-total-real-money-amount','online-payment-total-credits-amount']})
    //   console.log("mainData: ",mainData)
      for(let data of mainData ){
        
            if(data.variableName == 'online-payment-total-credits-amount'){
                onlinePaymentTotalCreditsAmount =  data.value
                onlinePaymentTotalCreditsAmount = onlinePaymentTotalCreditsAmount + req.body.onlinePayment.creditsAmount
                await MainData.findByIdAndUpdate(data._id ,{value: onlinePaymentTotalCreditsAmount})
            }
            if(data.variableName == 'online-payment-total-real-money-amount'){
                onlinePaymentTotalRealMoneyAmount =  data.value
                onlinePaymentTotalRealMoneyAmount = onlinePaymentTotalRealMoneyAmount + req.body.onlinePayment.realMoneyAmount
                await MainData.findByIdAndUpdate(data._id ,{value: onlinePaymentTotalRealMoneyAmount})
            }
            if(data.variableName == 'central-bank-total-amount'){
                centralBankTotalCreditsAmountBefore =  data.value
                centralBankTotalAmount = data.value            
                centralBankTotalAmount = centralBankTotalAmount - req.body.centralBank.creditsAmount 
                centralBankTotalCreditsAmountAfter =  centralBankTotalAmount
                await MainData.findByIdAndUpdate(data._id ,{value: centralBankTotalAmount})
            }
      }
    // console.log("transaction: ",transaction)
      if(transaction !== null){
        req.body.centralBank['transaction']= transaction._id
      }

    //   console.log("req.body.centralBank: ",req.body.centralBank)

    centralBank = await CentralBank.create(req.body.centralBank);

    req.body.onlinePayment['centralBankTotalCreditsAmountBefore'] = centralBankTotalCreditsAmountBefore
    req.body.onlinePayment['centralBankTotalCreditsAmountAfter'] = centralBankTotalCreditsAmountAfter

    if(centralBank !== null){
        req.body.onlinePayment['centralBankTransaction'] = centralBank._id
    }
    if(transaction !== null){
        req.body.onlinePayment['transaction'] = transaction._id
    }

    onlinePayment =  await OnlinePayment.create(req.body.onlinePayment);
    // console.log("onlinePayment: ",onlinePayment)
    if(onlinePayment !== null){
        let update = {}
        let user = await User.findOne({ _id: req.body.onlinePayment.user}) 
        update.coins = user.coins + req.body.transaction.creditsAmount
        userUpdated = await User.findByIdAndUpdate(req.body.onlinePayment.user, update, {new: true}); 
        // OperationLog.create({
        //     comment : senderOperationLog,
        //     user : senderId
        //   }) 
        operationLog = await OperationLog.create({
            comment: 'Added ' + req.body.transaction.creditsAmount  
            +' credits from Fiber Central Bank. Paypal executed successfully to buy credits for '
            + req.body.onlinePayment.realMoneyAmount+' '+ req.body.onlinePayment.realMoneyCurrency,
            user: req.body.onlinePayment.user
        });

        
        // console.log("operationLog: ",operationLog)
        // console.log("userUpdated: ",userUpdated)  
    }

    }

    res.status(201).json({
        status: 'success',
        userUpdatedCoins : userUpdated.coins
    });

});


exports.getAll = catchAsync(async (req, res, next) => {
    let resultsPerPage =   req.body.resultsPerPage 
    let page = req.body.page >= 1 ? req.body.page : 1;
    page = page - 1
  
    let data =
     await OnlinePayment.find()
     .populate({
      path: 'user',
      select: ['id','_id','name','surname','isVerified','photoColor','untrusted','earnCount','likesCount','commentsCount','postCount','email']
    })
    .limit( resultsPerPage)
    .skip( resultsPerPage * page)
    .sort({ _id: -1 })
    
    let count = await OnlinePayment.countDocuments()

    res.status(200).json({
      status: 'success',
      data: {
        data: data,
        count: count
      }
    });
  
});



exports.searchOnlinePayment = catchAsync(async (req, res, next) => {
  if(req.body.searchText !== ''){
      let resultsPerPage =   req.body.resultsPerPage 
      let page = req.body.page
      page = page - 1
      req.body.searchText = req.body.searchText.trim()

      let name = req.body.searchText.split(" ")[0]; 
      let surname = req.body.searchText.split(" ")[1] === undefined ? name : req.body.searchText.split(" ")[1]; 

      let nameRegex = name.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
      let surnameRegex = surname.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
      
      nameRegex = new RegExp(nameRegex, 'i') 
      surnameRegex = new RegExp(surnameRegex, 'i') 
      
      OnlinePayment.find({
          $or: [
          { 'user.name':  { $regex: nameRegex } },
          { 'user.surname':  { $regex: surnameRegex } },
        ]})
      .populate({
          path: 'user',
          select: ['id','_id','name','surname','isVerified','photoColor','untrusted']
      })
      .limit(resultsPerPage)
      .skip(resultsPerPage * page)
      .sort({ _id: -1 })
      .then(  async result => {
        // console.log("result: ",result)
        let countList = await OnlinePayment.find({
          $or: [
          { 'user.name':  { $regex: nameRegex } },
          { 'user.surname':  { $regex: surnameRegex } },
        ]},{user:0})
        let count = countList.length
        res.status(200).json({
          status: 'success',
          data: {
            data: result,
            count: count
          }
        });
      })

    }else{
      res.status(200).json({
        status: 'success',
        resultSearch: result 
      });
    }
});