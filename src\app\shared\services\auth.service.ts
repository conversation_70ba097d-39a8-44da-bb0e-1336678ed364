import { User } from '../models/user';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, of, forkJoin } from 'rxjs';
import { take, map, tap, delay, switchMap } from 'rxjs/operators';
import { from, Observable } from 'rxjs';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';
import { DomSanitizer } from '@angular/platform-browser';

const BACKEND_URL = environment.base_url + "/users/";

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  public currentUser = new BehaviorSubject<any>(null);
  private activeLogoutTimer: any;

  get userIsAuthenticated() {
    return this.currentUser.asObservable().pipe(
      map(user => {
        if (user) {
          return !!user.token;
        } else {
          return false;
        }
      })
    );
  }

  get user() {
    return this.currentUser.asObservable().pipe(
      map(user => {
        if (user) {
          return user;
          if(user._token){
            return user;
          }
          return null;

        } else {
          return null;
        }
      })
    );
  }

  get userId() {
    return this.currentUser.asObservable().pipe(
      map(user => {
        if (user) {
          return user.identifier;
        } else {
          return null;
        }
      })
    );
  }

  get token() {
    return this.currentUser.asObservable().pipe(
      map(user => {
        if (user) {
          return user.token;
        } else {
          return null;
        }
      })
    );
  }

  constructor(    
                private http: HttpClient,
                private router: Router,
              ) {}

autoLogin() {

                let tokenExpirationDate
                let storedData = new Observable<any>(observer => {
                  observer.next(JSON.parse(localStorage['authData'] || null));
                  observer.complete();
                });

                return from(storedData).pipe(
                  map( respond => {

                    if (!respond) {
                        return null
                    }
                    const expirationTime = new Date(respond.tokenExpirationDate);
                    if ( expirationTime <= new Date()) {
                      return null;
                    }

                    let user = new User(
                      respond._id,
                      respond.name,
                      respond.surname,
                      respond.bio,
                      respond.email,
                      respond.role,
                      respond.photoProfile,
                      respond.photoCover,
                      respond.photoColor,
                      respond.coins,
                      respond._token,
                      expirationTime
                    );

                    if (!this.token) {
                        tokenExpirationDate=0;
                    }else{
                      tokenExpirationDate = expirationTime;
                    }

                    return  user;
                  }),
                  tap(user => {
                    if (user) {
                      this.currentUser.next(user);
                    }
                  }),
                  map(user => {
                    this.autoLogout(tokenExpirationDate);
                    return !!user;
                  })
                );
}


setUserData(
  _id: string,
  name: string,
  surname: string,
  bio: string,
  email: string,
  role : string,
  photoProfile: any,
  photoCover: any,
  photoColor: string,
  coins:Number,
  _token: string,
  tokenExpirationDate: Date
) {


let user = new User(
  _id,
  name,
  surname,
  bio,
  email,
  role,
  photoProfile,
  photoCover,
  photoColor,
  coins,
  _token,
  new Date(tokenExpirationDate)
);
this.currentUser.next(user);
// this.autoLogout(user.tokenDuration);
this.storeAuthData(user);

}

public  storeAuthData (
  user: User
) {
  
  let path=''
    if( user.role == "client"){
            path = "client-user"
    }else if( user.role == "admin"){
          path = "admin-user"
    }

    localStorage['urlState'] = JSON.stringify(path)
    localStorage['authData'] =  JSON.stringify(user)

}


signUp(
  name: string,
  surname: string,
  email: string,
  role: string,
  password: string,
  passwordConfirm: string,
) {
  let colors = [
    '#EB7181', // red
    '#468547', // green
    '#FFD558', // yellow
    '#3670B2', // blue
  ];
  const randomIndex = Math.floor(Math.random() * Math.floor(colors.length));

  let user = {
    "name": name,
    "surname": surname,
    "email": email,
    "photoColor": colors[randomIndex],
    'role': role,
    "password": password,
    "passwordConfirm": passwordConfirm
  }

  return  this.http.post<any>( BACKEND_URL + 'signup', user)

}

  login(email: string, password: string) {
    let user = {
      "email": email,
      "password": password,
    }
    return  this.http.post<any>( BACKEND_URL + 'login', user)
  }



   signOut(lang: string = 'en') {
    this.currentUser.next(null);
    localStorage.removeItem('authData');
    localStorage.removeItem('confirmationResult');
    
    localStorage.removeItem('urlState');
    localStorage.removeItem('pageNumber');
    // this.router.navigateByUrl('/login');
    this.router.navigateByUrl('/auth');

    // this.router.navigateByUrl(lang+'/auth/login');
    // this.router.navigateByUrl('/login').then(() => {
    //   window.location.reload();
    // });

  }

  forgotPassword(email) {
    let user = {
      "email": email
    }
    return  this.http.post<any>( BACKEND_URL + 'forgotPassword', user)
  }

  resetPassword( password: string, passwordConfirm: string, token: string) {
    let user = {
      "password": password,
      "passwordConfirm": passwordConfirm
    }
    return  this.http.patch<any>( BACKEND_URL + 'resetPassword/' + token, user)
  }

  ngOnDestroy() {
    if (this.activeLogoutTimer) {
      clearTimeout(this.activeLogoutTimer);
    }
  }


  autoLogout(expirationTime: any) {
    if ( expirationTime < new Date().getTime()) {

          let storedData =   JSON.parse(localStorage['authData'] || null)
 
          if (!storedData) {
              return null
          }
            this.signOut();
    }
  }

}
