import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { LoadJsService } from 'src/app/shared/services/load-js.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent implements OnInit {

  constructor(
    private activatedRoute: ActivatedRoute) {
  }

  ngOnInit(): void {
    this.activatedRoute.paramMap.subscribe( paramMap => {
      console.log("paramMap: ", paramMap.has('lang'))
      if(paramMap.has('lang')){
        console.log("paramMap['lang']: ", paramMap['lang'])
        // this.lang = paramMap['lang'];           
      }
    })
  }

  ngOnDestroy(){
  }

}
