import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, CanActivate, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { take, map, switchMap, tap } from 'rxjs/operators';
import { CanLoad, Route, UrlSegment } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanLoad {
  constructor(
              private router: Router,
              private auth: AuthService,
             ) { }


  canLoad( route: Route,
           segments: UrlSegment[],
          ): Observable<boolean> | Promise<boolean> | boolean{
    const expectedRole = route.data.role;

    return this.auth.userIsAuthenticated.pipe(
      take(1),
      switchMap(isAuthenticated => {

        if (!isAuthenticated) {
          return this.auth.autoLogin();
        } else {

          return this.auth.user.pipe(
            map(user => {
                let role = user.role;

                if (expectedRole == role ) {
                  return true;
                } else {

                  this.router.navigateByUrl('/sign-in');

                  return false;
                }
            })
          )

        }
      }),
      tap(isAuthenticated => {
        if (!isAuthenticated) {
          this.router.navigateByUrl('/sign-in');
        }
      })
    );

  }
}
