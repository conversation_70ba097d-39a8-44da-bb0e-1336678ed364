<div
  style="
    background-color: var(--app-bg);
    border-radius: 20px;
    scale: 1.1;
    height: 100%;
  "
>
  <div class="modal-body">
    <!-- header -->
    <div style="display: flex; justify-content: end; width: 100%">
      <img
        (click)="activeModal.close('Close click')"
        style="height: 17px; width: 17px; margin-left: auto"
        src="/assets/icons/close.svg"
        alt=""
      />
    </div>

    <div style="margin: 1em 0">
      <div
        style="
          /* display: none !important; */
          display: grid;
          grid-template-rows: 4em 2em 1fr 4em;
          background-color: var(--sidebar);
          padding: 5px 1em;
          border-radius: 20px;
          max-height: 75vh;
        "
      >
        <div class="disp-flex a-i-center">
          <span class="no-wrap-1-line">User selected</span>
          <div class="m-l-auto disp-flex">
            <div class="search-bar">
              <input 
              (keyup)="searchUser($event)"
              type="text" placeholder="Search for user..." />
            </div>
            <div>
              <button
                (click)="openUserSearchModal(searchContent)"
                class="add-btn"
                title="Add New Project"
              >
                +
              </button>
            </div>
          </div>
        </div>
        <div
          style="
            display: grid;
            grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 1fr 1fr 0.5fr;
            background-color: rgba(26, 37, 59, 0.5);
            align-items: center;
            margin-bottom: 5px;
            border-radius: 10px;
            gap: 1em;
          "
        >
          <span style="margin-left: 1em" class="no-wrap-1-line">User info</span>
          <span style="text-align: center" class="no-wrap-1-line"
            >Earn Count</span
          >
          <span style="text-align: center" class="no-wrap-1-line"
            >Followers Count</span
          >
          <span style="text-align: center" class="no-wrap-1-line"
            >Following Count</span
          >
          <span style="text-align: center" class="no-wrap-1-line"
            >Posts Count</span
          >
          <span style="text-align: center" class="no-wrap-1-line"
            >Comments Count</span
          >
          <span style="text-align: center" class="no-wrap-1-line"
            >Likes Count</span
          >

          <span class="disp-flex j-c-center">
            <!-- Actions -->
          </span>
        </div>
        <ul
          style="
            padding-inline-start: 0px;
            margin-bottom: 0rem;
            overflow-y: scroll;
          "
          class=""
        >
          <ng-container *ngIf="loading">
            <li
            *ngFor="let i of [].constructor(10)"
              class="skeleton1"
              style="
                display: grid;
                grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 1fr 1fr 0.5fr;
                align-items: center;
                min-height: 4em;
                gap: 1em;
                border-bottom: 1px solid rgba(26, 37, 59, 0.5);
              "
            >
              <div class="disp-grid">
                <span
                  class="skeleton2"
                  style="
                    font-size: 14px;
                    font-weight: 500;
                    opacity: 0.7;
                    width: 40%;
                    height: 16px;
                    margin-left: 13px;
                  "
                ></span>
              </div>
              <div class="disp-grid">
                <span
                  class="skeleton2"
                  style="
                    font-size: 14px;
                    font-weight: 500;
                    opacity: 0.7;
                    width: 53%;
                    height: 16px;
                  "
                ></span>
              </div>
              <div class="disp-grid">
                <span
                  class="skeleton2"
                  style="
                    font-size: 14px;
                    font-weight: 500;
                    opacity: 0.7;
                    width: 53%;
                    height: 16px;
                  "
                ></span>
              </div>
              <div class="disp-grid">
                <span
                  class="skeleton2"
                  style="
                    font-size: 14px;
                    font-weight: 500;
                    opacity: 0.7;
                    width: 53%;
                    height: 16px;
                  "
                ></span>
              </div>
              <div class="disp-grid">
                <span
                  class="skeleton2"
                  style="
                    font-size: 14px;
                    font-weight: 500;
                    opacity: 0.7;
                    width: 53%;
                    height: 16px;
                  "
                ></span>
              </div>
              <div class="disp-grid">
                <span
                  class="skeleton2"
                  style="
                    font-size: 14px;
                    font-weight: 500;
                    opacity: 0.7;
                    width: 53%;
                    height: 16px;
                  "
                ></span>
              </div>
              <div class="disp-grid">
                <span
                  class="skeleton2"
                  style="
                    font-size: 14px;
                    font-weight: 500;
                    opacity: 0.7;
                    width: 53%;
                    height: 16px;
                  "
                ></span>
              </div>
              <div class="disp-grid"></div>
            </li>
          </ng-container>

          <div
            *ngIf="userFilteredData.length === 0 && !loading"
            style="
              position: relative;
              display: flex;
              justify-content: center;
              align-items: center;
              flex-direction: column;
            "
          >
            <img
              style="width: 70%; max-width: 299px"
              src="/assets/icons/animatedIconsTable/list_empty.svg"
              alt=""
            />
            <p style="font-size: 16px">List is empty</p>
          </div>
          <ng-container *ngIf="!loading">
            <li
            *ngFor="let user of userFilteredData"
              style="
                display: grid;
                grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 1fr 1fr 0.5fr;
                align-items: center;
                min-height: 4em;
                gap: 1em;
                border-bottom: 1px solid rgba(26, 37, 59, 0.5);
              "
            >
              <div class="disp-flex gap-05">
                <div style="" class="disp-flex gap-05">
                  <app-avatar-photo
                  [userId]="user?._id"
                  [circleColor]="user?.photoColor"
                  [name]="user?.name"
                  [surname]="user?.surname"
                  [class]="'userImgBloggers'"
                  [classAvatarInitials]="'initialsClass'"
                  ></app-avatar-photo>
                </div>
                <div class="disp-grid">
                  <span> {{user?.name}} {{user.surname}} </span>
                  <span> {{user?.email}} </span>
                </div>
              </div>
              <span style="text-align: center" class="no-wrap-1-line">
                <!-- Earn count -->
                {{user?.earnCount}}
              </span>
              <div class="disp-grid">
                <!-- Followers Count -->
                <span style="text-align: center" class="no-wrap-1-line">
                  {{user?.followersCount}}
                </span>
              </div>
              <div class="disp-grid">
                <!-- Following Count -->
                <span style="text-align: center" class="no-wrap-1-line">
                  {{user?.followingCount}}
                </span>
              </div>
              <div class="disp-grid">
                <!-- Post Count -->
                <span style="text-align: center" class="no-wrap-1-line">
                  {{user?.postCount}}
                </span>
              </div>
              <div class="disp-grid">
                <!-- Comments Count -->
                <span style="text-align: center" class="no-wrap-1-line">
                  {{user?.commentsCount}}
                </span>
              </div>
              <div class="disp-grid">
                <!-- Likes Count -->
                <span style="text-align: center" class="no-wrap-1-line">
                  {{user?.likesCount}}
                </span>
              </div>

              <div class="disp-flex j-c-center">
                <label class="control control--checkbox">
                  <input 
                  (change)="changeCheckbox(user,$event)"
                  [checked]="user?.checked"
                  type="checkbox" checked="checked" />
                  <div class="control__indicator"></div>
                </label>
              </div>
            </li>
          </ng-container>

        </ul>
        <div class="list-number disp-flex" style="">
          <div
            class="showingInfoWrapper"
            style="
              margin: 0 0 0.75rem;
              display: flex !important;
              align-items: center;
              gap: 1em;
            "
          >
            <span class="showingInfo">
              Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
            {{
              pageNo * resultsPerPage - (resultsPerPage - userFilteredData.length)
            }}
            of {{ count }}
            </span>
            <div style="border: 1px solid gray; border-radius: 15px">
              <div>
                <input style="display: none" id="dropdownInput" type="checkbox" />
                <div
                  style="
                    display: flex;
                    justify-content: center;
                    height: 45px;
                    width: 50px;
                  "
                  class="dropdown"
                >
                  <select
                    style="
                      display: flex;
                      background: transparent;
                      border: none;
                      color: white;
                      font-size: 18px;
                      width: 90%;
                      font-weight: 600;
                    "
                    (ngModelChange)='resultsPerPageChanged($event)' 
                    [(ngModel)]="resultsPerPage">
                    <option class="optionStyle colorCancel">10</option>
                    <option class="optionStyle colorCancel">15</option>
                    <option class="optionStyle colorCancel">20</option>
                    <option class="optionStyle colorCancel">30</option>
                    <option class="optionStyle colorCancel">50</option>
                  </select>
                  <label for="dropdownInput" class="overlay"></label>
                </div>
              </div>
            </div>
          </div>

          <nav>
            <ul class="pager">
              <li class="pager__item pager__item--prev">
              <a
                *ngIf="pageNo !== 1"
                (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
                style="width: fit-content !important; padding: 0 10px"
                class="pager__link"
              >
                Previous
              </a>
            </li>
            <li *ngIf="pageNo !== 1" class="pager__item">
              <a (click)="setPage(1)" class="pager__link">...</a>
            </li>
            <li
              *ngFor="
                let item of [].constructor(pageNoTotal) | slice : 0 : 5;
                let i = index
              "
              [ngClass]="pageNo + i === pageNo ? 'active' : ''"
              class="pager__item"
            >
              <a
                *ngIf="pageNo + i <= pageNoTotal"
                (click)="setPage(pageNo + i)"
                class="pager__link"
                >{{ pageNo + i }}</a
              >
            </li>
            <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
              <a (click)="setPage(pageNoTotal)" class="pager__link"
                >...</a
              >
            </li>
            <li
              *ngIf="pageNo !== pageNoTotal"
              class="pager__item pager__item--next"
            >
              <a
                (click)="
                  pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''
                "
                style="width: fit-content !important; padding: 0 10px"
                class="pager__link"
              >
                Next
              </a>
            </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button
        (click)="save()"
        type="button"
        class="btn btn-light">
        Save
      </button>
    </div>
  </div>
</div>
<ng-template #searchContent let-modal>
  <app-search-content
    [(me)]="me"
    (closeModal)="modal.dismiss('Cross click')"
    (userSelected)="selectUser($event)"
  ></app-search-content>
</ng-template>