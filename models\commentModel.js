// review / rating / createdAt / ref to tour / ref to user
const mongoose = require('mongoose');

const commentSchema = new mongoose.Schema(
  {
    comment: {
      type: String,
      trim: true
    },
    post: {
      type: mongoose.Schema.ObjectId,
      ref: 'Post',
      required: [true, 'Comment must belong to a post.']
    },
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Comment must been created from a User!']
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  } 
);

commentSchema.pre('save', async function( next) {
  await this.populate({
    path: 'user',
  }).execPopulate();;
  next();
});



commentSchema.pre(/^find/, function(next) {
  this.populate({
    path: 'user',
  });
  next();
})




const Comment = mongoose.model('Comment', commentSchema);

module.exports = Comment;
