<div style="overflow: auto" class="projects-section projects-add-version">
  
  <div
    style="
      display: flex;
      flex-direction: column;
      overflow: auto;
      flex-grow: 1;
      height: 100%;
      border-radius: 15px;
      overflow-x: hidden;
      gap: 1em;
      align-items: center;
    "
  >
    <div
      style="
        flex-grow: 1;
        border-radius: 15px;
        overflow-y: auto;
        width: 65%;
        display: flex;
        justify-content: center;
      "
    >
      <div style="width: 100%">

        <div 
        *ngIf="!thumbnail"
        (click)="fileInputThumbnail.click()"
        class="input-image-add-version">
          <img
            style="height: 30px; width: 30px"
            src="assets/icons/photo-camera.svg"
            alt=""
          />
          <span>Click to upload</span>
        </div>

        <div 
        *ngIf="thumbnail"
        class="input-image-add-version">
          <div
              style="
                display: flex;
                justify-content: center;
                position: relative;
              "
            >
              <img [src]="thumbnail" class="promotion-sent-image" alt="" />
              <img
                (click)="thumbnailFile = null; thumbnail = null"
                style="
                  height: 20px;
                  width: 20px;
                  position: absolute;
                  top: 0;
                  right: 1em;
                "
                src="assets/icons/close.svg"
                alt=""
              />
            </div>
        </div>

        <div
          style="
            background: var(--sidebar);
            border-radius: 14px;
            margin-top: 1em;
            padding: 1rem 0.7rem;
            display: flex;
            flex-direction: column;
            gap: 1em;
            width: 100%;
          "
        >
          <div class="input-add-version">
            <span>Version</span>
            <input 
            [(ngModel)]="appVersion"
            autocomplete="false" 
            type="text" 
            placeholder="Current version..." />
          </div>
        </div>
        <div
          style="
            background: var(--sidebar);
            border-radius: 14px;
            margin-top: 1em;
            padding: 1rem 0.7rem;
            display: flex;
            flex-direction: column;
            gap: 1em;
            width: 100%;
          "
        >
          <div class="input-add-version-TX">
            <span>Description</span>
            <!--  -->
            <textarea
              [(ngModel)]="description"
              autocomplete="false"
              type="text"
              placeholder="Description..."
            >
            </textarea>
          </div>
        </div>
        <div style="display: flex; width: 100%; gap: 1em">
          <div
            style="
              background: var(--sidebar);
              border-radius: 14px;
              margin-top: 1em;
              padding: 1rem 0.7rem;
              display: flex;
              flex-direction: column;
              gap: 1em;
              width: 100%;
            "
          >
            <div style="display: flex; justify-content: space-evenly">
              <span>App Store</span>
              <div class="disp-flex j-c-center">
                <label class="control control--checkbox">
                  <input 
                  (ngModelChange)="changeInput($event,'appStore')"
                  [(ngModel)]="appStore"
                  type="checkbox"/>
                  <div class="control__indicator"></div>
                </label>
              </div>
            </div>
          </div>
          <div
            style="
              background: var(--sidebar);
              border-radius: 14px;
              margin-top: 1em;
              padding: 1rem 0.7rem;
              display: flex;
              flex-direction: column;
              gap: 1em;
              width: 100%;
            "
          >
            <div style="display: flex; justify-content: space-evenly">
              <span>Google Play</span>
              <div class="disp-flex j-c-center">
                <label class="control control--checkbox">
                  <input 
                  (ngModelChange)="changeInput($event,'googlePlay')"
                  [(ngModel)]="googlePlay"
                  type="checkbox"/>
                  <div class="control__indicator"></div>
                </label>
              </div>
            </div>
          </div>
        </div>
        <div style="display: flex; width: 100%; gap: 1em">
          <div
            style="
              background: var(--sidebar);
              border-radius: 14px;
              margin-top: 1em;
              padding: 1rem 0.7rem;
              display: flex;
              flex-direction: column;
              gap: 1em;
              width: 100%;
            "
          >
            <div style="display: flex; justify-content: space-evenly">
              <span>Active</span>
              <div class="disp-flex j-c-center">
                <label class="control control--checkbox">
                  <input 
                  (ngModelChange)="changeInput($event,'enabled')"
                  [(ngModel)]="enabled"
                  type="checkbox"/>
                  <div class="control__indicator"></div>
                </label>
              </div>
            </div>
          </div>

          <div
            style="
              background: var(--sidebar);
              border-radius: 14px;
              margin-top: 1em;
              padding: 1rem 0.7rem;
              display: flex;
              flex-direction: column;
              gap: 1em;
              width: 100%;
            "
          >
            <div style="display: flex; justify-content: space-evenly">
              <span>Mandatory Update</span>
              <div class="disp-flex j-c-center">
                <label class="control control--checkbox">
                  <input 
                  (ngModelChange)="changeInput($event,'mandatoryToUpdate')"
                  [(ngModel)]="mandatoryToUpdate"
                  type="checkbox"/>
                  <div class="control__indicator"></div>
                </label>
              </div>
            </div>
          </div>

        </div>
        <div
        style="
          margin-top: 3em;
          padding: 1rem 0.7rem;
          display: flex;
          flex-direction: column;
          gap: 1em;
          width: 100%;
        ">
        <div style="display: flex; gap: 1em">
          <button
          (click)="redirectTo('list-version')">Back</button>
          <span 
          *ngIf="editMode"
          (click)="submit()"
          class="buttonReset"> Edit </span>
          <span 
          *ngIf="!editMode"
          (click)="submit()"
          class="buttonReset"> Save </span>
        </div>
      </div>

      </div>
    </div>

    <input
    class="input-image"
    #fileInputThumbnail
    accept="image/*"
    type="file"
    (change)="selectThumbnail($event)"
  />
  </div>

  <div
    style="
      display: flex;
      border-radius: 20px;
      overflow: auto;
      aspect-ratio: 6/6;
    "
  >
    <div
      style="
        background-image: url(assets/iphone.svg);
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        aspect-ratio: 7/18;
      "
    >
      <div
        style="
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 43%;
        "
      >
        <div
          style="
            height: 92%;
            width: 100%;
            margin: 20px 0 0 0;
            border-radius: 0 0 35px 35px;
            display: flex;
            flex-direction: column;
            gap: 1em;
            align-items: center;
            max-height: 670px;
            position: relative;
          "
        >
          <div
            style="
              display: flex;
              align-items: center;
              padding: 0 1.5em;
              gap: 5px;
              margin-top: 5px;
              width: 100%;
            "
          >
            <img
              style="height: 30px; width: 30px"
              src="/assets/fiberLogo.svg"
              alt=""
            />
            <div
              style="
                background-color: black;
                flex: 1;
                height: 28px;
                border-radius: 10px;
                box-shadow: 0px 0px 4px 0px rgba(172, 172, 172, 0.5);
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 6px;
              "
            >
              <span
                style="
                  font-size: 9px;
                  color: rgb(210, 210, 210);
                  line-height: 1;
                "
                class="no-wrap-1-line"
                >Search on FiberAl</span
              ><svg
                xmlns="http://www.w3.org/2000/svg"
                width="12"
                height="12"
                fill="none"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                class="feather feather-search"
                viewBox="0 0 24 24"
              >
                <defs></defs>
                <circle cx="11" cy="11" r="8"></circle>
                <path d="M21 21l-4.35-4.35"></path>
              </svg>
            </div>
            <img
              style="height: 20px; width: 20px"
              src="/assets/icons/notification.svg"
              alt=""
            />
            <img
              style="height: 20px; width: 20px"
              src="/assets/icons/realTime.svg"
              alt=""
            />
          </div>
          <div style="display: flex; flex: 1; width: 100%">
            <div
              class="skeleton1"
              style="
                display: flex;
                flex-direction: column;
                height: 100%;
                width: 100%;
                align-items: center;
                justify-content: center;
              ">

            <!-- [src]="" -->
              <div
                style="
                  position: relative;
                  background: hsla(0deg, 0%, 0%, 0.103);
                  width: 100%;
                  height: 3em;
                  padding: 5px 12px;
                  display: flex;
                  justify-content: start;
                  background-image: linear-gradient(
                    180deg,
                    black,
                    rgba(35, 35, 0, 0)
                  );
                  z-index: 1;
                  align-items: center;
                  gap: 5px;
                "
              >
                <span
                  class="skeleton2"
                  style="height: 30px; width: 30px; border-radius: 50%"
                ></span>
                <div
                  style="
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    gap: 5px;
                  "
                >
                  <span
                    class="skeleton2"
                    style="height: 10px; width: 80px"
                  ></span>
                  <span
                    class="skeleton2"
                    style="height: 10px; width: 50px"
                  ></span>
                </div>
                <span
                  class="skeleton2"
                  style="height: 25px; width: 10px; margin-left: auto"
                ></span>
              </div>
              <div style="flex: 1"></div>
              <div
                style="
                  position: relative;
                  background: hsla(0deg, 0%, 0%, 0.103);
                  width: 100%;
                  height: 3em;
                  padding: 3px 12px;
                  overflow: hidden;
                  background-image: linear-gradient(
                    360deg,
                    black,
                    rgba(35, 35, 0, 0)
                  );
                  display: flex;
                  justify-content: space-evenly;
                  align-items: center;
                  margin-top: auto;
                  z-index: 6;
                "
              >
                <div
                  style="
                    border-radius: 30px;
                    height: 100%;
                    width: 25%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    min-width: 20%;
                    max-width: 30%;
                    max-height: 28px;
                    min-height: 22px;
                    gap: 5px;
                    opacity: 0.7;
                  "
                  class="skeleton2"
                ></div>
                <div
                  style="
                    border-radius: 30px;
                    height: 100%;
                    width: 25%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    min-width: 20%;
                    max-width: 30%;
                    max-height: 28px;
                    min-height: 22px;
                    gap: 5px;
                    opacity: 0.7;
                  "
                  class="skeleton2"
                ></div>
                <div
                  style="
                    border-radius: 30px;
                    height: 100%;
                    width: 25%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    min-width: 20%;
                    max-width: 30%;
                    max-height: 28px;
                    min-height: 22px;
                    gap: 5px;
                    opacity: 0.7;
                  "
                  class="skeleton2"
                ></div>
              </div>
            </div>
          </div>
          <div
            style="
              margin-top: auto;
              display: flex;
              padding: 16px 1em;
              justify-content: space-between;
            "
          >
            <img
              style="width: 9%"
              src="assets/icons/bottomTabIcons/homeAn.svg"
              alt=""
            />
            <img
              style="width: 9%"
              src="assets/icons/bottomTabIcons/PromotionIcon.svg"
              alt=""
            />
            <div
              style="
                width: 9%;
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <span
                style="
                  background: var(--action-color);
                  padding: 4px;
                  height: 18px;
                  width: 18px;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  border-radius: 50%;
                  transform: translateY(-4px);
                  scale: 1.7;
                  font-size: 18px;
                "
                >+</span
              >
            </div>
            <img
              style="width: 9%"
              src="assets/icons/bottomTabIcons/ChatIcon.svg"
              alt=""
            />
            <img
              style="width: 9%; scale: 1.1"
              src="assets/icons/bottomTabIcons/ProfileIcon.svg"
              alt=""
            />
          </div>
          <div
            style="
              position: absolute;
              background: rgba(0, 0, 0, 0.705);
              width: 100%;
              height: 100%;
              z-index: 99;
              border-radius: 30px;
              display: flex;
              justify-content: center;
              align-items: center;
            "
          >
            <div
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                background-color: rgb(17, 17, 17);
                width: 90%;
                gap: 2em;
                border-radius: 20px;
                overflow: hidden;
              "
            >
              <div
                style="
                  background-color: #2d2d2d;
                  height: fit-content;
                  display: flex;
                  flex-direction: column;
                  border-radius: 20px;
                  overflow: hidden;
                  max-width: 320px;
                  position: relative;
                  width: 100%;
                "
              >
                <div style="z-index: 4">
                  <div
                    style="
                      height: 100%;
                      width: 100%;
                      background-repeat: no-repeat;
                    "
                  >
                    <div
                      style="
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                      "
                    >
                      <img
                        *ngIf="thumbnail"
                        style="width: 75%; object-fit: contain;margin-top: 2em;"
                        [src]="thumbnail"
                        alt=""
                      />
                      <span
                        *ngIf="!thumbnail"
                        class="skeleton1"
                        style="width: 75%; margin-top: 2em; aspect-ratio: 1/1"
                      ></span>
                    </div>
                  </div>
                </div>
                <div
                  style="
                    background-image: url(/assets/new1.svg);
                    background-repeat: no-repeat;
                    z-index: 3;
                    background-position: bottom;
                    height: 232px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    width: 100%;
                  "
                >
                  <div
                    style="
                      display: flex;
                      flex-direction: column;
                      align-items: center;
                      justify-content: center;
                      max-width: 319px;
                      width: 100%;
                    "
                  >
                    <p
                    *ngIf="appVersion !== '' "
                      style="
                        margin: 0;
                        color: #ffffff;
                        font-size: 24px;
                        font-weight: 900;
                      "
                    >
                      {{appVersion}}
                    </p>
                    <p
                    *ngIf="appVersion === '' "
                      class="skeleton1"
                      style="height: 30px; margin: 0; width: 60%"
                    ></p>
                    <p
                    *ngIf="description !== '' "
                      style="
                        margin: 9px 0 0 0;
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: 500;
                        width: 90%;
                        text-align: center;
                        flex-wrap: wrap;
                        opacity: 0.7;
                      "
                    >
                      {{description}}
                    </p>
                    <p
                    *ngIf="description === '' "
                      class="skeleton1"
                      style="
                        margin: 9px 0 0 0;
                        min-width: 90%;
                        flex-wrap: wrap;
                        opacity: 0.7;
                        height: 30px;
                        width: 90%;
                      "
                    >
                    </p>
                  </div>
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      padding: 0 2em;
                      gap: 1em;
                      position: absolute;
                      width: 100%;
                      bottom: 1.5em;
                    "
                  >
                    <div
                      *ngIf="!mandatoryToUpdate"
                      style="
                        height: 30px;
                        width: 50%;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-radius: 7px;
                        filter: drop-shadow(0px 0px 6px #00000031);
                        gap: 10px;
                      "
                    >
                      <p
                        style="
                          margin: 0;
                          color: #fff;
                          font-size: 14px;
                          font-weight: 800;
                        "
                      >
                        Cancel
                      </p>
                    </div>
                    <div
                      style="
                        background: #0bbda8;
                        height: 38px;
                        width: 100%;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-radius: 7px;
                        filter: drop-shadow(0px 0px 6px #00000031);
                        gap: 10px;
                      "
                    >
                      <p
                        style="
                          margin: 0;
                          color: #fff;
                          font-size: 14px;
                          font-weight: 800;
                        "
                      >
                        Update
                      </p>
                      <img
                        style="height: 16px; width: 9.4px; margin-bottom: 1px"
                        src="assets/icons/forwardIconWhite.png"
                        alt="icon"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>
