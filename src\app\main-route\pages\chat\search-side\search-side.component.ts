import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { User } from 'src/app/shared/models/user';
import { UserService } from 'src/app/shared/services/user.service';

@Component({
  selector: 'app-search-side',
  templateUrl: './search-side.component.html',
  styleUrls: ['./search-side.component.css']
})
export class SearchSideComponent implements OnInit {

  @Input() me: User;
  @Output() searchLoadingAction: EventEmitter<any> = new EventEmitter();
  @Output() selcetUser: EventEmitter<any> = new EventEmitter();

  resultSearchPageNumber = 1;
  timeout: any = null;
  resultSearch: any = null;
  searchText = '';
  searchLoading = false
  searchSideActivate = false;


  constructor(
    private userService: UserService
  ) { }

  ngOnInit(): void {
  }


  searchUser(searchText) {
    clearTimeout(this.timeout);
    var $this = this;
    this.timeout = setTimeout(function () {
      if (searchText.keyCode != 13) {
        $this.searchLoadingAction.emit(true)
        $this.searchSideActivate = true
        $this.searchLoading = true
        $this.searchSocket(searchText.target.value);
      }else{
        $this.searchLoadingAction.emit(false)
        $this.searchSideActivate = false
        $this.resultSearch = null
        $this.searchLoading = false
      }
    }, 1000);
  }
  public searchSocket(searchText) {
    this.searchText = searchText.toString();
    if(this.searchText == ''){
      this.searchLoadingAction.emit(false)
      this.searchSideActivate = false
      this.searchLoading = false
      this.resultSearch = null
      this.resultSearchPageNumber = 1
      return
    } 
    this.resultSearch = []
    this.resultSearchPageNumber = 1
    this.getAllResultSearch(1, null)
  }
  getAllResultSearch(pageNumber, event) {
    if(this.searchText == '') return
    this.searchLoading = true;
    this.userService.searchUser(this.searchText, '630a2e5d4989aae851a657e4',pageNumber).subscribe(
      (result) => {
        if (result.status == 'success') {
          this.resultSearch = [...this.resultSearch, ...result.resultSearch];
          this.searchLoading = false;
        }
      },
      (respond_error) => {
        this.searchLoading = false;
      }
    );
  }
  async selectUser(user){
    this.selcetUser.emit(user) 
  }

} 
