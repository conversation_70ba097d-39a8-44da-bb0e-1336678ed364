<div style="overflow: auto" class="projects-section">
  <div
    style="
      /* display: none !important; */
      display: grid;
      grid-template-rows: 4em 1fr 4em;
      background-color: var(--sidebar);
      padding: 5px 1em;
      border-radius: 40px;
      height: 80vh;
    "
  >
    <div class="disp-flex a-i-center">
      <span style="margin-left: 1em" class="no-wrap-1-line">Promotions</span>
      <div class="m-l-auto disp-flex">
        <div>
          <input style="display: none" id="dropdownInput" type="checkbox" />
          <!-- <div
            style="
              display: flex;
              justify-content: center;
              height: 35px;
              width: 147px;
            "
            class="dropdown"
          >
            <select
              style="
                display: flex;
                background: transparent;
                border: none;
                color: white;
                font-size: 18px;
                width: 90%;
                font-weight: 600;
              "
            >
              (ngModelChange)="changeStatus($event)"
              [(ngModel)]="statusSelected"
              <option class="optionStyle colorCancel">All</option>
              <option class="optionStyle colorCancel">untrusted</option>
              <option class="optionStyle colorCancel">isVerified</option>
              <option class="optionStyle colorCancel">deleted</option>
            </select>
            <label for="dropdownInput" class="overlay"></label>
          </div> -->
        </div>
        <div class="search-bar">
          <!-- (keyup)="searchUser($event)" -->
          <!-- <input type="text" placeholder="Search for user..." /> -->
        </div>
        <div (click)="redirectTo('/send-promotion')">
          <button class="add-btn" title="Add New Project">+</button>
        </div>
      </div>
    </div>

    <ul
      class="no-bullets"
      style="
        display: flex;
        flex-wrap: wrap;
        overflow: auto;
        justify-content: center;
      "
    >
      <ng-container *ngIf="loading">
        <li
          style="
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding: 2em;
          border-radius: 15px;
          gap: 1em;
          background: var(--app-container);
          flex: 1 0 300px;
          margin: 1rem;
          color: #fff;
          cursor: pointer;
          max-width: 300px;
          max-height: 336px;
          "
          class="skeleton1"
          *ngFor="let i of [].constructor(12)"
        >
          <div
            style="width: 40%; height: 100px; align-self: center"
            class="skeleton2"
          ></div>
          <div
            style="
              position: relative;
              display: grid;
              grid-template-columns: 1fr 1fr;
              align-items: center;
              gap: 5px;
            "
          >
            <div style="height: 10px; width: 100%" class="skeleton2"></div>
            <div style="height: 10px; width: 100%" class="skeleton2"></div>
            <div style="height: 10px; width: 100%" class="skeleton2"></div>
            <div style="height: 10px; width: 100%" class="skeleton2"></div>
            <div style="height: 10px; width: 100%" class="skeleton2"></div>
            <div style="height: 10px; width: 100%" class="skeleton2"></div>
          </div>
          <div
            style="width: 100%; height: 20px; align-self: center"
            class="skeleton2"
          ></div>
        </li>
      </ng-container>
      <li
        style="
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding: 2em;
          border-radius: 15px;
          gap: 1em;
          background: var(--app-container);
          flex: 1 0 300px;
          margin: 1rem;
          color: #fff;
          cursor: pointer;
          max-width: 300px;
          max-height: 336px;
        "
        *ngFor="let promotion of promotions"
      >
        <div
          style="display: flex; align-items: center; justify-content: center"
        >
          <app-img
            *ngIf="promotion?._id"
            style="width: 60%; object-fit: contain;"
            [(promotionId)]="promotion._id"
            [isPromotion]="true"
            [class]="'promImgS'"
          ></app-img>
        </div>
        <div
        style="
          position: absolute;
          display: flex;
          flex-direction: column;
          gap: 5px;
          top: 1em;
          right: 2em;
        "
      >
        <div>
          <img
            (click)="openRemoveRecoverModal(RemoveRecoverContent)"
            style="width: 18px"
            src="assets/icons/promotionIcons/remove.svg"
            alt=""
          />
        </div>
        <div>
          <img
            (click)="openPlayPauseModal(PlayPauseContent)"
            style="width: 18px"
            src="assets/icons/promotionIcons/play.svg"
            alt=""
          />
        </div>
        <div>
          <img
            (click)="openUserModal(ListModalComponent)"
            style="width: 18px"
            src="assets/icons/edit.svg"
            alt=""
          />
        </div>
        <div>
          <img
            (click)="openUserModal(ListModalComponent)"
            style="width: 18px"
            src="assets/icons/promotionIcons/moreDots.svg"
            alt=""
          />
        </div>
        
      </div>

        <div
          style="
            position: relative;
            display: grid;
            grid-template-columns: 1fr 1fr;
            align-items: center;
            gap: 15px;
          "
        >
          <div
            style="
              display: grid;
              grid-column: 1/3;
              border: 1px solid var(--app-bg);
              border-radius: 10px;
              align-items: center;
              justify-content: center;
            "
          >
            <span
              style="
                text-align: center;
                font-size: 10px;
                opacity: 0.8;
                font-weight: 500;
                background: var(--app-container);
                margin-top: -8px;
                margin-bottom: 5px;
              "
              >Start Date Time</span
            ><span style="text-align: center; margin-bottom: 5px">
              {{ promotion.startPromoteDateTime | date : "M/d/yy" }} &nbsp;
              {{ promotion.startPromoteDateTime | date : "h:mm a" }}
            </span>
          </div>

          <div
            style="
              display: grid;
              border: 1px solid var(--app-bg);
              border-radius: 10px;
              align-items: center;
              justify-content: center;
            "
          >
            <span
              style="
                text-align: center;
                font-size: 10px;
                opacity: 0.8;
                font-weight: 500;
                background: var(--app-container);
                margin-top: -8px;
                margin-bottom: 5px;
              "
              >Targeted Users Total</span
            ><span style="text-align: center; margin-bottom: 5px">{{
              promotion.specificUsersCount
            }}</span>
          </div>

          <div
            style="
              display: grid;
              border: 1px solid var(--app-bg);
              border-radius: 10px;
              align-items: center;
              justify-content: center;
            "
          >
            <span
              style="
                text-align: center;
                font-size: 10px;
                opacity: 0.8;
                font-weight: 500;
                background: var(--app-container);
                margin-top: -8px;
                margin-bottom: 5px;
              "
              >Socket Notification Send</span
            ><span style="text-align: center; margin-bottom: 5px">{{
              promotion.iPromotionNotificationSendCount
            }}</span>
          </div>

          <div
            style="
              display: grid;
              border: 1px solid var(--app-bg);
              border-radius: 10px;
              align-items: center;
              justify-content: center;
            "
          >
            <span
              style="
                text-align: center;
                font-size: 10px;
                opacity: 0.8;
                font-weight: 500;
                background: var(--app-container);
                margin-top: -8px;
                margin-bottom: 5px;
              "
              >Native Sent Notification</span
            ><span style="text-align: center; margin-bottom: 5px">{{
              promotion.nativeMissedNotificationSendCount +
                promotion.nativeNotificationSendCount
            }}</span>
          </div>

          <div
            style="
              display: grid;
              border: 1px solid var(--app-bg);
              border-radius: 10px;
              align-items: center;
              justify-content: center;
            "
          >
            <span
              style="
                text-align: center;
                font-size: 10px;
                opacity: 0.8;
                font-weight: 500;
                background: var(--app-container);
                margin-top: -8px;
                margin-bottom: 5px;
              "
              >Shared</span
            ><span style="text-align: center; margin-bottom: 5px">{{
              promotion.sharedCount
            }}</span>
          </div>
        </div>

        <!-- <div style="display: flex; justify-content: center">
          <span
            style="
              background: var(--action-color);
              width: 100%;
              text-align: center;
              padding: 5px;
              border-radius: 10px;
            "
            >button</span
          >
        </div> -->
      </li>
    </ul>
    <div class="list-number disp-flex" style="">
      <div
        class="showingInfoWrapper"
        style="
          margin: 0 0 0.75rem;
          display: flex !important;
          align-items: center;
          gap: 1em;
        "
      >
        <span class="showingInfo"
          >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
          {{
            pageNo * resultsPerPage - (resultsPerPage - promotions.length)
          }}
          of {{ count }}</span
        >
        <div style="border: 1px solid gray; border-radius: 15px">
          <div>
            <input style="display: none" id="dropdownInput" type="checkbox" />
            <div
              style="
                display: flex;
                justify-content: center;
                height: 45px;
                width: 50px;
              "
              class="dropdown"
            >
              <select
                style="
                  display: flex;
                  background: transparent;
                  border: none;
                  color: white;
                  font-size: 18px;
                  width: 90%;
                  font-weight: 600;
                "
                (ngModelChange)="resultsPerPageChanged($event)"
                [(ngModel)]="resultsPerPage"
              >
                <option class="optionStyle colorCancel">12</option>
                <option class="optionStyle colorCancel">18</option>
                <option class="optionStyle colorCancel">24</option>
                <option class="optionStyle colorCancel">30</option>
                <option class="optionStyle colorCancel">36</option>
                <option class="optionStyle colorCancel">42</option>
              </select>
              <label for="dropdownInput" class="overlay"></label>
            </div>
          </div>
        </div>
      </div>
      <nav>
        <ul class="pager">
          <li class="pager__item pager__item--prev">
            <a
              *ngIf="pageNo !== 1"
              (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Previous
            </a>
          </li>
          <li *ngIf="pageNo !== 1" class="pager__item">
            <a (click)="setPage(1)" class="pager__link">...</a>
          </li>
          <li
            *ngFor="
              let item of [].constructor(pageNoTotal) | slice : 0 : 5;
              let i = index
            "
            [ngClass]="pageNo + i === pageNo ? 'active' : ''"
            class="pager__item"
          >
            <a
              *ngIf="pageNo + i <= pageNoTotal"
              (click)="setPage(pageNo + i)"
              class="pager__link"
              >{{ pageNo + i }}</a
            >
          </li>
          <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
            <a (click)="setPage(pageNoTotal)" class="pager__link">...</a>
          </li>
          <li
            *ngIf="pageNo !== pageNoTotal"
            class="pager__item pager__item--next"
          >
            <a
              (click)="pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Next
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>

  <ng-template #ListModalComponent let-modal>
    <div style="background-color: var(--app-bg); border-radius: 5px">
      <div class="modal-body">
        <!-- header -->
        <div
          style="
            display: flex;
            justify-content: center;
            width: 100%;
            position: relative;
          "
        >
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; position: absolute; right: 0"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->

        <div style="margin: 1em 0; display: flex">
          <div style="flex: 1 0 30%">
            <div
              style="
                display: flex;
                flex-direction: column;
                gap: 1em;
                align-items: center;
              "
            >
              <img
                style="width: 50%; border-radius: 20px"
                src="../../../../assets/unnamed.png"
                alt=""
              />
              <div
                style="
                  background-color: var(--sidebar);
                  width: 75%;
                  padding: 18px 1em;
                  border-radius: 10px;
                "
              >
                <div
                  style="
                    display: grid;
                    border: 1px solid var(--app-bg);
                    border-radius: 10px;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                  "
                >
                  <span style="position: absolute; top: -10px; left: 1em;"
                    >Time sent</span
                  >
                  <span style="line-height: 3">Time sent</span>
                </div>
              </div>

              <div
                style="
                  background-color: var(--sidebar);
                  width: 75%;
                  padding: 18px 1em;
                  border-radius: 10px;
                "
              >
                <div
                  style="
                    display: grid;
                    border: 1px solid var(--app-bg);
                    border-radius: 10px;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                  "
                >
                  <span style="position: absolute; top: -10px; left: 1em;"
                    >Created</span
                  >
                  <span style="line-height: 3">Created</span>
                </div>
              </div>
              <div
                style="
                  background-color: var(--sidebar);
                  width: 75%;
                  padding: 18px 1em;
                  border-radius: 10px;
                "
              >
                <div
                  style="
                    display: grid;
                    border: 1px solid var(--app-bg);
                    border-radius: 10px;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                  "
                >
                  <span style="position: absolute; top: -10px; left: 1em;"
                    >Targeted users</span
                  >
                  <span style="line-height: 3">200</span>
                </div>
              </div>
              <div
                style="
                  background-color: var(--sidebar);
                  width: 75%;
                  padding: 18px 1em;
                  border-radius: 10px;
                "
              >
                <div
                  style="
                    display: grid;
                    border: 1px solid var(--app-bg);
                    border-radius: 10px;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                  "
                >
                  <span style="position: absolute; top: -10px; left: 1em;"
                    ><img
                      style="width: 17px; margin-right: 5px"
                      src="../../../../assets/icons/promotionIcons/share.svg"
                      alt=""
                    />
                    Shared</span
                  >
                  <span style="line-height: 3">200</span>
                </div>
              </div>
              <div
                style="
                  background-color: var(--sidebar);
                  width: 75%;
                  padding: 18px 1em;
                  border-radius: 10px;
                "
              >
                <div
                  style="
                    display: grid;
                    border: 1px solid var(--app-bg);
                    border-radius: 10px;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                  "
                >
                  <span style="position: absolute; top: -10px; left: 1em;"
                    ><img
                      style="width: 17px; margin-right: 5px"
                      src="../../../../assets/icons/promotionIcons/envelopeDot.svg"
                      alt=""
                    />Native notification</span
                  >
                  <span style="line-height: 3">2000</span>
                </div>
              </div>
              <div
                style="
                  background-color: var(--sidebar);
                  width: 75%;
                  padding: 18px 1em;
                  border-radius: 10px;
                "
              >
                <div
                  style="
                    display: grid;
                    border: 1px solid var(--app-bg);
                    border-radius: 10px;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                  "
                >
                  <span style="position: absolute; top: -10px; left: 1em;"
                    ><img
                      style="width: 17px; margin-right: 5px"
                      src="../../../../assets/icons/promotionIcons/bell.svg"
                      alt=""
                    />Socked notification</span
                  >
                  <span style="line-height: 3">200</span>
                </div>
              </div>
            </div>
          </div>
          <div style="flex: 1 0 70%">
            <div
              style="
                /* display: none !important; */
                display: grid;
                grid-template-rows: 4em 1fr 4em;
                background-color: var(--sidebar);
                padding: 5px 1em;
                border-radius: 40px;
                height: 85vh;
              "
            >
              <div class="disp-flex a-i-center">
                <span style="margin-left: 1em" class="no-wrap-1-line"
                  >Gifts</span
                >
                <div class="m-l-auto disp-flex">
                  <div>
                    <input
                      style="display: none"
                      id="dropdownInput"
                      type="checkbox"
                    />
                    <div
                      style="
                        display: flex;
                        justify-content: center;
                        height: 35px;
                        width: 147px;
                      "
                      class="dropdown"
                    >
                      <select
                        style="
                          display: flex;
                          background: transparent;
                          border: none;
                          color: white;
                          font-size: 18px;
                          width: 90%;
                          font-weight: 600;
                        "
                      >
                        <option class="optionStyle colorCancel">All</option>
                        <option class="optionStyle colorCancel">
                          untrusted
                        </option>
                        <option class="optionStyle colorCancel">
                          isVerified
                        </option>
                        <option class="optionStyle colorCancel">deleted</option>
                      </select>
                      <label for="dropdownInput" class="overlay"></label>
                    </div>
                  </div>
                  <div class="search-bar">
                    <input type="text" placeholder="Search for user..." />
                  </div>
                </div>
              </div>
              <!-- <div
      *ngIf="gifts.length === 0 && !loading"
      style="
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
      "
    >
      <img
        style="width: 70%; max-width: 299px"
        src="/assets/icons/animatedIconsTable/list_empty.svg"
        alt=""
      />
      <p style="font-size: 16px">List is empty</p>
    </div> -->
              <ul
                class="no-bullets"
                style="
                  display: flex;
                  flex-wrap: wrap;
                  overflow: auto;
                  justify-content: center;
                "
              >
                <!-- skeleton -->
                <li
                  style="
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    padding: 2em;
                    border-radius: 15px;
                    gap: 1em;
                    flex: 1 0 20%;
                    margin: 1rem;
                    color: #fff;
                    cursor: pointer;
                    max-width: 145px;
                    max-height: 225px;
                  "
                  class="skeleton1"
                  *ngFor="let i of [].constructor(2)"
                >
                  <div
                    style="
                      border-radius: 50%;
                      width: 55px;
                      height: 55px;
                      min-width: 55px;
                      min-height: 55px;
                      align-self: center;
                    "
                    class="skeleton2"
                  ></div>
                  <div style="display: flex; gap: 5px">
                    <div
                      style="width: 100%; height: 40px; align-self: center"
                      class="skeleton2"
                    ></div>
                  </div>

                  <div
                    style="
                      position: relative;
                      display: grid;
                      grid-template-columns: 1fr 1fr 1fr;
                      align-items: center;
                      gap: 5px;
                    "
                  >
                    <div
                      style="height: 30px; width: 100%"
                      class="skeleton2"
                    ></div>
                    <div
                      style="height: 30px; width: 100%"
                      class="skeleton2"
                    ></div>
                    <div
                      style="height: 30px; width: 100%"
                      class="skeleton2"
                    ></div>
                  </div>
                </li>

                <li
                  style="
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    padding: 2em;
                    border-radius: 15px;
                    gap: 1em;
                    background: var(--app-container);
                    flex: 1 0 20%;
                    margin: 1rem;
                    color: #fff;
                    cursor: pointer;
                    max-width: 145px;
                    max-height: 225px;
                  "
                  *ngFor="let i of [].constructor(10)"
                >
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: center;
                    "
                  >
                    <!-- [circleColor]="me?.photoColor" -->
                    <app-avatar-photo
                      [userId]="'630a2e5d4989aae851a657e4'"
                      [name]="'Fiber'"
                      [surname]="'Al'"
                      [class]="'userImgModalPromotionList'"
                      [classAvatarInitials]="'initialsClassModalPromotionList'"
                    ></app-avatar-photo>
                  </div>
                  <div
                    style="
                      position: relative;
                      display: grid;
                      grid-template-columns: 1fr;
                      align-items: center;
                      gap: 15px;
                    "
                  >
                    <div style="display: flex; gap: 5px">
                      <div
                        style="
                          display: grid;
                          /* width: 100%; */
                          border: 1px solid var(--app-bg);
                          border-radius: 10px;
                          align-items: center;
                          justify-content: center;
                          flex: 1 0 67%;
                        "
                      >
                        <span
                          style="
                            text-align: center;
                            font-size: 10px;
                            opacity: 0.8;
                            font-weight: 500;
                            background: var(--app-container);
                            margin-top: -8px;
                            margin-bottom: 5px;
                          "
                          >Name</span
                        ><span style="text-align: center; margin-bottom: 5px">
                          gift.title
                        </span>
                      </div>
                    </div>

                    <div style="display: flex; gap: 5px">
                      <div
                        style="
                          display: flex;
                          border-radius: 10px;
                          align-items: center;
                          justify-content: center;
                          flex: 1;
                        "
                      >
                        <img
                          style="width: 21px"
                          src="../../../../assets/icons/promotionIcons/envelopeDot.svg"
                          alt=""
                        />
                      </div>
                      <div
                        style="
                          display: flex;
                          flex: 1;
                          border-radius: 10px;
                          align-items: center;
                          justify-content: center;
                        "
                      >
                        <img
                          style="width: 21px"
                          src="../../../../assets/icons/promotionIcons/bell.svg"
                          alt=""
                        />
                      </div>
                      <div
                        style="
                          display: flex;
                          flex: 1;
                          border-radius: 10px;
                          align-items: center;
                          justify-content: center;
                        "
                      >
                        <img
                          style="width: 21px"
                          src="../../../../assets/icons/promotionIcons/share.svg"
                          alt=""
                        />
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
              <div class="list-number disp-flex" style="">
                <div
                  class="showingInfoWrapper"
                  style="
                    margin: 0 0 0.75rem;
                    display: flex !important;
                    align-items: center;
                    gap: 1em;
                  "
                >
                  <span class="showingInfo">
                    <!-- Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
                    {{
                      pageNo * resultsPerPage - (resultsPerPage - gifts.length)
                    }}
                    of {{ count }} -->
                  </span>
                  <div style="border: 1px solid gray; border-radius: 15px">
                    <div>
                      <input
                        style="display: none"
                        id="dropdownInput"
                        type="checkbox"
                      />
                      <div
                        style="
                          display: flex;
                          justify-content: center;
                          height: 45px;
                          width: 50px;
                        "
                        class="dropdown"
                      >
                        <select
                          style="
                            display: flex;
                            background: transparent;
                            border: none;
                            color: white;
                            font-size: 18px;
                            width: 90%;
                            font-weight: 600;
                          "
                          (ngModelChange)="resultsPerPageChanged($event)"
                          [(ngModel)]="resultsPerPage"
                        >
                          <option class="optionStyle colorCancel">12</option>
                          <option class="optionStyle colorCancel">18</option>
                          <option class="optionStyle colorCancel">24</option>
                          <option class="optionStyle colorCancel">30</option>
                          <option class="optionStyle colorCancel">36</option>
                          <option class="optionStyle colorCancel">42</option>
                        </select>
                        <label for="dropdownInput" class="overlay"></label>
                      </div>
                    </div>
                  </div>
                </div>
                <nav>
                  <ul class="pager">
                    <li class="pager__item pager__item--prev">
                      <a
                        *ngIf="pageNo !== 1"
                        (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
                        style="width: fit-content !important; padding: 0 10px"
                        class="pager__link"
                      >
                        Previous
                      </a>
                    </li>
                    <li *ngIf="pageNo !== 1" class="pager__item">
                      <a (click)="setPage(1)" class="pager__link">...</a>
                    </li>
                    <li
                      *ngFor="
                        let item of [].constructor(pageNoTotal) | slice : 0 : 5;
                        let i = index
                      "
                      [ngClass]="pageNo + i === pageNo ? 'active' : ''"
                      class="pager__item"
                    >
                      <a
                        *ngIf="pageNo + i <= pageNoTotal"
                        (click)="setPage(pageNo + i)"
                        class="pager__link"
                        >{{ pageNo + i }}</a
                      >
                    </li>
                    <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
                      <a (click)="setPage(pageNoTotal)" class="pager__link"
                        >...</a
                      >
                    </li>
                    <li
                      *ngIf="pageNo !== pageNoTotal"
                      class="pager__item pager__item--next"
                    >
                      <a
                        (click)="
                          pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''
                        "
                        style="width: fit-content !important; padding: 0 10px"
                        class="pager__link"
                      >
                        Next
                      </a>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>

  <ng-template #RemoveRecoverContent let-modal>
    <div
      style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1"
    >
      <div class="modal-body">
        <!-- header -->
        <div
          style="
            display: flex;
            justify-content: center;
            width: 100%;
            position: relative;
          "
        >
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; position: absolute; right: 0"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->

        <div style="margin: 1em 0">
          <div
            style="
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
              gap: 2rem;
            "
          >
            <img style="width: 50%" src="../../../../assets/user2.jpg" alt="" />
            <div style="width: 50%">
              <div
                style="
                  display: grid;
                  width: 100%;
                  border: 1px solid var(--app-container);
                  border-radius: 10px;
                  align-items: center;
                  justify-content: center;
                "
              >
                <span
                  style="
                    text-align: center;
                    font-size: 10px;
                    opacity: 0.8;
                    font-weight: 500;
                    background: var(--app-bg);
                    margin-top: -8px;
                    margin-bottom: 5px;
                  "
                  >Name</span
                >
                <span style="text-align: center; margin-bottom: 5px">
                  selectPromotion.title
                </span>
              </div>
            </div>
            <h5>Do you want to Remove this promotion</h5>

            <div
              style="
                width: 50%;
                display: flex;
                justify-content: space-between;
                gap: 1em;
              "
            >
              <div
                (click)="modal.dismiss('Cross click')"
                class="modalButtonsCancel"
              >
                Cancel
              </div>
              <div
                (click)="modal.dismiss('Cross click')"
                class="modalButtonsDeactivate"
              >
                Remove
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>

  <ng-template #PlayPauseContent let-modal>
    <div
      style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1"
    >
      <div class="modal-body">
        <!-- header -->
        <div
          style="
            display: flex;
            justify-content: center;
            width: 100%;
            position: relative;
          "
        >
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; position: absolute; right: 0"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->

        <div style="margin: 1em 0">
          <div
            style="
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
              gap: 2rem;
            "
          >
            <img style="width: 50%" src="../../../../assets/user2.jpg" alt="" />
            <div style="width: 50%">
              <div
                style="
                  display: grid;
                  width: 100%;
                  border: 1px solid var(--app-container);
                  border-radius: 10px;
                  align-items: center;
                  justify-content: center;
                "
              >
                <span
                  style="
                    text-align: center;
                    font-size: 10px;
                    opacity: 0.8;
                    font-weight: 500;
                    background: var(--app-bg);
                    margin-top: -8px;
                    margin-bottom: 5px;
                  "
                  >Name</span
                >
                <span style="text-align: center; margin-bottom: 5px">
                  selectPromotion.title
                </span>
              </div>
            </div>
            <h5>Do you want to Remove this promotion</h5>

            <div
              style="
                width: 50%;
                display: flex;
                justify-content: space-between;
                gap: 1em;
              "
            >
              <div
                (click)="modal.dismiss('Cross click')"
                class="modalButtonsCancel"
              >
                Cancel
              </div>
              <div
                (click)="modal.dismiss('Cross click')"
                class="modalButtonsDeactivate"
              >
                Remove
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>

</div>
