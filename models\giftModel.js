const mongoose = require("mongoose");
const { MongooseFindByReference } = require("mongoose-find-by-reference");

const giftSchema = new mongoose.Schema({
  oldID: {
    type: String,
    trim: true,
  },
  orderIndex: {
    type: Number,
  },
  cost: {
    type: Number,
    default: 0,
  },
  duration: {
    type: Number,
    default: 0,
  },
  title: {
    type: String,
    trim: true,
  },
  animationId: {
    type: String,
    trim: true,
  },
  thumbnailId: {
    type: String,
    trim: true,
  },
  active: {
    type: Boolean,
    default: true,
  },
  createdBy: {
    type: mongoose.ObjectId,
    ref: "User",
    required: [true, "Gift must been created from a User!"],
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

giftSchema.pre("save", async function (next) {
  next();
});

giftSchema.pre(/^find/, function (next) {
  next();
});
giftSchema.plugin(MongooseFindByReference);

const Gift = mongoose.model("Gift", giftSchema);

module.exports = Gift;
