// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  base_url: 'http://localhost:5858',
  instagram_token: 'INSTA_TOKEN',
  stripe_token: 'STRIPE_TOKEN',
  paypal_token: 'PAYPAL_TOKEN',
  // apiUrl:"http://************:8000/api/v1",
  // apiUrl:"https://fiber-al.herokuapp.com/api/v1",
  // apiUrl:"http://localhost:8000/api/v1",
  apiUrl:"https://api.fiber.al/api/v1",
  videoUrl:"https://video.fiber.al/api/v1",
  // imageUrl:"http://localhost:7000/api/v1",
  imageUrl:"https://images.fiber.al/api/v1",
  // socketChatEndpoint :'http://localhost:9000',
  // socketChatEndpoint :'http://*************:9000',
  socketChatEndpoint :'https://chat.fiber.al',
  // socketChatEndpoint :'https://*************:443',
  // socketEndpoint : 'http://localhost:9000'
  // socketEndpoint :'https://fiber-al-socket.herokuapp.com/'
  // socketEndpoint :'http://************:9000',
  // socketEndpoint : 'http://localhost:9000'
  // socketEndpoint :'https://socket.fiber.al:443',
  socketEndpoint :'https://socket.fiber.al:443'
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.
