import { ToastrService } from 'ngx-toastr';
import { User } from '../models/user';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, of, forkJoin } from 'rxjs';
import { take, map, tap, delay, switchMap } from 'rxjs/operators';
import { from, Observable } from 'rxjs';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';

const BACKEND_URL = environment.base_url + "/promotionAvailable/";

@Injectable({
  providedIn: 'root'
})
export class PromotionAvailableService {

  constructor(
    private http: HttpClient,
    private router: Router,
    private toastrService: ToastrService
  ) { }

  create(
    timeStart,
    timeEnd,
    user,
    promotion
  ){

    let data = {
      timeStart:timeStart,
      timeEnd: timeEnd,
      promotion:promotion,
      userAllowed: user
    }
    return  this.http.post<any>( BACKEND_URL + 'create', data)
  }


  changeActiveStatus(
    _id,
    status
  ){  
    let data = {
      "_id": _id,
      "isActive": status
    }
    return  this.http.patch<any>( BACKEND_URL + 'changeActiveStatus/'+_id ,data)
  }

}
