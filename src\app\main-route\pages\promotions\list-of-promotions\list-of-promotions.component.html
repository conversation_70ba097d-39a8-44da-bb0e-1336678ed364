<div class="container-fluid">
  <div class="page-header">
    <div class="row">
      <div class="col-lg-6">
        <div class="page-header-left">
          <h3>
            List of promotions
            <small>Fiber Admin Panel </small>
          </h3>
        </div>
      </div>
      <div class="col-lg-6">
        <ol class="breadcrumb pull-right">
          <li class="breadcrumb-item">
            <a>
              <i data-feather="home"></i>
            </a>
          </li>
          <li class="breadcrumb-item">Promotions</li>
          <li class="breadcrumb-item active">List of promotions</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<ngx-loading
  [(show)]="loading"
  [config]="{
    animationType: ngxLoadingAnimationTypes.circleSwish,
    primaryColour: '#ffffff',
    backdropBorderRadius: '3px'
  }"
></ngx-loading>

<div class="container-fluid" *ngIf="!loading">
  <div class="card">
    <div class="card-header">
      <form class="form-inline  search-box" >
        <div class="form-group">
          <!-- search-form
          [formGroup]="searchForm"
          <input formControlName="search" placeholder="Search  " /> -->
        </div>
      </form>
      <a
        [routerLink]="['/' + lang + '/new-promotion']"
        class="btn btn-primary mt-md-0 mt-2"
        ><small>Create promotion</small></a
      >
    </div>

    <div class="card-body">
      <div class="table-responsive table-desi">
        <table class="all-package coupon-table table table-striped">
          <thead>
            <tr>
              <!-- <th></th> -->
              <th>Picture</th>
              <th>Start Date Time</th>
              <!-- <th>Specific Users Count</th> -->
              <th>Targeted Users Total</th>
              <th>Socket Notification Send</th>
              <th>Native Sent Notification</th>
              <th>Shared</th>
              <th>Option</th>
            </tr>
          </thead>

          <tbody>
            <tr *ngIf="startLoading">
              <td></td>
              <td></td>
              <td></td>
              <td>
                <div 
                class="loader"></div>
              </td>
              <td></td>
              <td></td>
              <td></td>
            </tr>

            <tr
              *ngFor="
                let promotion of promotions
                  | searchFilter : searchForm.value.search;
                let i = index
              "
            >

              <td>
                {{ pageNo + i }}  &nbsp; &nbsp;
                <img
                  *ngIf="!promotion?.images[0]?.data"
                  src="assets/user2.jpg"
                  alt=""
                  class="promImg"
                />
                <app-img
                  *ngIf="promotion?.images[0]?.data"
                  [(buffer)]="promotion.images[0].data"
                  [class]="'promImg'"
                ></app-img>
              </td>


              <td style="font-weight: 600;">
                {{ promotion.startPromoteDateTime | date: 	'M/d/yy' }} 
                <br>
                {{ promotion.startPromoteDateTime | date: 	'h:mm a' }}</td>

                
              <td style="font-weight: 600;">{{ promotion.specificUsersCount }}</td>
              <td style="font-weight: 600;">{{ promotion.iPromotionNotificationSendCount }}</td>
              <td style="font-weight: 600;">{{ promotion.nativeMissedNotificationSendCount + promotion.nativeNotificationSendCount}}</td>

              <td style="font-weight: 600;">{{ promotion.sharedCount }}</td>

              <td>
                <a
                  (click)="onRemove(promotion, i, !promotion.active)"
                  style="padding-inline: 5px"
                >
                  <i
                    *ngIf="promotion.active"
                    class="fa fa-trash"
                    title="Delete"
                  ></i>
                  <i
                    *ngIf="!promotion.active"
                    class="fa fa-eye"
                    title="Recover"
                  ></i>
                </a>
                <a
                (click)="redirectTo('/promotion-details/'+ promotion?._id)"
                  style="padding-inline: 5px; "
                >
                  <img style="height: 18px;width: 17px; margin-bottom: 5px;" src="assets/svg/more.svg" alt="" />
                </a>
                <div 
                (click)="onPause(promotion, i, !promotion?.pause)"
                style="display: flex; align-items: center; justify-content: center; " >
                    <div *ngIf="promotion?.pause" style="background-color: #32dbc6; width: 50%;border-radius: 30px;cursor: pointer;"> 
                        <p style="padding: .5em 0; color: aliceblue;font-weight: 600;">Play</p>
                    </div>
                    <div *ngIf="!promotion?.pause" style="background-color: tomato; width: 50%;border-radius: 30px;cursor: pointer;"> 
                        <p style="padding: .5em 0; color: aliceblue;font-weight: 600;">Stop</p>
                    </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="card-body order-datatable">
        <div id="basic-1_wrapper" class="dataTables_wrapper no-footer">

      <div class="dataTables_info">Showing {{ (pageNo-1) * resultsPerPage + 1 }} to {{ pageNo * resultsPerPage -  (resultsPerPage - promotions.length)  }} of {{promotionCount}} </div>
      <div class="dataTables_paginate paging_simple_numbers">
        <a 
        [ngClass]="pageNo === 1 ? 'disabled' : ''"
        (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
        class="paginate_button previous">Previous</a>
        <span
        *ngIf=" pageNo !== 1 ">
          <a 
          (click)="setPage(1)"
          class="paginate_button">...</a>
        </span>
        <span *ngFor="let item of [].constructor(pageNoTotal) | slice:0:5; let i = index">
          <a 
          *ngIf="pageNo + i <= pageNoTotal"
          (click)="setPage(pageNo+i)"
          [ngClass]=" pageNo + i  === pageNo ? 'current' : ''"
          class="paginate_button">{{ pageNo + i }}</a>
        </span>
        <span
        *ngIf=" pageNo !== pageNoTotal ">
          <a 
          (click)="setPage(pageNoTotal)"
          class="paginate_button">...</a>
        </span>
        <a 
        [ngClass]="pageNo !== pageNoTotal  ? '' : 'disabled'"
        (click)="pageNo !== pageNoTotal  ? setPage(pageNo + 1) : ''"
        class="paginate_button next">Next</a>
      </div>
    </div>
    
      </div>
    </div>

  </div>

</div>


