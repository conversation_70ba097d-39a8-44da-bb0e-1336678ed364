import { Component, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-suggested',
  templateUrl: './suggested.component.html',
  styleUrls: ['./suggested.component.css']
})
export class SuggestedComponent implements OnInit {

  constructor(
    private modalService: NgbModal,

  ) {
    
   }

  ngOnInit(): void {
  }

  openDelRoleModal(addToSuggested) {
    this.modalService.open(addToSuggested, { centered: true });
  }
  openDeleteSuggested(deleteSuggested) {
    this.modalService.open(deleteSuggested, { centered: true });
  }
}
