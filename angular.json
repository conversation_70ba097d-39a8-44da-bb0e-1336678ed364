{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"Fiber-Al": {"projectType": "application", "schematics": {"@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["lodash", "rxjs-compat"], "outputPath": "dist/fiber-al", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/custom-theme.scss", "node_modules/bootstrap/dist/css/bootstrap.min.css", "src/styles.css", "node_modules/ngx-toastr/toastr.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "4mb", "maximumError": "5mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "Fiber-Al:build:production"}, "development": {"browserTarget": "Fiber-Al:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "Fiber-Al:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/ngx-toastr/toastr.css", "src/styles.css"], "scripts": []}}}}}, "cli": {"analytics": "1be19bd7-90ac-4ead-84a2-4d8ea7c6e9a8"}}