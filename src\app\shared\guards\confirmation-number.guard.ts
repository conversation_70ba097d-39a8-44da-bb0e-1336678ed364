// import { AngularFireAuth } from '@angular/fire/auth';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { CanLoad, Route, Router, UrlSegment } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class ConfirmationNumberGuard implements CanLoad {

  constructor(
    // private auth: AuthService,
    // private afAuth: AngularFireAuth,
    // private router: Router,
    // private plt: Platform
   ) {

    }

   canLoad( route: Route,
    segments: UrlSegment[],
   ): Observable<boolean> | Promise<boolean> | boolean{


    let  parsedData
    return true
    // return Plugins.Storage.get({ key: 'confirmationResult' }).then( storedData => {
    //   if (!storedData || !storedData.value) {
    //     if(this.plt.is('hybrid')){
    //       this.router.navigateByUrl('/sign-in');
    //     }else{
    //       this.router.navigateByUrl('/web-sign-in-up');
    //     }
    //     return false;
    //   }
    //   parsedData = JSON.parse(storedData.value) as { result: boolean }
    //   if( parsedData != null && parsedData?.result == true ){
    //     return true
    //   }else{
    //     if(this.plt.is('hybrid')){
    //       this.router.navigateByUrl('/sign-in');
    //     }else{
    //       this.router.navigateByUrl('/web-sign-in-up');
    //     }
    //     return false

    //   }

    // })
  }

}
