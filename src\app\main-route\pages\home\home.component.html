<div
  style="
    background-color: black;
    box-shadow: 0 14px 28px rgba(36, 36, 36, 0.25),
      0 10px 10px rgba(32, 32, 32, 0.22);
    overflow-y: scroll;
    overflow-x: hidden;
  "
  class="projects-section"
>
  <div class="dashContentWrapper">
    <div class="dashLeftContent">
      <div class="dashLeftHeaderWrapper">
        <span class="dashLeftHeader">Income Sources</span>
        <span class="dashLeftHeaderContent"
          >Grand totla of income, and their breakdowns showing the achievements
          precentage and highlight for most value source, Marketing strategies,
          and operating profits</span
        >
      </div>
      <div class="leftContentTabs">
        <span class="shadow1 active">Jan</span><span class="shadow1">Feb</span
        ><span class="shadow1">Mar</span><span class="shadow1">Apr</span
        ><span class="shadow1">May</span>
      </div>
      <div class="statisticsWrapper">
        <p class="finStats">Financial Statistics</p>
        <p class="finStatsNum">898,932</p>
        <p class="finStatsTarget">Income Targets 720,883</p>
      </div>
      <div class="">
        <div style="display: grid; height: 100%" class="slds-p-top--medium">
          <div style="">
            <svg
              version="1.2"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              class="quiz-graph"
              style="scale: 1.1"
            >
              <defs>
                <pattern
                  id="grid"
                  width="50"
                  height="50"
                  patternUnits="userSpaceOnUse"
                >
                  <path
                    d="M 50 0 L 0 0 0 50"
                    fill="none"
                    stroke="#1a213188"
                    stroke-width="1"
                  ></path>
                </pattern>
              </defs>
              <rect
                style="box-sizing: content-box; scale: 0.35"
                x="50"
                width="calc(100% - 50px)"
                height="300px"
                fill="url(#grid)"
                stroke="transparent"
              ></rect>

              <g style="scale: 0.335" class="x-labels">
                <text fill="#ccc" x="50" y="320">JAN</text>
                <text fill="#ccc" x="115.5" y="320">FEB</text>
                <text fill="#ccc" x="181" y="320">MAR</text>
                <text fill="#ccc" x="246.5" y="320">APR</text>
                <text fill="#ccc" x="312" y="320">MAY</text>
                <text fill="#ccc" x="377.5" y="320">JUN</text>
                <text fill="#ccc" x="443" y="320">JUY</text>
                <text fill="#ccc" x="508.5" y="320">AUG</text>
                <text fill="#ccc" x="574" y="320">SEP</text>
                <text fill="#ccc" x="639.5" y="320">OCT</text>
                <text fill="#ccc" x="705" y="320">NOV</text>
                <text fill="#ccc" x="770" y="320">DEC</text>
              </g>
              <linearGradient
                id="grad"
                x1="0%"
                y1="0%"
                x2="0%"
                y2="100%"
                style="scale: 0.35"
              >
                <stop
                  offset="0%"
                  style="stop-color: #32dbc688; stop-opacity: 1"
                ></stop>
                <stop
                  offset="100%"
                  style="stop-color: #32dbc606; stop-opacity: 0"
                ></stop>
              </linearGradient>
              <polyline
                style="scale: 0.35"
                fill="url(#grad)"
                stroke="#32dbc6"
                stroke-width="0"
                points="
          50,700
          51,100
          150,100
          250,80
          350,160
          450,100
          550,100
          650,150
          750,200
          750,300
          "
              ></polyline>

              <polyline
                style="scale: 0.35"
                fill="none"
                stroke="#32dbc6"
                stroke-width="2"
                points="
          50,100
          150,100
          250,80
          350,160
          450,100
          550,100
          650,150
          750,200
          "
              ></polyline>
            </svg>
          </div>
        </div>
      </div>
      <div class="quantityOfItemWrapper">
        <p class="quantityOfItemHeader">Quantity of Items's</p>
        <div style="gap: 0.3em" class="disp-grid">
          <div class="quantityOfItem">
            <span>•</span>
            <span>Usage fees</span>
            <span>10%</span>
            <span>11,845</span>
          </div>
          <div class="quantityOfItem">
            <span>•</span>
            <span>Subscription</span>
            <span>10%</span>
            <span>11,845</span>
          </div>
          <div class="quantityOfItem">
            <span>•</span>
            <span>Renting</span>
            <span>10%</span>
            <span>11,845</span>
          </div>
          <div class="quantityOfItem">
            <span>•</span>
            <span>Licensing</span>
            <span>10%</span>
            <span>11,845</span>
          </div>
          <div class="quantityOfItem">
            <span>•</span>
            <span>Advertising</span>
            <span>10%</span>
            <span>11,845</span>
          </div>
          <div class="quantityOfItem">
            <span>•</span>
            <span>Assets sale</span>
            <span>10%</span>
            <span>11,845</span>
          </div>
        </div>
        <div class="list-number" style="width: 90%">
          <nav style="width: 100%">
            <ul class="pager">
              <li class="pager__item pager__item--prev">
                <a class="pager__link" href="#">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="8"
                    height="12"
                    viewbox="0 0 8 12"
                  >
                    <g fill="none" fill-rule="evenodd">
                      <path
                        fill="#33313C"
                        d="M7.41 1.41L6 0 0 6l6 6 1.41-1.41L2.83 6z"
                      ></path>
                    </g>
                  </svg>
                </a>
              </li>
              <li class="pager__item">
                <a class="pager__link" href="#">...</a>
              </li>
              <li class="pager__item">
                <a class="pager__link" href="#">1</a>
              </li>
              <li class="pager__item">
                <a class="pager__link" href="#">2</a>
              </li>
              <li class="pager__item">
                <a class="pager__link" href="#">3</a>
              </li>

              <li class="pager__item">
                <a class="pager__link" href="#">...</a>
              </li>
              <li class="pager__item pager__item--next">
                <a class="pager__link" href="#">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="8"
                    height="12"
                    viewbox="0 0 8 12"
                  >
                    <g fill="none" fill-rule="evenodd">
                      <path
                        fill="#33313C"
                        d="M7.41 1.41L6 0 0 6l6 6 1.41-1.41L2.83 6z"
                      ></path>
                    </g>
                  </svg>
                </a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
    <div
      style="display: flex; justify-content: center; align-items: flex-start"
    >
      <div
        style="
          aspect-ratio: 4/3;
          width: 90%;

          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          background-image: url(/assets/circle_svg.svg);
          -webkit-background-size: contain;
          -moz-background-size: contain;
          -o-background-size: contain;
          background-size: contain;
          background-repeat: no-repeat;
        "
      >
        <div></div>

        <div id="div1" class="mWrapper" style="top: 5%; left: 75%">
          25%
          <span class="mText no-wrap-1-line-200" style="right: 90%; bottom: 80%"
            >Company website <br />
            31,123
          </span>
        </div>
        <div id="div1" class="mWrapper" style="top: 5%; left: 85%">
          25%
          <span class="mText no-wrap-1-line-200" style="left: 90%; bottom: 60%"
            >Company website <br />
            31,123
          </span>
        </div>
        <div id="div1" class="mWrapper" style="top: 15%; left: 90%">
          25%
          <span class="mText no-wrap-1-line-200" style="left: 90%; bottom: 50%"
            >Company website <br />
            31,123
          </span>
        </div>
        <div id="div1" class="mWrapper" style="top: 25%; left: 88%">
          25%
          <span class="mText no-wrap-1-line-200" style="left: 90%; bottom: 40%"
            >Company website <br />
            31,123
          </span>
        </div>

        <div id="div1" class="mWrapper" style="top: 80%; left: 80%">
          25%
          <span class="mText no-wrap-1-line-200" style="right: 90%; bottom: 80%"
            >Company website <br />
            31,123
          </span>
        </div>
        <div id="div1" class="mWrapper" style="top: 90%; left: 90%">
          25%
          <span class="mText no-wrap-1-line-200" style="left: 90%; bottom: 80%"
            >Company website <br />
            31,123
          </span>
        </div>

        <div id="div1" class="mWrapper" style="top: 90%; left: 44%">
          25%
          <span class="mText no-wrap-1-line-200" style="right: 90%; bottom: 70%"
            >Company website <br />
            31,123
          </span>
        </div>
        <div id="div1" class="mWrapper" style="top: 88%; left: 64%">
          25%
          <span class="mText no-wrap-1-line-200" style="left: 100%; bottom: 0%"
            >Company website <br />
            31,123
          </span>
        </div>
        <div id="div1" class="mWrapper" style="top: 80%; left: 44%">
          25%
          <span class="mText no-wrap-1-line-200" style="right: 90%; bottom: 80%"
            >Company website <br />
            31,123
          </span>
        </div>

        <div id="div1" class="mWrapper" style="top: 55%; left: 10%">
          25%
          <span class="mText no-wrap-1-line-200" style="right: 90%; bottom: 60%"
            >Company website <br />
            31,123
          </span>
        </div>
        <div id="div1" class="mWrapper" style="top: 85%; left: 10%">
          25%
          <span class="mText no-wrap-1-line-200" style="right: 90%; bottom: 60%"
            >Company website <br />
            31,123
          </span>
        </div>

        <div id="div1" class="mWrapper" style="top: 5%; left: 18%">
          25%
          <span class="mText no-wrap-1-line-200" style="right: 90%; bottom: 70%"
            >Company website <br />
            31,123
          </span>
        </div>
        <div id="div1" class="mWrapper" style="top: 25%; left: 5%">
          25%
          <span class="mText no-wrap-1-line-200" style="left: 20%; top: 110%"
            >Company website <br />
            31,123
          </span>
        </div>

        <div class="lWrapper" style="top: 15%; left: 80%">25%</div>
        <div class="lWrapper" style="top: 70%; left: 90%">25%</div>
        <div class="lWrapper" style="top: 88%; left: 54%">25%</div>
        <div class="lWrapper" style="top: 70%; left: 15%">25%</div>
        <div class="lWrapper" style="top: 15%; left: 10%">25%</div>

        <div
          id="div1"
          class="KWrapper no-wrap-1-line-200"
          style="top: 10%; left: 60%"
        >
          <p>61,204</p>
          <p>Usage fees</p>
        </div>
        <div
          id="div1"
          class="KWrapper no-wrap-1-line-200"
          style="top: 32%; left: 75%"
        >
          <p>61,204</p>
          <p>Usage fees</p>
        </div>
        <div
          id="div1"
          class="KWrapper no-wrap-1-line-200"
          style="top: 20%; left: 20%"
        >
          <p>61,204</p>
          <p>Usage fees</p>
        </div>
        <div
          id="div1"
          class="KWrapper no-wrap-1-line-200"
          style="top: 60%; left: 80%"
        >
          <p>61,204</p>
          <p>Usage fees</p>
        </div>
        <div
          id="div1"
          class="KWrapper no-wrap-1-line-200"
          style="top: 75%; left: 54%"
        >
          <p>61,204</p>
          <p>Usage fees</p>
        </div>
        <div
          id="div1"
          class="KWrapper no-wrap-1-line-200"
          style="top: 60%; left: 25%"
        >
          <p>61,204</p>
          <p>Usage fees</p>
        </div>

        <div class="nucleus">
          <img style="height: 21px" src="../assets/icons/money.svg" alt="" />
          <span
            class="no-wrap-1-line-200"
            style="
              font-size: 30px;
              /* margin: 0 0 12px; */
              text-align: center;
              font-weight: 500;
            "
            >80%</span
          >
          <span
            class="no-wrap-1-line-200"
            style="
              font-size: 9px;
              margin: 0 0 12px;
              text-align: center;
              font-weight: 500;
            "
            >Income Achieved</span
          >
        </div>

        <svg width="100%" height="100%">
          <line x1="60%" y1="10%" x2="50%" y2="50%" stroke="purple"></line>
          <line x1="75%" y1="32%" x2="50%" y2="50%" stroke="purple"></line>
          <line x1="80%" y1="60%" x2="50%" y2="50%" stroke="purple"></line>
          <line x1="54%" y1="75%" x2="50%" y2="50%" stroke="purple"></line>
          <line x1="25%" y1="60%" x2="50%" y2="50%" stroke="purple"></line>
          <line x1="20%" y1="20%" x2="50%" y2="50%" stroke="purple"></line>

          <line x1="54%" y1="75%" x2="50%" y2="50%" stroke="purple"></line>
          <line x1="80%" y1="15%" x2="75%" y2="32%" stroke="purple"></line>
          <line x1="90%" y1="70%" x2="80%" y2="60%" stroke="purple"></line>
          <line x1="54%" y1="88%" x2="54%" y2="75%" stroke="purple"></line>
          <line x1="15%" y1="70%" x2="25%" y2="60%" stroke="purple"></line>
          <line x1="10%" y1="15%" x2="20%" y2="20%" stroke="purple"></line>

          <line x1="75%" y1="5%" x2="80%" y2="15%" stroke="gray"></line>
          <line x1="85%" y1="5%" x2="80%" y2="15%" stroke="gray"></line>
          <line x1="90%" y1="15%" x2="80%" y2="15%" stroke="gray"></line>
          <line x1="88%" y1="25%" x2="80%" y2="15%" stroke="gray"></line>

          <line x1="80%" y1="80%" x2="90%" y2="70%" stroke="gray"></line>
          <line x1="90%" y1="90%" x2="90%" y2="70%" stroke="gray"></line>

          <line x1="44%" y1="90%" x2="54%" y2="88%" stroke="gray"></line>
          <line x1="64%" y1="88%" x2="54%" y2="88%" stroke="gray"></line>
          <line x1="44%" y1="80%" x2="54%" y2="88%" stroke="gray"></line>

          <line x1="10%" y1="55%" x2="15%" y2="70%" stroke="gray"></line>
          <line x1="10%" y1="85%" x2="15%" y2="70%" stroke="gray"></line>

          <line x1="18%" y1="5%" x2="10%" y2="15%" stroke="gray"></line>
          <line x1="5%" y1="25%" x2="10%" y2="15%" stroke="gray"></line>
        </svg>
      </div>
    </div>
    <div class="dashRightContent" style="">
      <div class="dashLeftFirst">
        <div style="padding: 0.3em; display: grid; gap: 0.4em" class="">
          <p class="finRightNum">
            <!-- 98,932 -->
          </p>
          <p class="finRightStats">
            Average
            <span class="finRightTarget">Monthly Income</span>
          </p>
        </div>
      </div>
      <div style="display: grid" class="">
        <p class="finRightStats">
          Average
          <span class="finRightTarget">Monthly Income</span>
        </p>
        <table cellspacing="0">
          <tr>
            <th>Dex</th>
            <td>
              <!-- <div
                class="value"
                style="
                  width: calc(30%);
                  background-image: linear-gradient(-90deg, purple, lime);
                "
              ></div> -->
            </td>
          </tr>
          <tr>
            <th>Nov</th>
            <td>
              <!-- <div
                class="value"
                style="
                  width: calc(35%);
                  background-image: linear-gradient(-90deg, purple, lime);
                "
              ></div> -->
            </td>
          </tr>
          <tr>
            <th>Oct</th>
            <td>
              <!-- <div
                class="value"
                style="
                  width: calc(10%);
                  background-image: linear-gradient(-90deg, purple, lime);
                "
              ></div> -->
            </td>
          </tr>
          <tr>
            <th>Sep</th>
            <td>
              <!-- <div
                class="value"
                style="
                  width: calc(43%);
                  background-image: linear-gradient(-90deg, purple, lime);
                "
              ></div> -->
            </td>
          </tr>
          <tr>
            <th>Aug</th>
            <td>
              <!-- <div
                class="value"
                style="
                  width: calc(64%);
                  background-image: linear-gradient(-90deg, purple, lime);
                "
              ></div> -->
            </td>
          </tr>
          <tr>
            <th>Jul</th>
            <td>
              <!-- <div
                class="value"
                style="
                  width: calc(23%);
                  background-image: linear-gradient(-90deg, purple, lime);
                "
              ></div> -->
            </td>
          </tr>
          <tr>
            <th>Jun</th>
            <td>
              <!-- <div
                class="value"
                style="
                  width: calc(42%);
                  background-image: linear-gradient(-90deg, purple, lime);
                "
              ></div> -->
            </td>
          </tr>
          <tr>
            <th>May</th>
            <td>
              <!-- <div
                class="value"
                style="
                  width: calc(32%);
                  background-image: linear-gradient(-90deg, purple, lime);
                "
              ></div> -->
            </td>
          </tr>
          <tr>
            <th>Apr</th>
            <td>
              <!-- <div
                class="value"
                style="
                  width: calc(12%);
                  background-image: linear-gradient(-90deg, purple, lime);
                "
              ></div> -->
            </td>
          </tr>
          <tr>
            <th>Mar</th>
            <td>
              <!-- <div
                class="value"
                style="
                  width: calc(53%);
                  background-image: linear-gradient(-90deg, purple, lime);
                "
              ></div> -->
            </td>
          </tr>
          <tr>
            <th>Feb</th>
            <td>
              <!-- <div
                class="value"
                style="
                  width: calc(44%);
                  background-image: linear-gradient(-90deg, purple, lime);
                "
              ></div> -->
            </td>
          </tr>
          <tr>
            <th>Jan</th>
            <td>
              <!-- <div
                class="value"
                style="
                  width: calc(35%);
                  background-image: linear-gradient(-90deg, purple, lime);
                "
              ></div> -->
            </td>
          </tr>
        </table>
        <p class="finRightStats">
          <!-- 144,177 -->
        </p>
      </div>
      <div class="disp-grid">
        <!-- <p style="text-align: center" class="finRightStats">
          B2B<br />
          <span style="font-size: var(--font-size-m)" class="finRightStats"
            >546,321</span
          >
          <br />

          <span style="font-size: var(--font-size-m)" class="finRightStats"
            >63.78%</span
          >
        </p>
        <div
          style="display: flex; justify-content: center; align-items: center"
        >
          <div style="width: 70%">
            <div style="width: 100% !important" class="chart-svg">
              <svg viewBox="0 0 36 36" class="circular-chart blue">
                <path
                  class="circle-bg"
                  d="M18 2.0845
                  a 15.9155 15.9155 0 0 1 0 31.831
                  a 15.9155 15.9155 0 0 1 0 -31.831"
                ></path>
                <path
                  class="circle"
                  stroke-dasharray="63.78, 100"
                  d="M18 2.0845
                  a 15.9155 15.9155 0 0 1 0 31.831
                  a 15.9155 15.9155 0 0 1 0 -31.831"
                ></path>
              </svg>
            </div>
          </div>
        </div>
        <p style="text-align: center" class="finRightStats">
          <span style="font-size: var(--font-size-m)" class="finRightStats"
            >36.21%</span
          >
          <br />

          <span style="font-size: var(--font-size-m)" class="finRightStats"
            >346,321</span
          >
          <br />
          B2C
        </p> -->
      </div>
    </div>
  </div>
</div>
