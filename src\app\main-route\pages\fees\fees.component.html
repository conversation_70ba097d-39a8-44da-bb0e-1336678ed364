<div class="projects-section settings-details-section">
  <div class="headerDetails1" style="display: flex">
    <div>
      <p
        style="font-size: 12px; font-weight: 800; line-height: 36px; margin: 0"
      >
        <span style="opacity: 0.5">Settings</span> • <span>Fees</span>
      </p>
      <p
        style="font-size: 28px; font-weight: 800; line-height: 36px; margin: 0"
      >
        Fees Details
      </p>
    </div>

    <div style="margin-left: auto; display: flex; gap: 2em">
      <p
        class=""
        style="
          font-size: 15px;
          font-weight: 400;
          line-height: 21px;
          margin: 5px 1em 1em 0;
          display: flex;
          align-self: flex-end;
          padding: 5px 1.2em;
          color: white;
          border: 1px solid var(--action-color);
          border-radius: 10px;
        "
      >
        Back
      </p>
    </div>
  </div>
  <div
    style="
      /* display: none !important; */

      display: grid;
      grid-template-rows: 4em 2em 1fr 4em;
      background-color: var(--sidebar);
      padding: 5px 1em;
      border-radius: 20px;
      height: 74vh;
      max-height: 950px;
    "
  >
    <div class="disp-flex a-i-center">
      <span class="no-wrap-1-line">Fee List</span>
      <div class="m-l-auto disp-flex">
        <div class="search-bar">
          <input type="text" placeholder="Search" />
        </div>
      </div>
    </div>
    <div
      style="
        display: grid;
        grid-template-columns: 1fr 1fr 1.5fr 0.5fr;
        background-color: rgba(26, 37, 59, 0.5);
        align-items: center;
        margin-bottom: 5px;
        border-radius: 10px;
        gap: 1em;
      "
    >
      <span style="margin-left: 1em" class="no-wrap-1-line">Fees Name</span>
      <span class="no-wrap-1-line">Amount</span>
      <span class="no-wrap-1-line">Desc.</span>
      <span class="no-wrap-1-line">Edit</span>
    </div>
    <ul style="padding-inline-start: 0px; overflow: scroll" class="">
      <!-- Skeleton -->
      <li
        *ngFor="let i of [].constructor(0)"
        class="skeleton1"
        style="
          display: grid;
          grid-template-columns: 1fr 1fr 1.5fr 0.5fr;
          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
        "
      >
        <div style="margin-left: 13px" class="disp-flex">
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 111px;
                height: 16px;
              "
            ></span>
          </div>
        </div>
        <div class="disp-grid">
          <span
            class="skeleton2"
            style="
              font-size: 14px;
              font-weight: 500;
              opacity: 0.7;
              width: 37px;
              height: 16px;
            "
          ></span>
        </div>
        <div class="disp-grid">
          <span
            class="skeleton2"
            style="
              font-size: 14px;
              font-weight: 500;
              opacity: 0.7;
              width: 277px;
              height: 17px;
            "
          ></span>
        </div>
        <div style="justify-content: center" class="disp-grid">
          <span
            class="skeleton2"
            style="
              font-size: 14px;
              font-weight: 500;
              opacity: 0.7;
              width: 27px;
              height: 26px;
            "
          ></span>
        </div>
      </li>
      <!-- Skeleton ENd -->
      <li
        *ngFor="let i of [].constructor(10)"
        style="
          display: grid;
          grid-template-columns: 1fr 1fr 1.5fr 0.5fr;
          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
        "
      >
        <div class="disp-flex gap-05">
          <span style="margin-left: 1em" class="no-wrap-1-line">Fees Name</span>
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">10</span>
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line"
            >Fee Description "Menage settings such as busines name and time
            zone"</span
          >
        </div>

        <div style="justify-content: center" class="disp-flex">
          <img
            style="height: 20px; width: 20px"
            src="assets/icons/edit.svg"
            alt=""
          />
        </div>
      </li>
      <li
        style="
          display: grid;
          grid-template-columns: 1fr 1fr 1.5fr 0.5fr;
          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
        "
      >
        <div class="disp-flex gap-05">
          <span style="margin-left: 1em" class="no-wrap-1-line">Fees Name</span>
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">10</span>
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line"
            >Fee Description "Menage settings such as busines name and time
            zone"</span
          >
        </div>

        <div style="justify-content: center" class="disp-flex">
          <img
            style="height: 20px; width: 20px"
            src="assets/icons/edit.svg"
            alt=""
          />
        </div>
      </li>
      <li
        style="
          display: grid;
          grid-template-columns: 1fr 1fr 1.5fr 0.5fr;
          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
        "
      >
        <div class="disp-flex gap-05">
          <span style="margin-left: 1em" class="no-wrap-1-line">Fees Name</span>
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">10</span>
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line"
            >Fee Description "Menage settings such as busines name and time
            zone"</span
          >
        </div>

        <div style="justify-content: center" class="disp-flex">
          <img
            style="height: 20px; width: 20px"
            src="assets/icons/edit.svg"
            alt=""
          />
        </div>
      </li>
      <li
        style="
          display: grid;
          grid-template-columns: 1fr 1fr 1.5fr 0.5fr;
          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
        "
      >
        <div class="disp-flex gap-05">
          <span style="margin-left: 1em" class="no-wrap-1-line">Fees Name</span>
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">10</span>
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line"
            >Fee Description "Menage settings such as busines name and time
            zone"</span
          >
        </div>

        <div style="justify-content: center" class="disp-flex">
          <img
            style="height: 20px; width: 20px"
            src="assets/icons/edit.svg"
            alt=""
          />
        </div>
      </li>
    </ul>
    <div class="list-number disp-flex" style="">
      <div
        class="showingInfoWrapper"
        style="
          margin: 0 0 0.75rem;
          display: flex !important;
          align-items: center;
          gap: 1em;
        "
      >
        <!-- <span class="showingInfo"
          >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
          {{ pageNo * resultsPerPage - (resultsPerPage - data.length) }} of
          {{ count }}</span
        > -->
        <span class="showingInfo">Showing 10 to 15 of 200</span>
        <div style="border: 1px solid gray; border-radius: 15px">
          <div>
            <input style="display: none" id="dropdownInput" type="checkbox" />
            <div
              style="
                display: flex;
                justify-content: center;
                height: 45px;
                width: 50px;
              "
              class="dropdown"
            >
              <select
                style="
                  display: flex;
                  background: transparent;
                  border: none;
                  color: white;
                  font-size: 18px;
                  width: 90%;
                  font-weight: 600;
                "
              >
                <!-- *ngFor="let currency of currencyTypes"
              [ngValue]="currency" -->
                <option class="optionStyle colorCancel">20</option>
                <option class="optionStyle colorCancel">30</option>
                <option class="optionStyle colorCancel">50</option>
              </select>
              <label for="dropdownInput" class="overlay"></label>
            </div>
          </div>
        </div>
      </div>
      <nav>
        <ul class="pager">
          <li class="pager__item pager__item--prev">
            <a
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
              href="#"
            >
              Previous
            </a>
          </li>
          <li class="pager__item">
            <a class="pager__link" href="#">...</a>
          </li>
          <li class="pager__item">
            <a class="pager__link" href="#">1</a>
          </li>
          <li class="pager__item">
            <a class="pager__link" href="#">2</a>
          </li>
          <li class="pager__item">
            <a class="pager__link" href="#">3</a>
          </li>
          <li class="pager__item">
            <a class="pager__link" href="#">4</a>
          </li>
          <li class="pager__item active">
            <a class="pager__link" href="#">5</a>
          </li>
          <li class="pager__item">
            <a class="pager__link" href="#">6</a>
          </li>
          <li class="pager__item">
            <a class="pager__link" href="#">...</a>
          </li>
          <li class="pager__item pager__item--next">
            <a
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
              href="#"
            >
              Next
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
  <div style="" class="disp-grid settingsDetailsWrapper">
    <div class="headerDetails" style="display: flex">
      <div>
        <p
          style="
            font-size: 12px;
            font-weight: 800;
            line-height: 36px;
            margin: 0;
          "
        >
          <span style="opacity: 0.5">Settings</span> • <span>Fees</span>
        </p>
        <p
          style="
            font-size: 28px;
            font-weight: 800;
            line-height: 36px;
            margin: 0;
          "
        >
          Fees Details
        </p>
      </div>
    </div>
    <div style="width: 100%; display: flex; flex-direction: column; gap: 2em">
      <!-- <div class="settingItemsWrapper" style="">
        <div style="">
          <div
            style="
              cursor: pointer;
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: space-between;
            "
            href=""
          >
            <div style="width: 100%">
              <p style="font-size: 17px; margin: 0 0 10px">Fee Name</p>
              <span style="opacity: 0.5; font-weight: 500; font-size: 12px"
                >Fee Description "Menage settings such as busines name and time
                zone"</span
              >
            </div>
          </div>
        </div>
        <div style="display: flex; flex-direction: column; gap: 2em">
          <div
            style="
              display: grid;
              border: 1.2px solid var(--link-color-active-bg);
              border-radius: 10px;
            "
          >
            <div style="border-bottom: 1px solid var(--link-color-hover)">
              <div
                style="
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  padding: 14px 22px 14px 22px;
                  justify-content: space-between;
                "
                href=""
              >
                <div style="width: 100%">
                  <p>Fee Amount</p>
                  <input
                    class="fee-amount-input"
                    type="number"
                    placeholder="10"
                  />
                </div>
              </div>
            </div>
            <div style="border-bottom: 1px solid var(--link-color-hover)">
              <div
                style="
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  padding: 14px 22px 14px 22px;
                  justify-content: space-between;
                "
                href=""
              >
                <div style="width: 100%">
                  <span
                    style="color: #8f949b; font-weight: 500; font-size: 12px"
                    >Fee amount is <span style="">10</span>. To change the fee
                    change the amount from above and press the Save button</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div> -->

      <div class="settingItemsWrapper" style="">
        <div style="display: flex; flex-direction: column; gap: 2em">
          <div
            style="
              display: grid;
              border: 1.2px solid var(--link-color-active-bg);
              border-radius: 10px;
            "
          >
            <div>
              <div
                style="
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  padding: 14px 22px 14px 22px;
                  justify-content: space-between;
                "
                href=""
              >
                <div style="width: 100%">
                  <p>Fee Amount</p>
                  <input
                    class="fee-amount-input"
                    type="number"
                    placeholder="10"
                  />
                </div>
              </div>
            </div>
            <div>
              <div
                style="
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  padding: 14px 22px 14px 22px;
                  justify-content: space-between;
                "
                href=""
              >
                <div style="width: 100%">
                  <p>Fee Name</p>
                  <input
                    class="fee-amount-input"
                    type="text"
                    placeholder="Fee Name"
                  />
                </div>
              </div>
            </div>
            <div style="border-bottom: 1px solid var(--link-color-hover)">
              <div
                style="
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  padding: 14px 22px 14px 22px;
                  justify-content: space-between;
                "
                href=""
              >
                <div style="width: 100%">
                  <p>Fee Description</p>
                  <textarea
                    class="fee-amount-input"
                    type="text"
                    placeholder="Where does this fee accrue"
                  >
                  </textarea>
                </div>
              </div>
            </div>
            <div
              style="
                border-bottom: 1px solid var(--link-color-hover);
                position: relative;
              "
            >
              <div
                style="
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  padding: 14px 22px 14px 22px;
                  justify-content: space-between;
                "
              >
                <div style="width: 100%; display: flex; align-items: center">
                  <p style="margin: 0; font-size: 17px; font-weight: 700">
                    Type
                  </p>
                  <form style="margin-left: auto" action="">
                    <input type="radio" name="rdo" id="yes" checked />
                    <input type="radio" name="rdo" id="no" />
                    <div class="switch">
                      <label for="yes">Fixed</label>
                      <label for="no">Percentage</label>
                      <span></span>
                    </div>
                  </form>
                </div>
              </div>
            </div>

            <div style="border-bottom: 1px solid var(--link-color-hover)">
              <div
                style="
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  padding: 14px 22px 14px 22px;
                  justify-content: space-between;
                "
                href=""
              >
                <div
                  style="
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                  "
                >
                  <span
                    style="
                      color: white;
                      font-weight: 500;
                      font-size: 13px;
                      border: 2px solid var(--action-color);
                      padding: 10px 2.5em;
                      border-radius: 10px;
                      display: flex;
                      align-items: center;
                    "
                    >Cancel</span
                  >
                  <span
                    style="
                      color: white;
                      font-weight: 500;
                      font-size: 13px;
                      background-color:var(--action-color);
                      padding: 10px 2.5em;
                      border-radius: 10px;
                      display: flex;
                      align-items: center;
                    "
                    >Add</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
