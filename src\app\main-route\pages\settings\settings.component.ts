import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.css']
})
export class SettingsComponent implements OnInit {

  @Input() lang = 'en'

  constructor(
    private router: Router
  ) { }

  ngOnInit(): void {
  }


  routeIsActive(routePath: string) {
    console.log("routeIsActive")
    return this.router.url === '/'+ this.lang+routePath;
  } 

  redirectTo(uri:string){
    this.router.navigateByUrl(this.lang+ uri)
      .then(() =>{
        // this.loadJsService.removeScripts()
      });
  }


}
