const mongoose = require('mongoose');

const roleTitleSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      trim: true
    },
    createdBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Role Title must been created from a User!']
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }

);
roleTitleSchema.pre('save', async function( next) {
  next();
});

roleTitleSchema.pre(/^find/, function(next) {
  next();
})

const RoleTitle = mongoose.model('RoleTitle', roleTitleSchema);

module.exports = RoleTitle;
