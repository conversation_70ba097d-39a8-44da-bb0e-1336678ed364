import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/services/auth.service';
import { LoadJsService } from 'src/app/shared/services/load-js.service';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ConfirmedValidator } from 'src/app/shared/validators/confirmed.validator';
import { emailValidator } from 'src/app/shared/validators/email.validators';
import { passwordValidator } from 'src/app/shared/validators/password.validator';

@Component({
  selector: 'app-auth',
  templateUrl: './auth.component.html',
  styleUrls: ['./auth.component.css']
})
export class AuthComponent implements OnInit {

  delay = ms => new Promise(res => setTimeout(res, ms));

  lang = 'en'
  params_lang = ''
  action = 'login'
  reset_firstName = ''
  reset_email = ''
  reset_token = ''
  
  signUpForm = new UntypedFormGroup({});

  loginForm: UntypedFormGroup;
  resetEmailForm: UntypedFormGroup;
  resetPasswordForm: UntypedFormGroup;
  changePasswordForm: UntypedFormGroup;
  
  public showPassword: boolean = false;
  typeInput = 'password'
  resetPasswordSuccessful = false
  resetEmail = ''
  routerLinkHome = '/' + this.lang + '/home'

  loading = false
  languages: any[] = []
  selectedLang = {
    code : "en",
    nativeName : ""
  }
  params_lang_code = 'en'
  passwordValidatorTranslate 
  disableSignUpButton = false

  constructor(
    private router: Router, 
    private authService: AuthService,
    private toastrService: ToastrService,
    private activatedRoute: ActivatedRoute,
    private formBuilder: UntypedFormBuilder,
    public loadJsService: LoadJsService) {
      this.loading = true

    }

  ngOnInit(): void {
    this.activatedRoute.params.subscribe(params => {    
        if(params['action']){
          this.action = params['action'];
        }
        if(params['lang']){
          this.lang = params['lang'];
          this.params_lang_code = this.lang
        }
    });


      this.activatedRoute.queryParams.subscribe(params => {    
        this.reset_firstName = params.firstName ? params.firstName.split(",") : [];
        this.reset_email = params.resetEmail ? params.resetEmail.split(",") : [];
        this.reset_token  = params.resetToken ? params.resetToken.split(",")  : [];
      });



      this.prepareForm()
      this.loadJsService.loadScripts()
      this.loading = false

}

async prepareForm(){
  this.resetPasswordForm = new UntypedFormGroup({
    email: new UntypedFormControl( null, [Validators.required, emailValidator ]),
  });
  this.changePasswordForm = this.formBuilder.group({
    password: new UntypedFormControl( null, [Validators.required,new passwordValidator(this.passwordValidatorTranslate).passwordValidatorMethod ]),
    confirmPassword: new UntypedFormControl( null, [Validators.required ])
  } ,{
    validator: ConfirmedValidator('password', 'confirmPassword')
  });
  this.signUpForm = this.formBuilder.group({
    name: new UntypedFormControl( null, [Validators.required ]),
    surname: new UntypedFormControl( null, [Validators.required ]),
    email: new UntypedFormControl( null, [Validators.required, emailValidator ]),
    password: new UntypedFormControl( null, [Validators.required, new passwordValidator(this.passwordValidatorTranslate).passwordValidatorMethod  ]),
    confirmPassword: new UntypedFormControl( null, [Validators.required ]),
    accepted: new UntypedFormControl( false),
  },{
    validator: ConfirmedValidator('password', 'confirmPassword')
  });
  this.loginForm = new UntypedFormGroup({
    email: new UntypedFormControl( null, [Validators.required, emailValidator ]),
    password: new UntypedFormControl( null, [Validators.required ]),
  });
}


get f(){
  return this.signUpForm.controls;
}

get f1(){
  return this.changePasswordForm.controls;
}

changeAction(newAction){
  this.router.navigateByUrl(this.router.url.replace(this.action, newAction));
}


login(){

  this.authService.login(
    this.loginForm.value.email,
    this.loginForm.value.password
  ).subscribe( respond  => {
          if( respond.status = "success"){

            if(respond.data.user.role !== 'admin'){
              this.toastrService.error( "You are not allowed. You need to be admin" );
              return
            }
            this.toastrService.success( "You have successfully logged in" );
            this.authService.setUserData(
              respond.data.user._id,
              respond.data.user.name,
              respond.data.user.surname,
              respond.data.user.bio,
              respond.data.user.email,
              respond.data.user.role,
              respond.data.user.photoProfile,
              respond.data.user.photoCover,
              respond.data.user.photoColor,
              respond.data.user.coins,
              respond.token,
              respond.tokenExpirationDate
            )
            this.router.navigateByUrl(this.lang + '/home');
            this.changePasswordForm.reset();
          }
      },
      respond_error => {
          let error_message = respond_error.error.message;
          this.toastrService.error(error_message);            
      }
  ); 

}

change(){
  this.showPassword = !this.showPassword
  this.typeInput = this.showPassword ? 'text' : 'password'
}

signUp(photo: string = 'assets/default.jpg'  , role: string = 'client'){

  this.loading = true
  if(this.disableSignUpButton){
    return
  }
  this.disableSignUpButton = true
  this.authService.signUp(   
    this.signUpForm.value.name, 
    this.signUpForm.value.surname,
    this.signUpForm.value.email,
    'admin',
    this.signUpForm.value.password,
    this.signUpForm.value.confirmPassword,
  ).subscribe( respond  => {
          if( respond.status = "success"){
            this.signUpForm.reset();
            this.authService.setUserData(
              respond.data.user._id,
              respond.data.user.name,
              respond.data.user.surname,
              respond.data.user.bio,
              respond.data.user.email,
              respond.data.user.role,
              respond.data.user.photoProfile,
              respond.data.user.photoCover,
              respond.data.user.photoColor,
              respond.data.user.coins,
              respond.token,
              respond.tokenExpirationDate
            )

            this.router.navigate(["/home"])

            this.toastrService.success('Successfully created new account ');
            this.loading = false    
            this.disableSignUpButton = false       
          }
      },
      respond_error => {

        let error_message = respond_error.error.message;
        
        if( error_message.includes('E11000 duplicate key error collection') ) {
          this.toastrService.error( 'This email address exists already');
        }else{
          this.toastrService.error(error_message);
        }

        this.loading = false           
        this.disableSignUpButton = false       

        
      }

  ); 
  
  }
  

forgotPassword(){

    this.authService.forgotPassword(
      this.resetPasswordForm.value.email
    ).subscribe( respond  => {
            if( respond.status = "success"){
              this.toastrService.success( "A link has been sent to your email" );
              this.resetPasswordSuccessful = !this.resetPasswordSuccessful
              this.resetEmail = this.resetPasswordForm.value.email
              this.resetPasswordForm.reset();
            }
        },
        respond_error => {
            let error_message = respond_error.error.message;
            this.toastrService.error(error_message);            
        }

      ); 
}


resetPassword(){
  // this.toastrService.success('Password has been changed!');
  // this.router.navigateByUrl(this.lang+'/auth/login');
  // this.changePasswordForm.reset();
  this.authService.resetPassword(
    this.changePasswordForm.value.password,
    this.changePasswordForm.value.confirmPassword,
    this.reset_token[0]
  ).subscribe( respond  => {
          if( respond.status = "success"){
            this.toastrService.success('Password has been changed!');
            this.router.navigateByUrl(this.lang+'/auth/login');

            this.changePasswordForm.reset();
          }
      },
      respond_error => {
          let error_message = respond_error.error.message;
          this.toastrService.error(error_message);            
      }
  ); 
}
done(){
  this.router.navigateByUrl(this.lang+'/auth/login')
}

refreshComponent(){
  let route  = this.router.url
  this.router.navigate(['/'+this.lang+'/loading']).then(async ()=>{
    await this.delay(1020);
    this.router.navigate([route])
});
}

changeLang(lang){
  this.selectedLang = lang
  this.lang = this.selectedLang.code
  this.router.navigateByUrl(this.router.url.replace( this.params_lang_code, this.selectedLang.code));
 
}
ngOnDestroy(){
    this.loadJsService.removeScripts()
}

}
