const mongoose = require('mongoose');
const { MongooseFindByReference } = require('mongoose-find-by-reference');

const centralBankSchema = new mongoose.Schema({
  total: {
    type: Number,
    required: [true, 'Not possible bank transaction without amount!']
  },
  creditsAmount: {
    type: Number,
    default: 0
  },
  realMoneyAmount: {
    type: Number,
    default: 0
  },
  realMoneyCurrency:{
    type: String,
    default: 'EUR'
  },
  type: {
    type: String,
    enum: ['loan-to-central-bank', 'tax-collection','selling-credits-online-payment-payPal','selling-credits'],
    required: [true, 'Not possible bank transaction without type of transaction selected!']
  },
  inflowCredits: {
    type: Boolean,
    default: false
  },
  inflowRealMoney: {
    type: Boolean,
    default: false
  },
  outflowCredits: {
    type: Boolean,
    default: false
  },
  outflowRealMoney: {
    type: Boolean,
    default: false
  },
  reason: {
    type: String,
    trim: true,
  },
  active: {
    type: Boolean,
    default: true
  },
  transaction:{
    type: mongoose.Schema.ObjectId,
    ref: 'Transaction',
    required: [true, 'must have transaction created before central bank transaction!']
  },
  createdBy:{
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: [true, 'must been created from a User!']
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});


centralBankSchema.pre(/^find/, async function(next) {
    // this.populate({
    //     path: 'createdBy',
    // });
  next();
});


centralBankSchema.plugin(MongooseFindByReference);

const CentralBank = mongoose.model('CentralBank', centralBankSchema);

module.exports = CentralBank;