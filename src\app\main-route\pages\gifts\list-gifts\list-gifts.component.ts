import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/services/auth.service';
import { GiftService } from 'src/app/shared/services/gift.service';
import { environment } from 'src/environments/environment';
const BACKEND_Image_URL = environment.imageUrl + '/post/getImage/';

@Component({
  selector: 'app-list-gifts',
  templateUrl: './list-gifts.component.html',
  styleUrls: ['./list-gifts.component.css'],
})
export class ListGiftsComponent implements OnInit {
  
  backendImageUrl = BACKEND_Image_URL

  me = null

  timeout: any = null;
  resultSearch: any = null;
  searchText = '';

  public pageNo: number = 1;
  public pageNoTotal: number = 1;
  count: number = 0;
  resultsPerPage: number = 12;
  gifts: any[] = [];
  loading = false;

  selectGift = null

  constructor(
    private router: Router,
    private toastrService: ToastrService,
    private giftService: GiftService,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private modalService: NgbModal,
  ) {
    this.authService.user.pipe().subscribe((appUser) => {
      this.me = appUser;
    });
  }

  ngOnInit(): void {
    this.loading = true;
    this.activatedRoute.queryParams.subscribe((params) => {
      this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;
    });
    this.getAll();
  }

  getAll() {
    this.giftService.getAll(this.pageNo, this.resultsPerPage).subscribe(
      async (result) => {
        if (result.status == 'success') {
          this.count = result.data.count;
          this.gifts = result.data.data;
          this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
          this.loading = false;
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }


  redirectTo(uri:string){
    this.router.navigateByUrl('/en/'+ uri)
      .then(() =>{
      });
  }
  
  

  public setPage(page: number) {
    this.gifts = [];
    this.loading = true;
    this.router
      .navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: page },
        queryParamsHandling: 'merge', // preserve the existing query params in the route
        skipLocationChange: false, // do trigger navigation
      })
      .finally(() => {
          if(this.searchText === ''){
            this.getAll();
          }else{
            // this.getAllResultSearch()
          }
      });
  }

  resultsPerPageChanged(event){
    this.loading = true;
    this.count =  1
    this.pageNoTotal = 1
    this.gifts = []
    this.resultsPerPage = Number(event)
    this.setPage(1)
  }
  openStatusChangeModal(statusContent,gift) {
    this.selectGift = gift
    this.modalService.open(statusContent, { centered: true });
  }

  action(){
        this.giftService.edit(
          this.selectGift._id,
          {
            active: !this.selectGift.active
          }
        ).subscribe(
          async (result) => {
            if (result.status == 'success') {
              this.gifts[
               this.gifts.indexOf(this.selectGift)
              ].active = !this.selectGift.active
            }
          },
          (respond_error) => {
            this.toastrService.error(
              respond_error?.error.message,
              respond_error?.name
            );
          }
        );
  }
}
