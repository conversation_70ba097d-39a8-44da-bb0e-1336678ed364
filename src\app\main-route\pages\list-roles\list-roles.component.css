.message-content {
  padding-left: 16px;
  width: 100%;
}

.star-checkbox input {
  opacity: 0;
  position: absolute;
  width: 0;
  height: 0;
}
.star-checkbox label {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.dark .star-checkbox {
  color: var(--secondary-color);
}
.dark .star-checkbox input:checked + label {
  color: var(--star);
}
.star-checkbox input:checked + label svg {
  fill: var(--star);
  transition: 0.2s;
}

.message-line {
  font-size: 14px;
  line-height: 20px;
  margin: 8px 0;
  color: var(--secondary-color);
  opacity: 0.7;
}
.message-line.time {
  text-align: right;
  margin-bottom: 0;
}

.project-boxes {
  margin: 0 -8px;
  overflow-y: auto;
}
.project-boxes.jsGridView {
  display: flex;
  flex-wrap: wrap;
}
.project-boxes.jsGridView .project-box-wrapper {
  width: 33.3%;
}
.project-boxes.jsListView .project-box {
  display: flex;
  border-radius: 10px;
  position: relative;
}
.project-boxes.jsListView .project-box > * {
  margin-right: 24px;
}
.project-boxes.jsListView .more-wrapper {
  position: absolute;
  right: 16px;
  top: 16px;
}
.project-boxes.jsListView .project-box-content-header {
  order: 1;
  max-width: 120px;
}
.project-boxes.jsListView .project-box-header {
  order: 2;
}
.project-boxes.jsListView .project-box-footer {
  order: 3;
  padding-top: 0;
  flex-direction: column;
  justify-content: flex-start;
}
.project-boxes.jsListView .project-box-footer:after {
  display: none;
}
.project-boxes.jsListView .participants {
  margin-bottom: 8px;
}
.project-boxes.jsListView .project-box-content-header p {
  text-align: left;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.project-boxes.jsListView .project-box-header > span {
  position: absolute;
  bottom: 16px;
  left: 16px;
  font-size: 12px;
}
.project-boxes.jsListView .box-progress-wrapper {
  order: 3;
  flex: 1;
}

.project-box {
  --main-color-card: #b2caf3;
  border-radius: 30px;
  padding: 16px;
  background-color: var(--main-color-card);
}
.project-box-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  color: var(--main-color);
}
.project-box-header span {
  color: #4a4a4a;
  opacity: 0.7;
  font-size: 14px;
  line-height: 16px;
}
.project-box-content-header {
  text-align: center;
  margin-bottom: 16px;
}
.project-box-content-header p {
  margin: 0;
}
.project-box-wrapper {
  padding: 8px;
  transition: 0.2s;
}

.project-btn-more {
  padding: 0;
  height: 14px;
  width: 24px;
  height: 24px;
  position: relative;
  background-color: transparent;
  border: none;
  flex-shrink: 0;
  /*&:after, &:before {
      content: '';
      position: absolute;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: var(--main-color);
      opacity: .8;
      left: 50%;
      transform: translatex(-50%);
    }
  
    &:before { top: 0;}
    &:after { bottom: 0; }*/
}

.more-wrapper {
  position: relative;
}

.box-content-header {
  font-size: 16px;
  line-height: 24px;
  font-weight: 700;
  opacity: 0.7;
}

.box-content-subheader {
  font-size: 14px;
  line-height: 24px;
  opacity: 0.7;
}

.box-progress {
  display: block;
  height: 4px;
  border-radius: 6px;
}
.box-progress-bar {
  width: 100%;
  height: 4px;
  border-radius: 6px;
  overflow: hidden;
  background-color: #fff;
  margin: 8px 0;
}
.box-progress-header {
  font-size: 14px;
  font-weight: 700;
  line-height: 16px;
  margin: 0;
}
.box-progress-percentage {
  text-align: right;
  margin: 0;
  font-size: 14px;
  font-weight: 700;
  line-height: 16px;
}

.project-box-footer {
  display: flex;
  justify-content: space-between;
  padding-top: 16px;
  position: relative;
}
.project-box-footer:after {
  content: "";
  position: absolute;
  background-color: rgba(255, 255, 255, 0.6);
  width: calc(100% + 32px);
  top: 0;
  left: -16px;
  height: 1px;
}

.participants {
  display: flex;
  align-items: center;
}
.participants img {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  overflow: hidden;
  -o-object-fit: cover;
  object-fit: cover;
}
.participants img:not(:first-child) {
  margin-left: -8px;
}

.add-participant {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: none;
  background-color: rgba(255, 255, 255, 0.6);
  margin-left: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
}

.days-left {
  background-color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  border-radius: 20px;
  flex-shrink: 0;
  padding: 6px 16px;
  font-weight: 700;
}

.mode-switch.active .moon {
  fill: var(--main-color);
}

.messages-btn {
  border-radius: 4px 0 0 4px;
  position: absolute;
  right: 0;
  top: 58px;
  background-color: var(--message-btn);
  border: none;
  color: var(--main-color);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px;
  display: none;
}

@media screen and (max-width: 980px) {
  .project-boxes.jsGridView .project-box-wrapper {
    width: 50%;
  }

  .status-number,
  .status-type {
    font-size: 14px;
  }

  .status-type:after {
    width: 4px;
    height: 4px;
  }

  .item-status {
    margin-right: 0;
  }
}
@media screen and (max-width: 880px) {
  .messages-section {
    transform: translateX(100%);
    position: absolute;
    opacity: 0;
    top: 0;
    z-index: 2;
    height: 100%;
    width: 100%;
  }
  .messages-section .messages-close {
    display: block;
  }

  .messages-btn {
    display: flex;
  }
}
@media screen and (max-width: 720px) {
  .app-name,
  .profile-btn span {
    display: none;
  }

  .add-btn,
  .notification-btn,
  .mode-switch {
    width: 20px;
    height: 20px;
  }
  .add-btn svg,
  .notification-btn svg,
  .mode-switch svg {
    width: 16px;
    height: 16px;
  }

  .app-header-right button {
    margin-left: 4px;
  }
}
@media screen and (max-width: 520px) {
  .projects-section {
    overflow: auto;
  }

  .app-sidebar,
  .app-icon {
    display: none;
  }

  .app-content {
    padding: 16px 12px 24px 12px;
  }

  .app-header {
    padding: 16px 10px;
  }

  .search-input {
    max-width: 120px;
  }

  .project-boxes.jsGridView .project-box-wrapper {
    width: 100%;
  }

  .projects-section {
    padding: 24px 16px 0 16px;
  }

  .profile-btn img {
    width: 24px;
    height: 24px;
  }

  .app-header {
    padding: 10px;
  }

  .projects-section-header p,
  .projects-section-header .time {
    font-size: 18px;
  }

  .status-type {
    padding-right: 4px;
  }
  .status-type:after {
    display: none;
  }

  .search-input {
    font-size: 14px;
  }

  .messages-btn {
    top: 48px;
  }

  .box-content-header {
    font-size: 12px;
    line-height: 16px;
  }

  .box-content-subheader {
    font-size: 12px;
    line-height: 16px;
  }

  .project-boxes.jsListView .project-box-header > span {
    font-size: 10px;
  }

  .box-progress-header {
    font-size: 12px;
  }

  .box-progress-percentage {
    font-size: 10px;
  }

  .days-left {
    font-size: 8px;
    padding: 6px 6px;
    text-align: center;
  }

  .project-boxes.jsListView .project-box > * {
    margin-right: 10px;
  }

  .project-boxes.jsListView .more-wrapper {
    right: 2px;
    top: 10px;
  }
}

.tiles {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  -moz-column-gap: 1rem;
  column-gap: 1rem;
  row-gap: 1rem;
  margin-top: 0.25rem;
}
@media (max-width: 647px) {
  .tiles {
    grid-template-columns: repeat(1, 1fr);
  }
  .tile {
    min-height: 170px !important;
  }
}

.tile {
  padding: 1rem;
  border-radius: 8px;
  background-color: #26364f4a;
  color: var(--c-gray-900);
  min-height: 168px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  transition: 0.25s ease;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.068), 0 6px 6px rgba(0, 0, 0, 0.068);
}
.tile:hover {
  transform: translateY(-5px);
}
.tile:focus-within {
  box-shadow: 0 0 0 2px var(--c-gray-800), 0 0 0 4px #48556a;
}

.tile a {
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: end;
  font-weight: 600;
}
.green {
  color: green;
}
.red {
  color: tomato;
}
.tile a .icon-button {
  color: inherit;
  border-color: inherit;
}
.tile a .icon-button:hover,
.tile a .icon-button:focus {
  background-color: transparent;
}
.tile a .icon-button:hover i,
.tile a .icon-button:focus i {
  transform: none;
}
.tile a:focus {
  box-shadow: none;
}
.tile a:after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.tile-header {
  color: #e3e3e3;
  font-size: 19px;
  font-weight: 600;
  margin: 5px;
}

.settingCheckoutHeaderWrapper {
  display: grid;
  height: 3em;
  align-items: center;
  gap: 1em;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 35px;
}

.settingCheckOutsWrapper {
  display: flex;
  flex-direction: column;
  /* height: 4em; */
  overflow-x: hidden;
  overflow-y: scroll;
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */
}

.settingCheckOutsWrapper::-webkit-scrollbar {
  display: none;
}
.settingCheckout {
  display: grid;
  min-height: 4em;
  align-items: center;
  gap: 1em;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 35px;
  border-top: 1px solid var(--sidebar-active-link);
}
.settingCheckout > span {
  color: var(--text-dark);
  font-size: var(--font-size-s);
  font-weight: 500;
}
.search-bar {
  height: 34px;
  display: flex;
  width: 100%;
  max-width: 450px;
}
.search-bar input {
  width: 100%;
  height: 100%;
  border: none;
  background-color: var(--button-bg);
  border-radius: 8px;
  font-family: var(--body-font);
  font-size: 14px;
  font-weight: 500;
  padding: 0 40px 0 16px;
  box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 56.966 56.966' fill='%23717790c7'%3e%3cpath d='M55.146 51.887L41.588 37.786A22.926 22.926 0 0046.984 23c0-12.682-10.318-23-23-23s-23 10.318-23 23 10.318 23 23 23c4.761 0 9.298-1.436 13.177-4.162l13.661 14.208c.571.593 1.339.92 2.162.92.779 0 1.518-.297 2.079-.837a3.004 3.004 0 00.083-4.242zM23.984 6c9.374 0 17 7.626 17 17s-7.626 17-17 17-17-7.626-17-17 7.626-17 17-17z'/%3e%3c/svg%3e");
  background-size: 14px;
  background-repeat: no-repeat;
  background-position: 96%;
  color: #fff;
}
.status-inProgress {
  background: #80690014;
  text-align: center;
  line-height: 2;
  border-radius: 10px;
  color: #fcb92c;
  font-size: 14px;
}
.status-completed {
  background: #00800014;
  text-align: center;
  line-height: 2;
  border-radius: 10px;
  color: var(--green);
  font-size: 14px;
}

.status-canceled {
  background: #80000031;
  text-align: center;
  line-height: 2;
  border-radius: 10px;
  color: var(--red);
  font-size: 14px;
}

/* ------------------------------ PAGINTION BEGIN ------------------------------ */
.pager {
  margin: 0 0 0.75rem;
  font-size: 0;
  text-align: center;
  padding-inline-start: 0;
}
.showingInfo {
  position: relative;
  border-radius: 4px;
  display: block;
  text-align: center;
  width: fit-content;
  height: 2.625rem;
  line-height: 2.625rem;
  margin-left: -1px;
  color: #63676c;
  text-decoration: none;
  transition: 0.3s;
}
.pager__item {
  display: inline-block;
  vertical-align: top;
  font-size: 1.125rem;
  font-weight: bold;
  margin: 0 2px;
}
.pager__item.active .pager__link {
  background-color: #32dbc6;
  border-color: #32dbc6;
  color: #fff;
  text-decoration: none;
  box-shadow: 0 0 0 2px rgba(66, 66, 66, 0.37);
}
.pager__item--prev svg,
.pager__item--next svg {
  width: 8px;
  height: 12px;
}
.pager__item--next .pager__link svg {
  transform: rotate(180deg);
  transform-origin: center center;
}
.pager__link {
  position: relative;
  border-radius: 4px;
  text-align: center;
  width: 2.625rem;
  height: 2.625rem;
  line-height: 2.625rem;
  margin-left: -1px;
  color: #63676c;
  text-decoration: none;
  transition: 0.3s;
}
.pager__link:hover,
.pager__link:focus,
.pager__link:active {
  background-color: #32dbc782;
  border-color: #32dbc782;
  color: #fff;
  text-decoration: none;
}
.pager__link:hover svg path,
.pager__link:focus svg path,
.pager__link:active svg path {
  fill: #fff;
}
.pager .pager__item.active + .pager__item .pager__link,
.pager .pager__item:hover + .pager__item .pager__link {
  border-left-color: #32dbc7d5;
}
@media screen and (max-width: 795px) {
  .showingInfoWrapper {
    display: none;
  }
  .list-number {
    justify-content: center !important;
  }
}
@media screen and (max-width: 795px) {
  .pager__item {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }
  .pager__item.active,
  .pager__item:first-of-type,
  .pager__item:last-of-type,
  .pager__item:nth-of-type(2),
  .pager__item:nth-last-of-type(2) {
    position: initial;
    top: initial;
    left: initial;
  }
  .pager__item.active + li {
    position: initial;
    top: initial;
    left: initial;
  }
}
/* ------------------------------ PAGINTION END ------------------------------ */

.chart-svg {
  position: relative;
  max-width: 60px;
  min-width: 40px;
  flex: 1;
}

.circle-bg {
  fill: none;
  stroke: #eee;
  stroke-width: 2.5;
}

.circle {
  fill: none;
  stroke-width: 3;
  stroke-linecap: round;
  -webkit-animation: progress 1s ease-out forwards;
  animation: progress 1s ease-out forwards;
}

.circular-chart.orange .circle {
  stroke: #32dbc6;
}
.circular-chart.orange .circle-bg {
  stroke: #32dbc63c;
}
.circular-chart.green .circle-bg {
  stroke: var(--green-trans);
}
.circular-chart.blue .circle {
  stroke: #32dbc6;
}
.circular-chart.blue .circle-bg {
  stroke: #557b88;
}
.circular-chart.pink .circle {
  stroke: #ff7dcb;
}
.circular-chart.pink .circle-bg {
  stroke: #6f5684;
}

.percentage {
  fill: #fff;
  font-size: 0.5em;
  text-anchor: middle;
  font-weight: 400;
}
.downAnimation {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  box-shadow: 0 0 10px 4px #df1d1d51, 0 0 10px 4px #df1d1d29;
}
.upAnimation {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  box-shadow: 0 0 10px 4px #32dbc751, 0 0 10px 4px #32dbc729;
}
.creditsInput {
    border: none;
    flex: 1;
    outline: none;
    height: 100%;
    padding: 0 20px;
    font-size: 20px;
    background-color: var(--search-area-bg);
    color: var(--main-color);
    font-weight: 600;
    width: 100%;
    border-radius: 10px;
  }
  .creditsInput:placeholder {
    color: var(--main-color);
    opacity: 0.6;
  }
@-webkit-keyframes progress {
  0% {
    stroke-dasharray: 0 100;
  }
}

@keyframes progress {
  0% {
    stroke-dasharray: 0 100;
  }
}
.optionStyle {
  font-size: 16px;
  text-align: center;
  width: 100%;
  background-color: var(--app-bg);
  line-height: 2;
  border: none;
  color: white;
  font-weight: 600;
  height: 40px;
}
p,
span,
div {
  color: white;
  font-size: 16px;
  font-weight: 600;
}
.plusButton {
    background-color: var(--app-bg);
    height: 35px;
    width: 35px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    cursor: pointer;
  }
  
  .plusButton:hover {
    background-color: var(--action-color-30) !important;
  }
  .modalButtonsCancel {
    color: white;
    font-weight: 600;
    border: 1px solid var(--action-color-50);
    cursor: pointer;
    width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 2;
    border-radius: 10px;
  }
  .modalButtonsDelete {
    color: white;
    font-weight: 600;
    background-color: var(--action-color);
    cursor: pointer;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 2;
    border-radius: 10px;
  }
  .modalButtonsDelete:hover {
    border: 1px solid var(--action-color-50);
    background-color: var(--action-color-50);
  }
  .modalButtonsCancel:hover {
    background-color: var(--action-color-30);
  }
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
