import { Component, Input, OnInit } from '@angular/core';
import { ImageService } from 'src/app/shared/services/image.service';

@Component({
  selector: 'app-avatar-photo',
  templateUrl: './avatar-photo.component.html',
  styleUrls: ['./avatar-photo.component.css']
})
export class AvatarPhotoComponent implements OnInit {

  @Input()
  public photoUrl: any = undefined;

  @Input() public imgAvatar = true
  @Input() public avtImg = false
  @Input() public avtNull = false

  @Input() class = '';
  @Input() classAvatar = '';
  @Input() classAvatarInitials = '';
  
  @Input() buffer;

  @Input()
  public name: string = '<PERSON>ber';

  @Input()
  public userId: string = null;
  
  @Input()
  public surname: string = 'Al';

  @Input()
  public circleColor: string = '#EB7181';

  public showInitials = true;
  public initials: string;

  private colors = [
      '#EB7181', // red
      '#468547', // green
      '#FFD558', // yellow
      '#3670B2', // blue
  ];
  constructor(
    public imageService: ImageService
  ) { 

  }

    ngOnInit() {
      if(this.userId){
        if(!this.buffer ){
          this.imageService.getProfileImage(this.userId).subscribe(
            async (result) => {
              if (result.status == 'success') {
                this.buffer = result.data.image.data
                // this.post.user.photoProfile = result.data.image
              }
            },
            (respond_error) => {
              // respond_error?.error.message,
              // respond_error?.name
            }
          );
        }
      }

      if(this.photoUrl){
      }else{
        this.showInitials = true;
        this.createInititals();
      }
    }




  private createInititals(): void {
      let name = "";
      let surname = "";

      for (let i = 0; i < this.name.length; i++) {
          if (this.name.charAt(i) === ' ') {
              continue;
          }

          if (this.name.charAt(i) === this.name.charAt(i).toUpperCase()) {
            name += this.name.charAt(i);

              if (name.length == 1) {
                  break;
              }
          }
      }

      for (let i = 0; i < this.surname.length; i++) {
        if (this.surname.charAt(i) === ' ') {
            continue;
        }

        if (this.surname.charAt(i) === this.surname.charAt(i).toUpperCase()) {
          surname += this.surname.charAt(i);

            if (surname.length == 1) {
                break;
            }
        }
    }

      this.initials = name + '' + surname;

  }
}
