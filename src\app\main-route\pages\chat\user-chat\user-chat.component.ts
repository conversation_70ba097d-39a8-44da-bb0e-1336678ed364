import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { AuthService } from 'src/app/shared/services/auth.service';
import { ChatRoomService } from 'src/app/shared/services/chat-room.service';
import { SocketService } from 'src/app/shared/services/socket.service';

@Component({
  selector: 'app-user-chat',
  templateUrl: './user-chat.component.html',
  styleUrls: ['./user-chat.component.css']
})
export class UserChatComponent implements OnInit {

  delay = ms => new Promise(res => setTimeout(res, ms));
  @ViewChild("toBottom") MyProp: ElementRef;  
  @Input() userChatId = null;
  @Input() userChat =null
  socket
  chatRoom  = null
  chatRoomId= null

  me: any;
  anotherUser: any;
  message: string = ''
  messages: any[] = []
  loading = true
  loadingMessages = false

  constructor(
    public toastrService: ToastrService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private chatRoomService: ChatRoomService,
    private socketService: SocketService,
    private authService: AuthService
  ) {
    this.socket = this.socketService.setupChatSocketConnection();  
    this.socket.on('getChatMessages',(data: any) => {
      if(data?.messages){
          if(data.messages.length){
            if(data?.userId === '630a2e5d4989aae851a657e4' ){
              console.log("this.messages: ",data.messages )
                this.messages = data.messages 
            }
          }
          this.scrollToBottom();
      }
      if(data?.message){

        if(data?.message?.me !== '630a2e5d4989aae851a657e4' ){
          // this.messages.push(data.message)
          this.messages = [ ...this.messages ,data.message]
          this.scrollToBottom();
        }
      }
      if(data?.usersIsOnline){
        for(let status of data?.usersIsOnline){
          if(status?._userId.toString() === this.anotherUser?._id.toString()){
            this.anotherUser.isOnline = status?.isOnline
          }
        }
      }
      if(data?.updateManyOkId?.toString() === '630a2e5d4989aae851a657e4'){
        this.messages = []
      }
      this.loadingMessages = false
    });
  }

  ngOnInit() {


    this.authService.user.pipe().subscribe(appUser => {
      this.me = appUser
    })
    if(this.userChatId !== null){
      this.getChatRoomData(this.userChatId)
    }
  }

  ngAfterViewInit() {
    this.scrollToBottom();
  }

  scrollToBottom(): void {
    setTimeout(() => {
      this.MyProp.nativeElement.scrollIntoView({behavior: 'smooth'});
    }, 200)   
  }



  getChatRoomData(userChatId){
    this.chatRoomService.getChatRoomData(
      userChatId
    ).subscribe( async respond  => {
              if( respond.status = "success"){
                this.anotherUser = respond?.data?.anotherUser
                this.chatRoomId = respond?.data?.chatRoom?._id
                this.chatRoom = respond?.data?.chatRoom
                if(this.chatRoomId){
                  this.loadingMessages = true
                  this.socket.emit('getChatMessages',{
                    me: '630a2e5d4989aae851a657e4',
                    anotherUser: this.anotherUser?._id,
                    chatRoomId: this.chatRoom?._id,
                    chat: this.chatRoom?.chat,
                  });
                }else{
                  let chat = null
                  if( '630a2e5d4989aae851a657e4' > this.anotherUser?._id){
                    chat = await '630a2e5d4989aae851a657e4' +this.anotherUser?._id.toString()
                  }else{
                    chat = await this.anotherUser?._id+'630a2e5d4989aae851a657e4'.toString()
                  }
                  this.socket.emit('getChatMessages',{
                    me: '630a2e5d4989aae851a657e4',
                    anotherUser: this.anotherUser?._id,
                    chatRoomId: null,
                    chat: chat,
                  });
                }
                this.loading = false
              }
      },
          respond_error => {
              this.loading = false
              let error_message = respond_error.error.message;
              this.toastrService.error(error_message);            
      }
    ); 
  }


  async createChatRoom(userChatId){
    await this.chatRoomService.createChatRoom(
      userChatId
    ).subscribe( async respond  => {
              if( respond.status = "success"){
                this.chatRoomId = await respond?.data?.chatRoom?._id
                this.chatRoom = respond?.data?.chatRoom
                if(this.chatRoomId){
                  await this.socket.emit('getChatMessages',{
                    me:  '630a2e5d4989aae851a657e4',
                    anotherUser: this.anotherUser?._id,
                    chatRoomId:  this.chatRoomId,
                    chat: this.chatRoom?.chat
                  });
                }
                await this.emitMessage(this.message,this.chatRoomId)
                this.scrollToBottom();
              }
      },
          respond_error => {
              this.loading = false
              let error_message = respond_error.error.message;
              this.toastrService.error(error_message);            
      }
    ); 
  }

  async send() {
    if (this.message.trim().length !== 0) {
       if(this.chatRoomId){
        this.emitMessage(this.message,this.chatRoomId)
      }else{
        await this.createChatRoom(this.anotherUser?._id)
      }
    }else{
      this.message = ''
    }
  }

  async emitMessage(text,chat){


      this.messages = [ ...this.messages ,{
        text: text,
        adminUser: {
          _id: this.me?._id,
          name: this.me?.name,
          surname: this.me?.surname,
          isVerified: this.me?.isVerified,
          photoColor: this.me?.photoColor,
          untrusted:  this.me?.untrusted
        },
        me: '630a2e5d4989aae851a657e4',
        anotherUser: this.anotherUser?._id,
        chatRoomId: this.chatRoom?._id,
        chat: this.chatRoom?.chat,
        createdAt: Date.now()
      }]

    this.scrollToBottom();
    this.socket.emit('createChatMessages',{
      text: text,
      adminUser:this.me?._id,
      me: '630a2e5d4989aae851a657e4',
      anotherUser: this.anotherUser?._id,
      chatRoomId:  this.chatRoom?._id,
      chat: this.chatRoom?.chat,
      });


    this.message = '';
  }

  humanizingDate(createdAt){
    return  new Date(createdAt);
  }


  ngOnDestroy(){
    // console.log("ecni ma hani pallen")
  }

}
