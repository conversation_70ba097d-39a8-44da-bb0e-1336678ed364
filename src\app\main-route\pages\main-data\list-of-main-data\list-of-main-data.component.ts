import { Component, OnInit, TemplateRef } from '@angular/core';
import { UntypedFormBuilder } from '@angular/forms';
import { ActivatedRoute ,Router} from '@angular/router';
import { ngxLoadingAnimationTypes } from 'ngx-loading';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { AuthService } from 'src/app/shared/services/auth.service';
import { LoadJsService } from 'src/app/shared/services/load-js.service';
import { MainDataService } from 'src/app/shared/services/main-data.service';
import { PromotionService } from 'src/app/shared/services/promotion.service';

@Component({
  selector: 'app-list-of-main-data',
  templateUrl: './list-of-main-data.component.html',
  styleUrls: ['./list-of-main-data.component.css']
})
export class ListOfMainDataComponent implements OnInit {

  lang = 'en';
  searchForm;
  
  loading = false

  data: any[] = []

  public ngxLoadingAnimationTypes = ngxLoadingAnimationTypes;
  public loadingTemplate: TemplateRef<any>;

  startLoading = false
  public pageNo: number = 1;
  public pageNoTotal: number = 1;
  count : number = 0;
  resultsPerPage : number = 5;

  me = null
  variableName = null
  createLoading = false
  constructor(
    private authService: AuthService,
    private mainDataService: MainDataService,
    private formBuilder: UntypedFormBuilder,
    private toastrService: ToastrService,
    public loadJsService: LoadJsService,
    private activatedRoute: ActivatedRoute,
    private router: Router
  ) { 
    this.authService.user.pipe().subscribe( async appUser => {
      this.me = appUser
    })
    this.searchForm = this.formBuilder.group({
      search: '',
    });
    this.loadJsService.loadScripts()
  }

  ngOnInit(): void {
    this.loading = true

    this.activatedRoute.params.subscribe( async paramMap => {
      if(paramMap['lang']){
        this.lang = paramMap['lang'];
      }
    })

    this.activatedRoute.queryParams.subscribe(params => {
      this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;
    })

    this.getAll()


  }

  public setPage(page: number) {
    this.data = []
    this.startLoading = true
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { page: page },
      queryParamsHandling: 'merge', // preserve the existing query params in the route
      skipLocationChange: false  // do trigger navigation
    }).finally(() => {
      this.getAll()
      // this.viewScroller.setOffset([120, 120]);
      // this.viewScroller.scrollToAnchor('deals'); // Anchore Link
    });
  }

  getAll(){
    this.mainDataService.getAll(this.pageNo,this.resultsPerPage).subscribe( 
     async result =>{
        if(result.status == "success"){
              // this.users = await result.data.data.filter( user  =>{
              //   return  user._id !== this.user._id
              // });
              this.count = result.data.count
              this.data = result.data.data
              this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1 
              this.loading = false
              this.startLoading = false
          }

      },
      respond_error => {
       this.toastrService.error(
         respond_error?.error.message
          , respond_error?.name);         
       }
    )
  }

  submit(){
    let v = this.variableName.trim() 
    if( v === ''){
      this.toastrService.error('Variable Name not empty');
      return 
    }
    this.createLoading = true
    this.mainDataService.create(   
      {
        variableName : this.variableName,
        createdBy : this.me._id
      }
    ).subscribe( respond  => {
            if( respond.status = "success"){
              this.variableName = ''
              this.data = [respond.data.data,...this.data]
              this.toastrService.success( " Successfully created new" );
              this.createLoading = false
            }
        },
        respond_error => {
          let error_message = respond_error.error.message;
          this.toastrService.error(error_message);
          this.createLoading = false
        }

      ); 
  }

  ngOnDestroy(){
    this.loadJsService.removeScripts()
  }
  redirectTo(uri:string){
    this.router.navigateByUrl(this.lang+ uri)
      .then(() =>{
        this.loadJsService.removeScripts()
      });
  }

  resultsPerPageChanged(event){
    this.loading = true;
    this.startLoading = true;
    this.count =  1
    this.pageNoTotal = 1
    this.data = []
    this.resultsPerPage = Number(event)
    this.setPage(1)
  }

}
