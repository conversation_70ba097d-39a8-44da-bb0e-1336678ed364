import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, of, forkJoin } from 'rxjs';
import { take, map, tap, delay, switchMap } from 'rxjs/operators';
import { from, Observable } from 'rxjs';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';
import { AuthService } from './auth.service';
import { User } from '../models/user';

const BACKEND_URL = environment.apiUrl + "/chatRoom/";

@Injectable({
  providedIn: 'root'
})
export class ChatRoomService {

  me: User;
  private chatRoomsSource = new BehaviorSubject(null);
  get chatRooms() {
    return this.chatRoomsSource.asObservable().pipe(
      map(data => {
        if (data) {
          return data;
        } else {
          return null;
        }
      })
    );
  }

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {
    this.authService.user.pipe().subscribe(appUser => {
      this.me = appUser
    })
   }

  changeChatRooms(rooms) {
    this.chatRoomsSource.next(rooms)
  }

  createChatRoom(_id){
    return this.http.get<any>(BACKEND_URL + 
      'createChatRoom/'+_id + '/' + '630a2e5d4989aae851a657e4' )
  }
  
  getChatRoomData(_id){
    return this.http.get<any>(BACKEND_URL + 
      'getChatRoomData/'+_id + '/' + '630a2e5d4989aae851a657e4' )
  }

}
