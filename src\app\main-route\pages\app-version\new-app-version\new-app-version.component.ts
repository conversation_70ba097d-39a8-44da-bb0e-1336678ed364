import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { AppVersionService } from 'src/app/shared/services/app-version.service';
import { AuthService } from 'src/app/shared/services/auth.service';

@Component({
  selector: 'app-new-app-version',
  templateUrl: './new-app-version.component.html',
  styleUrls: ['./new-app-version.component.css']
})
export class NewAppVersionComponent implements OnInit {

  lang = 'en'
  params_lang = ''

  form = new UntypedFormGroup({});

  loading = false
  user: User;

  constructor(
    private toastrService: ToastrService,
    private appVersionService: AppVersionService,
    private authService: AuthService,
    private formBuilder: UntypedFormBuilder,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.authService.user.pipe().subscribe( async appUser => {
      this.user = appUser
    })
    this.form = this.formBuilder.group({
      appVersion: new UntypedFormControl( null),
    });
  }

  submit(){
    // this.signUpForm.value.name
    console.log("appVersion: ",this.form.value.appVersion )
    let data = {
      current: this.form.value.appVersion,
      enabled: false,
      msg: {
        title: 'App maintenance',
        msg: 'We are currently improving your app experince.Please try again later..',
        btn: 'Ok',
      },
      majorMsg: {
        title: 'Important App update',
        msg: 'Please update your app to the latest version to continue using it.',
        btn: 'Download',
      },
      minorMsg: {
        title: 'App update available',
        msg: 'There is a new version available, would you like to get it now?',
        btn: 'Download',
      },
      user: this.user._id
    }
    this.appVersionService.create(   
      data
    ).subscribe( respond  => {
            if( respond.status = "success"){
  
              this.router.navigate(["/" + this.lang + "/list-of-app-versions"]).then(() => {});
              this.toastrService.success( " Successfully created new" );
              this.form.reset();
            }
        },
        respond_error => {
          let error_message = respond_error.error.message;
          this.toastrService.error(error_message);
        }

      ); 
  }

}
