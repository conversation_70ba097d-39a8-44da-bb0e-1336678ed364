import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { CanLoad, Route, Router, UrlSegment } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthStateGuard implements CanLoad {


  constructor(
    private auth: AuthService,
    private router: Router
   ) {

    }

   canLoad( route: Route,
    segments: UrlSegment[],
   ): Observable<boolean> | Promise<boolean> | boolean{


    this.auth.user.pipe().subscribe(appUser => {

      if( appUser != null && appUser.emailVerified == false ){
        this.router.navigateByUrl('/verify-email');
        return false
      }

      }
    )

    return true
  }

}
