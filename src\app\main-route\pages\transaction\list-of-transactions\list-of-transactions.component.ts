import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Component, OnInit, TemplateRef } from '@angular/core';
import { UntypedFormBuilder } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ngxLoadingAnimationTypes } from 'ngx-loading';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { LoadJsService } from 'src/app/shared/services/load-js.service';
import { MainDataService } from 'src/app/shared/services/main-data.service';
import { PromotionService } from 'src/app/shared/services/promotion.service';
import { TransactionService } from 'src/app/shared/services/transaction.service';
import { AuthService } from 'src/app/shared/services/auth.service';

@Component({
  selector: 'app-list-of-transactions',
  templateUrl: './list-of-transactions.component.html',
  styleUrls: ['./list-of-transactions.component.css'],
})
export class ListOfTransactionsComponent implements OnInit {
  lang = 'en';
  searchForm;

  loading = false;

  data: any[] = [];

  public ngxLoadingAnimationTypes = ngxLoadingAnimationTypes;
  public loadingTemplate: TemplateRef<any>;

  startLoading = false;
  public pageNo: number = 1;
  public pageNoTotal: number = 1;
  count: number = 0;
  resultsPerPage: number = 7;

  selectedTransaction = null

  timeout: any = null;
  resultSearch: any = null;
  searchText = '';
  me: User;

  constructor(
    private authService: AuthService,
    private transactionService: TransactionService,
    private formBuilder: UntypedFormBuilder,
    private toastrService: ToastrService,
    public loadJsService: LoadJsService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private modalService: NgbModal
  ) {
    this.searchForm = this.formBuilder.group({
      search: '',
    });
    this.authService.user.pipe().subscribe((appUser) => {
      this.me = appUser;
    });
    this.loadJsService.loadScripts();
  }

  ngOnInit(): void {
    this.loading = true;
    this.startLoading = true;
    this.activatedRoute.params.subscribe(async (paramMap) => {
      if (paramMap['lang']) {
        this.lang = paramMap['lang'];
      }
    });

    this.activatedRoute.queryParams.subscribe((params) => {
      this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;
    });

    this.getAll();
  }

  public setPage(page: number) {
    this.data = [];
    this.startLoading = true;
    this.router
      .navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: page },
        queryParamsHandling: 'merge', // preserve the existing query params in the route
        skipLocationChange: false, // do trigger navigation
      })
      .finally(() => {
        if(this.searchText === ''){
          this.getAll();
        }else{
          this.getAllResultSearch()
        }
        // this.viewScroller.setOffset([120, 120]);
        // this.viewScroller.scrollToAnchor('deals'); // Anchore Link
      });
  }

  getAll() {
    this.transactionService.getAll(this.pageNo, this.resultsPerPage).subscribe(
      async (result) => {
        if (result.status == 'success') {
          // this.users = await result.data.data.filter( user  =>{
          //   return  user._id !== this.user._id
          // });
          this.count = result.data.count;
          console.log('result.data.data: ', result.data);
          this.data = result.data.data;

          this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
          this.loading = false;
          this.startLoading = false;
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }

  ngOnDestroy() {
    this.loadJsService.removeScripts();
  }
  redirectTo(uri: string) {
    this.router.navigateByUrl(this.lang + uri).then(() => {
      this.loadJsService.removeScripts();
    });
  }

  openOptionsModal(optionsContent) {
    this.modalService.open(optionsContent, { centered: true });
  }


  selectTransaction(transaction){
    this.selectedTransaction = transaction
  }

  resultsPerPageChanged(event){
    this.loading = true;
    this.startLoading = true;
    this.resultsPerPage = Number(event)
    this.count =  1
    this.pageNoTotal = 1
    this.data = []
    this.setPage(1)
  }


  searchUser(searchText) {
    clearTimeout(this.timeout);
    var $this = this;
    this.timeout = setTimeout(function () {
      if (searchText.keyCode != 13) {
        $this.startLoading = true
        $this.loading = true
        $this.searchSocket(searchText.target.value);
      }else{
        $this.startLoading = true
        $this.loading = true
        $this.getAll()
      }
    }, 1000);
  } 
  public searchSocket(searchText) {
    this.searchText = searchText.toString();
    if(this.searchText == ''){
      this.startLoading = true
      this.loading = true
      this.setPage(1)
      return
    }
    this.data = []
    this.setPage(1)
  }
  getAllResultSearch() {
    if(this.searchText == '') return
    this.loading = true;
    this.startLoading = true;
    this.count =  1
    this.pageNoTotal = 1
    this.data = []
    this.transactionService.searchTransaction(this.searchText, this.me._id,this.pageNo,this.resultsPerPage).subscribe(
      (result) => {
        if (result.status == 'success') {
          console.log("getAllResultSearch: ",result.data)

          this.count = result.data.count;
          this.data = result.data.data;
          this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
          this.loading = false;
          this.startLoading = false;
        }
      },
      (respond_error) => {
        this.loading = false;
        this.startLoading = false;
        // this.toastrService.error(
        //   respond_error?.error.message,
        //   respond_error?.name
        // );
      }
    );
  }

}
