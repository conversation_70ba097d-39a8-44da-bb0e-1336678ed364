<a
  (click)="redirectTo('/home')"
  class="app-sidebar-link"
  [class.active]="routeIsActiveUrl == '/' + lang + '/home'"
>
  <img
    width="25"
    height="25"
    [src]="
      routeIsActiveUrl == '/' + lang + '/home'
        ? 'assets/iconsTab/raport.svg'
        : 'assets/iconsTab/raportSel.svg'
    "
  />
</a>
<a
  (click)="redirectTo('/users')"
  class="app-sidebar-link"
  [class.active]="routeIsActiveUrl == '/' + lang + '/users'"
>
  <img
    width="25"
    height="25"
    [src]="
      routeIsActiveUrl == '/' + lang + '/users'
        ? '/assets/iconsTab/userListSel.svg'
        : '/assets/iconsTab/userList.svg'
    "
  />
</a>
<a
  (click)="redirectTo('/list-promotions')"
  class="app-sidebar-link"
  [class.active]="routeIsActiveUrl == '/' + lang + '/list-promotions'"
>
  <img
    width="25"
    height="25"
    [src]="
      routeIsActiveUrl == '/' + lang + '/list-promotions'
        ? '/assets/iconsTab/send-promotion.svg'
        : '/assets/iconsTab/send-promotion-sel.svg'
    "
  />
</a>

<a
  (click)="redirectTo('/list-gifts')"
  class="app-sidebar-link"
  [class.active]="routeIsActiveUrl == '/' + lang + '/list-gifts'"
>
  <img
    width="25"
    height="25"
    [src]="
      routeIsActiveUrl == '/' + lang + '/list-gifts'
        ? '/assets/iconsTab/giftListSel.svg'
        : '/assets/iconsTab/giftList.svg'
    "
  />
</a>
<a
  (click)="redirectTo('/suggested')"
  class="app-sidebar-link"
  [class.active]="routeIsActiveUrl == '/' + lang + '/suggested'"
>
  <img
    width="25"
    height="25"
    [src]="
      routeIsActiveUrl == '/' + lang + '/suggested'
        ? '/assets/iconsTab/suggested.svg'
        : '/assets/iconsTab/suggestedSel.svg'
    "
  />
</a>
<!-- list-notifications -->
<a
  (click)="redirectTo('/list-notifications')"
  class="app-sidebar-link"
  [class.active]="routeIsActiveUrl == '/' + lang + '/list-notifications'"
>
  <img
    width="25"
    height="25"
    [src]="
      routeIsActiveUrl == '/' + lang + '/list-notifications'
        ? '/assets/iconsTab/send-notification.svg'
        : '/assets/iconsTab/send-notification-sel.svg'
    "
  />
</a>

<a
  (click)="redirectTo('/chat')"
  class="app-sidebar-link"
  [class.active]="routeIsActiveUrl == '/' + lang + '/chat'"
>
  <img
    width="25"
    height="25"
    [src]="
      routeIsActiveUrl == '/' + lang + '/chat'
        ? '/assets/iconsTab/chat.svg'
        : '/assets/iconsTab/chatSel.svg'
    "
  />
</a>
<a
  (click)="redirectTo('/list-of-central-bank-transactions')"
  class="app-sidebar-link"
  [class.active]="
    routeIsActiveUrl == '/' + lang + '/list-of-central-bank-transactions'
  "
>
  <img
    width="25"
    height="25"
    [src]="
      routeIsActiveUrl == '/' + lang + '/list-of-central-bank-transactions'
        ? 'assets/iconsTab/centralBank.svg'
        : 'assets/iconsTab/centralBankSel.svg'
    "
  />
</a>

<!-- <a 
(click)="redirectTo('/list-of-exchange')" 
class="app-sidebar-link"
[class.active]="routeIsActive('/list-of-exchange')">
<img
width="25"
height="25"
[src]=" routeIsActive('/list-of-exchange') ?  '/assets/iconsTab/exchange.svg' : 'assets/iconsTab/exchangeSel.svg' "
/>
</a> -->

<a
  (click)="redirectTo('/list-of-online-payment')"
  class="app-sidebar-link"
  [class.active]="routeIsActiveUrl == '/' + lang + '/list-of-online-payment'"
>
  <img
    width="25"
    height="25"
    [src]="
      routeIsActiveUrl == '/' + lang + '/list-of-online-payment'
        ? '/assets/iconsTab/onlinePaymentSel-1.svg'
        : '/assets/iconsTab/onlinePaymentSel.svg'
    "
  />
</a>

<a
  (click)="redirectTo('/list-of-transcations')"
  class="app-sidebar-link"
  [class.active]="routeIsActiveUrl == '/' + lang + '/list-of-transcations'"
>
  <img
    width="25"
    height="25"
    [src]="
      routeIsActiveUrl == '/' + lang + '/list-of-transcations'
        ? '/assets/iconsTab/transaction.svg'
        : 'assets/iconsTab/transactionSel.svg'
    "
  />
</a>

<a
  (click)="redirectTo('/list-of-transcations-of-ministry-of-finance')"
  class="app-sidebar-link"
  [class.active]="
    routeIsActiveUrl == '/en/list-of-transcations-of-ministry-of-finance'
  "
>
  <img
    width="25"
    height="25"
    [src]="
      routeIsActiveUrl == '/en/list-of-transcations-of-ministry-of-finance'
        ? '/assets/iconsTab/minFinance.svg'
        : '/assets/iconsTab/minFinanceSel.svg'
    "
  />
</a>

<a
  (click)="redirectTo('/list-of-transcations-of-atm')"
  class="app-sidebar-link"
  [class.active]="
    routeIsActiveUrl == '/' + lang + '/list-of-transcations-of-atm'
  "
>
  <img
    width="25"
    height="25"
    [src]="
      routeIsActiveUrl == '/' + lang + '/list-of-transcations-of-atm'
        ? '/assets/iconsTab/atm.svg'
        : '/assets/iconsTab/atmSel.svg'
    "
  />
</a>

<a
  (click)="redirectTo('/settings')"
  class="app-sidebar-link"
  [class.active]="routeIsActiveUrl == '/' + lang + '/settings'"
>
  <img
    width="25"
    height="25"
    [src]="
      routeIsActiveUrl == '/' + lang + '/settings'
        ? '/assets/iconsTab/settings.svg'
        : '/assets/iconsTab/settingsSel.svg'
    "
  />
</a>

<!-- <div class="main-header-left d-none d-lg-block">
<div class="logo-wrapper">
    <a>
        <img 
        class="d-none d-lg-block blur-up lazyloaded"
            src="assets/fiber-al-logo.png" alt="">
    </a>
</div>
</div>
<div class="sidebar custom-scrollbar">
<div class="sidebar-user" *ngIf="userIsAuthenticated">
    <img *ngIf="!user?.photoProfile?.data" class="img-60" src="assets/user2.jpg" alt="#">
    <app-img
    *ngIf="user?.photoProfile?.data"
        [(buffer)]="user.photoProfile.data"
        [class]="'align-self-center pull-right img-50 blur-up lazyloaded'"
    ></app-img>
    <div>
        <h6 class="f-14">{{user.name}} {{user.surname}}</h6>
        <p>{{user.role}}.</p>
    </div>
</div>
<ul class="sidebar-menu">
    
    <li>
        <a class="sidebar-header"
        (click)="redirectTo('/home')"
        [ngClass]="routeIsActive('/home') ? 'active' : '' ">
            <i data-feather="home"></i>
            <span> Home </span>
        </a>
    </li>

    
    <li>
        <a class="sidebar-header"
        (click)="redirectTo('/list-of-transcations')"
        [ngClass]="routeIsActive('/list-of-transcations') ? 'active' : '' ">
            <i data-feather="list"></i>
            <span> Transactions </span>
        </a>
    </li>
    <li>
        <a class="sidebar-header"
        (click)="redirectTo('/the-minister-of-finance')"
        [ngClass]="routeIsActive('/the-minister-of-finance') ? 'active' : '' ">
            <i data-feather="dollar-sign"></i>
            <span> The Minister of Finance </span>
        </a>
    </li>
    <li>
        <a class="sidebar-header"
        (click)="redirectTo('/online-payment')"
        [ngClass]="routeIsActive('/online-payment') ? 'active' : '' ">
            <i data-feather="credit-card"></i>
            <span> Online Payment </span>
        </a>
    </li>
    <li>
        <a class="sidebar-header"
        (click)="redirectTo('/atm')"
        [ngClass]="routeIsActive('/atm') ? 'active' : '' ">
            <i data-feather="upload"></i>
            <span> ATM (convert credits) </span>
        </a>
    </li>
    
    <li [class]="liSelected  === 'promotions' ? 'active' : '' "> 
        <a   
        class="sidebar-header" 
        [class]="liSelected  === 'promotions'? 'active' : '' "
        (click)="getElementById('promotions')">
            <i data-feather="airplay"></i>
            <span >  Promotions </span>
            <i class="fa fa-angle-right pull-right"></i>
        </a>

        <ul class="sidebar-submenu ">
            
            <li 
            [class]="routeIsActive('/new-promotion')? 'active' : '' ">
                <a 
                (click)="redirectTo('/new-promotion')">
                    <i class="fa fa-circle"></i>
                    <span> New Promotion </span>
                </a>
            </li>

            <li 
            [class]="routeIsActive('/list-of-promotions')? 'active' : '' ">
                <a 
                (click)="redirectTo('/list-of-promotions')">
                    <i class="fa fa-circle"></i>
                    <span>  List of promotions </span>
                </a>
            </li>
        </ul>
    </li>
    <li [class]="liSelected  === 'podatoci' ? 'active' : '' "> 
        <a   
        class="sidebar-header" 
        [class]="liSelected  === 'podatoci'? 'active' : '' "
        (click)="getElementById('podatoci')">
            <i data-feather="settings"></i>
            <span >  Data </span>
            <i class="fa fa-angle-right pull-right"></i>
        </a>

        <ul class="sidebar-submenu ">
            
            <li 
            [class]="subLiSelected  === 'main-data'? 'active' : '' ">
                
                <a (click)="selectSub('main-data')">
                    <i class="fa fa-circle"></i>
                    <span> Main Data </span>
                    <i class="fa fa-angle-right pull-right"></i>
                </a>

                <ul class="sidebar-submenu">
                    <li [class]="routeIsActive('/new-main-data')? 'active' : '' ">
                        <a 
                        (click)="redirectTo('/new-main-data')" >
                            <i class="fa fa-circle"></i> Add new </a>
                    </li>

                    <li [class]="routeIsActive('/list-main-data')? 'active' : '' ">
                        <a 
                        (click)="redirectTo('/list-main-data')">
                            <i class="fa fa-circle"></i> List of data </a>
                    </li>
                </ul>
            </li>

            <li 
            [class]="subLiSelected  === 'central-bank'? 'active' : '' ">
                
                <a (click)="selectSub('central-bank')">
                    <i class="fa fa-circle"></i>
                    <span> Central Bank </span>
                    <i class="fa fa-angle-right pull-right"></i>
                </a>

                <ul class="sidebar-submenu">
                    <li [class]="routeIsActive('/add-new-bank-transaction')? 'active' : '' ">
                        <a 
                        (click)="redirectTo('/add-new-bank-transaction')" >
                            <i class="fa fa-circle"></i> Add new Central Bank Transaction </a>
                    </li>

                    <li [class]="routeIsActive('/list-of-central-bank-transactions')? 'active' : '' ">
                        <a 
                        (click)="redirectTo('/list-of-central-bank-transactions')">
                            <i class="fa fa-circle"></i> List of Central Bank Transactions </a>
                    </li>
                </ul>
            </li>

            <li 
            [class]="subLiSelected  === 'fee'? 'active' : '' ">
                
                <a (click)="selectSub('fee')">
                    <i class="fa fa-circle"></i>
                    <span> Fees </span>
                    <i class="fa fa-angle-right pull-right"></i>
                </a>

                <ul class="sidebar-submenu">
                    <li [class]="routeIsActive('/add-new-fee')? 'active' : '' ">
                        <a 
                        (click)="redirectTo('/add-new-fee')" >
                            <i class="fa fa-circle"></i> Add new Fee</a>
                    </li>

                    <li [class]="routeIsActive('/list-of-fees')? 'active' : '' ">
                        <a 
                        (click)="redirectTo('/list-of-fees')">
                            <i class="fa fa-circle"></i> List of Fees</a>
                    </li>
                </ul>
            </li>

        </ul>
    </li>
    




</ul>
</div> -->
