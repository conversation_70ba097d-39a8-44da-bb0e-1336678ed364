<div style="padding: 32px !important" class="projects-section">
  <div
    style="
      background-color: var(--app-container);
      flex: 1;
      padding: 25px 25px 60px;
      border-radius: 32px;
      gap: 1em;
      display: flex;
      flex-direction: column;
      overflow: auto;
      position: relative;
    "
  >
    <div
      style="
        display: flex;
        flex-direction: column;
        position: sticky;
        top: 0;
        margin-right: 10px;
      "
    >
      <div style="display: flex; flex-direction: column; border-radius: 10px">
        <div style="display: flex">
          <div style="display: flex; flex: 1 0 20%"></div>
          <div
            style="
              display: flex;
              flex: 1 0 75.6%;
              border-radius: 10px 10px 10px 10px;
              /* border-bottom: 3px solid var(--app-bg); */
              background-color: var(--app-bg);
              border-right-width: 0;
            ">
            <div
              *ngFor="let role of rolesTitle"
              style="
                display: flex;
                flex: 1;
                align-items: center;
                justify-content: center;
                position: relative;
                gap: 2em;
              "
            >
              <div style="display: flex; position: relative;background-color: #1c2836 ;padding: 0 1em;border-radius: 5px;">
                <span>{{role?.title}}</span>
                <img
                  (click)="openDelRoleModal(delRoleContent)"
                  class="cancelButton"
                  style="
                    height: 10px;
                    width: 10px;
                    position: absolute;
                    top: 0;
                    right: -3px;
                  "
                  src="assets/icons/close.svg"
                  alt=""
                />
              </div>
            </div>
          </div>
          <div
            style="
              display: flex;
              flex: 1 0 5%;
              align-items: center;
              justify-content: center;
              border-radius: 0 10px 10px 0;
              cursor: pointer;
            "
          >
            <div (click)="openAddRoleModal(addRoleContent)" class="plusButton">
              +
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      style="overflow: auto; display: flex; flex-direction: column; gap: 1em"
    >
      <div
        *ngFor="let category of categories; let i = index"
        [ngClass]="i === 0 ? '' : 'topHeaderCell' "
        style="
        display: flex; 
        flex-direction: column"
      >
        <span
          style="
            padding: 5px 1em;
            background-color: var(--app-bg);
            width: 95%;
            border-radius: 10px 10px 10px 10px;"
          >{{category?.title}}</span
        >
        <div
          style="
            display: flex;
            flex-direction: column;
            border-radius: 10px;
            gap: 1px;
          ">
          <div *ngFor="let permission of category.permissions" style="display: flex">
            <div
              style="
                display: flex;
                flex: 1 0 20%;
                border-bottom: 3px solid var(--app-bg);
                border-right-width: 0;
                border-radius: 10px 0 0 10px;
                overflow: hidden;
                padding: 5px 1em;
                border-right-width: 0;
                line-height: 2;
                justify-content: center;
              ">
              {{permission?.title}}
            </div>
            <div
              style="
                border-bottom: 3px solid var(--app-bg);
                border-right-width: 0;
                display: flex;
                flex: 1 0 75%;
                border-radius: 0 10px 10px 0;
                overflow: hidden;
              "
            >
              <div
                *ngFor="let role of rolesTitle"
                style="
                  border-bottom: 3px solid var(--app-bg);
                  display: flex;
                  flex: 1;
                  padding: 5px 1em;
                  border-top-width: 0;
                  border-bottom-width: 0;
                  justify-content: center;
                  align-items: center;
                "
              >
                <div class="disp-flex j-c-center">
                  <label class="control control--checkbox">
                    <input type="checkbox" checked="checked" />
                    <div class="control__indicator"></div>
                  </label>
                </div>
              </div>
            </div>
            <div style="display: flex; flex: 1 0 5%"></div>
          </div>
          
          <div style="
          margin-top: 30px !important;
          display: flex">
            <div
              (click)="openAddPermissionModal(addPermissionContent,category)"
              class="addCategory"
            >
              Add
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      style="
        display: flex;
        position: absolute;
        bottom: 0;
        height: 60px;
        align-items: center;
      "
    >
      <div
        (click)="openAddCategoryModal(addCategoryContent)"
        class="addEntireCategory"
      >
        <span>Add Category</span>
        <div
          style="
            background-color: var(--app-bg);
            height: 35px;
            width: 35px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 20px;
            cursor: pointer;
          "
        >
          +
        </div>
      </div>
    </div>
  </div>

  <ng-template #delRoleContent let-modal>
    <div
      style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1"
    >
      <div class="modal-body">
        <!-- header -->
        <div
          style="
            display: flex;
            justify-content: center;
            width: 100%;
            position: relative;
          "
        >
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; position: absolute; right: 0"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->

        <div style="margin: 1em 0">
          <div
            style="
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
              gap: 2rem;
            "
          >
            <span style="color: white; font-weight: 600; font-size: 17px"
              >Are you sure ou want to delete role</span
            >
            <span style="color: white; font-weight: 800; font-size: 20px"
              >Basic</span
            >

            <div
              style="
                width: 50%;
                display: flex;
                justify-content: space-between;
                gap: 1em;
              "
            >
              <div
                (click)="modal.dismiss('Cross click')"
                class="modalButtonsCancel"
              >
                Cancel
              </div>

              <div class="modalButtonsDelete">Delete</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
  <ng-template #addPermissionContent let-modal>
    <div
      style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1"
    >
      <div class="modal-body">
        <!-- header -->
        <div
          style="
            display: flex;
            justify-content: center;
            width: 100%;
            position: relative;
          "
        >
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; position: absolute; right: 0"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->

        <div style="margin: 1em 0">
          <div
            style="
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
              gap: 2rem;
            "
          >
            <div
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                gap: 1em;
              "
            >
              <span style="color: white; font-weight: 600; font-size: 17px"
                >Create New</span
              >
              <span style="color: white; font-weight: 500; font-size: 14px">
                Permission in {{selectPermissionCategory?.title | uppercase }}
              </span>
            </div>

            <div class="creditsInputWrapper">
              <input
                [(ngModel)]="permissionTitle"
                class="creditsInput"
                type="text"
                placeholder="Permission name"
              />
            </div>

            <div
              style="
                width: 50%;
                display: flex;
                justify-content: space-between;
                gap: 1em;
              "
            >
              <div
                (click)="modal.dismiss('Cross click')"
                class="modalButtonsCancel"
              >
                Cancel
              </div>

              <div 
              (click)="createPermission()"
              class="modalButtonsDelete">Add</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
  <ng-template #addRoleContent let-modal>
    <div
      style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1"
    >
      <div class="modal-body">
        <!-- header -->
        <div
          style="
            display: flex;
            justify-content: center;
            width: 100%;
            position: relative;
          "
        >
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; position: absolute; right: 0"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->

        <div style="margin: 1em 0">
          <div
            style="
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
              gap: 2rem;
            "
          >
            <div
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
              "
            >
              <span style="color: white; font-weight: 600; font-size: 17px"
                >Create New Role</span
              >
            </div>

            <div class="creditsInputWrapper">
              <input
                [(ngModel)]="roleTitle"
                class="creditsInput"
                type="text"
                placeholder="Role name"
              />
            </div>

            <div
              style="
                width: 50%;
                display: flex;
                justify-content: space-between;
                gap: 1em;
              "
            >
              <div
                (click)="modal.dismiss('Cross click')"
                class="modalButtonsCancel"
              >
                Cancel
              </div>

              <div 
              (click)="createRoleTitle()"
              class="modalButtonsDelete">Create</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
  <ng-template #addCategoryContent let-modal>
    <div
      style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1"
    >
      <div class="modal-body">
        <!-- header -->
        <div
          style="
            display: flex;
            justify-content: center;
            width: 100%;
            position: relative;
          "
        >
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; position: absolute; right: 0"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->

        <div style="margin: 1em 0">
          <div
            style="
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
              gap: 2rem;
            "
          >
            <div
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
              "
            >
              <span style="color: white; font-weight: 600; font-size: 17px"
                >Create New Category</span
              >
            </div>

            <div class="creditsInputWrapper">
              <input
                [(ngModel)]="permissionCategoryTitle"
                class="creditsInput"
                type="text"
                placeholder="Category name"
              />
            </div>

            <div
              style="
                width: 50%;
                display: flex;
                justify-content: space-between;
                gap: 1em;
              "
            >
              <div
                (click)="
                modal.dismiss('Cross click')
                "
                class="modalButtonsCancel"
              >
                Cancel
              </div>

              <div 
              (click)="createPermissionCategory()"
              class="modalButtonsDelete">Create</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
</div>
