import { Component } from '@angular/core';
import {
  Ngb<PERSON><PERSON>,
  Ngb<PERSON><PERSON><PERSON><PERSON>,
  NgbDate<PERSON>arserFormatter,
  NgbDatepickerModule,
} from '@ng-bootstrap/ng-bootstrap';
import { FormsModule } from '@angular/forms';
import { JsonPipe } from '@angular/common';

@Component({
  selector: 'ngbd-datepicker-range-popup',
  standalone: true,
  imports: [NgbDatepickerModule, FormsModule, JsonPipe],
  templateUrl: './datepicker-range-popup.html',
  styleUrls: ['./datepicker-range-popup.css'],
  // styles: [
  //   `
  //     .dp-hidden {
  //       width: 0;
  //       margin: 0;
  //       border: none;
  //       padding: 0;
  //     }
  //     .custom-day {
  //       text-align: center;
  //       padding: 0.185rem 0.25rem;
  //       display: inline-block;
  //       height: 2rem;
  //       width: 2rem;
  //       color: black;
  //     }
  //     .custom-day.focused {
  //       background-color: var(--app-bg);
  //     }
  //     .custom-day.range,
  //     .custom-day:hover {
  //       background-color: rgb(2, 117, 216);
  //       color: black;
  //     }
  //     .custom-day.faded {
  //       background-color: rgba(100, 117, 216, 0.5);
  //     }
  //   `,
  // ],
})
export class NgbdDatepickerRangePopup {
  hoveredDate: NgbDate | null = null;

  fromDate: NgbDate | null;
  toDate: NgbDate | null;

  constructor(
    private calendar: NgbCalendar,
    public formatter: NgbDateParserFormatter
  ) {
    this.fromDate = calendar.getToday();
    this.toDate = calendar.getNext(calendar.getToday(), 'd', 10);
  }

  onDateSelection(date: NgbDate) {
    if (!this.fromDate && !this.toDate) {
      this.fromDate = date;
    } else if (
      this.fromDate &&
      !this.toDate &&
      date &&
      date.after(this.fromDate)
    ) {
      this.toDate = date;
    } else {
      this.toDate = null;
      this.fromDate = date;
    }
  }

  isHovered(date: NgbDate) {
    return (
      this.fromDate &&
      !this.toDate &&
      this.hoveredDate &&
      date.after(this.fromDate) &&
      date.before(this.hoveredDate)
    );
  }

  isInside(date: NgbDate) {
    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);
  }

  isRange(date: NgbDate) {
    return (
      date.equals(this.fromDate) ||
      (this.toDate && date.equals(this.toDate)) ||
      this.isInside(date) ||
      this.isHovered(date)
    );
  }

  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {
    const parsed = this.formatter.parse(input);
    return parsed && this.calendar.isValid(NgbDate.from(parsed))
      ? NgbDate.from(parsed)
      : currentValue;
  }
}
