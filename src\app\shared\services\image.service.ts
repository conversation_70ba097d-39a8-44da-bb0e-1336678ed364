import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { User } from '../models/user';
import { AuthService } from './auth.service';
import { BehaviorSubject } from 'rxjs';
const BACKEND_URL = environment.imageUrl + "/post/";

@Injectable({
  providedIn: 'root'
})
export class ImageService {

  me: User;
  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) { 
    this.authService.user.pipe().subscribe(appUser => {
      this.me = appUser
    })
  }


  uploadPostImages(
    userId,
    postId,
    isPayPerView,
    imagesFiles:any[]
  ){
    let postData = new FormData();
    
    postData.append('postId', JSON.stringify(postId));
    postData.append('userId', JSON.stringify(userId));
    postData.append('isPayPerView', JSON.stringify(isPayPerView));
    for (var i = 0; i < imagesFiles.length; i++) { 
      console.log("imagesFiles[i]): ",imagesFiles[i])
      postData.append("file[]", imagesFiles[i]);
    }
    return  this.http.post<any>( BACKEND_URL + 'uploadPostImages' , postData)
  }

  uploadImage(
    imagesFiles:any[]
  ){
    let postData = new FormData();
    for (var i = 0; i < imagesFiles.length; i++) { 
      postData.append("file[]", imagesFiles[i]);
    }
    return  this.http.post<any>( BACKEND_URL + 'uploadImage' , postData)
  }
  uploadOriginalImage(
    imagesFiles:any[]
  ){
    console.log("imagesFiles: ",imagesFiles)

    let postData = new FormData();
    for (var i = 0; i < imagesFiles.length; i++) { 
      postData.append("file[]", imagesFiles[i]);
    }
    return  this.http.post<any>( BACKEND_URL + 'uploadOriginalImage' , postData)
  }

  uploadGifFile(
    imagesFiles:any[]
  ){
    let postData = new FormData();
    for (var i = 0; i < imagesFiles.length; i++) { 
      postData.append("file[]", imagesFiles[i]);
    }
    return  this.http.post<any>( BACKEND_URL + 'uploadGifFile' , postData)
  }

  getProfileImage(userId){
    let postData = {
      meId: this.me?._id,
      userId: userId
    }
    return  this.http.post<any>( BACKEND_URL + 'getProfileImage', postData)
  }

  getPhotoCoverImage(userId){
    let postData = {
      meId: this.me._id,
      userId: userId
    }
    return  this.http.post<any>( BACKEND_URL + 'getPhotoCoverImage', postData)
  }

  getPostImages(postId,userId,postUserId){
    let postData = {
      postUserId: postUserId,
      postId: postId,
      userId: userId
    }
    return  this.http.post<any>( BACKEND_URL + 'getPostImages', postData)
  }

  getPostImage(postId){
    let postData = {
      postId: postId
    }
    return  this.http.post<any>( BACKEND_URL + 'getPostImage', postData)
  }

  getPromotionImage(postId,userId,postUserId){
    let postData = {
      postUserId: postUserId,
      postId: postId,
      userId: userId
    }
    return  this.http.post<any>( BACKEND_URL + 'getPromotionImage', postData)
  }

  getImageByPromotionId(promotionId,userId){
    let postData = {
      promotionId: promotionId,
      userId: userId
    }
    return  this.http.post<any>( BACKEND_URL + 'getImageByPromotionId', postData)
  }


  getVideoFirstImage(postId,userId,postUserId){
    let postData = {
      postUserId: postUserId,
      postId: postId,
      userId: userId
    }
    return  this.http.post<any>( BACKEND_URL + 'getVideoFirstImage', postData)
  }

  getVideoFirstBlurImage(postId,userId,postUserId){
    let postData = {
      postUserId: postUserId,
      postId: postId,
      userId: userId
    }
    return  this.http.post<any>( BACKEND_URL + 'getVideoFirstBlurImage', postData)
  }


  getPostBlurImages(postId,userId,postUserId){
    let postData = {
      postUserId: postUserId,
      postId: postId,
      userId: userId
    }
    return  this.http.post<any>( BACKEND_URL + 'getPostBlurImages', postData)
  }

}
