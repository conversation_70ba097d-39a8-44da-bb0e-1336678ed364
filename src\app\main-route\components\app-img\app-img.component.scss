.profileImgModalTop {
  height: 60px !important;
  width: 60px;
  border-radius: 20px;
  position: relative;
}

.postImg{
  object-fit: cover;
  height: 50px;
  width: 50px; 
  border-radius: 5px
}
.postBloggerImg{
  object-fit: cover;
  height: 40px; 
  width: 40px; 
  border-radius: 5px
}
.userProfileTransaction{
  height: 50px; 
  width: 50px; 
  border-radius: 15px
}

.promImgS{
  width: 100%; 
  object-fit: contain;
  border-radius: 20px;
}

.atmAvatar{
  min-width: 50px; 
  max-width: 55px; 
  border-radius: 14px
}
.notification-sent-content-image-preview {
  height: 30px;
  width: 30px;
  border-radius: 10px;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
