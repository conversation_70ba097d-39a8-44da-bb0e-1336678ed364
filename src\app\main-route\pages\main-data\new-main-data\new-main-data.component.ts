import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { AuthService } from 'src/app/shared/services/auth.service';
import { MainDataService } from 'src/app/shared/services/main-data.service';

@Component({
  selector: 'app-new-main-data',
  templateUrl: './new-main-data.component.html',
  styleUrls: ['./new-main-data.component.css']
})
export class NewMainDataComponent implements OnInit {

  lang = 'en'
  params_lang = ''

  form = new UntypedFormGroup({});

  loading = false
  user: User;

  constructor(
    private toastrService: ToastrService,
    private mainDataService: MainDataService,
    private authService: AuthService,
    private formBuilder: UntypedFormBuilder,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.authService.user.pipe().subscribe( async appUser => {
      this.user = appUser
    })
    this.form = this.formBuilder.group({
      variableName: new UntypedFormControl( null),
    });
  }

  submit(){
    // this.signUpForm.value.name
    console.log("appVersion: ",this.form.value.variableName )

    this.mainDataService.create(   
      {
        variableName : this.form.value.variableName,
        createdBy : this.user._id
      }
    ).subscribe( respond  => {
            if( respond.status = "success"){
  
              this.router.navigate(["/" + this.lang + "/list-main-data"]).then(() => {});
              this.toastrService.success( " Successfully created new" );
              this.form.reset();
            }
        },
        respond_error => {
          let error_message = respond_error.error.message;
          this.toastrService.error(error_message);
        }

      ); 
  }


}
