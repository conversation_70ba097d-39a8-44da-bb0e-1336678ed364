import { Component, OnInit } from '@angular/core';
import { User } from 'src/app/shared/models/user';
import { AuthService } from 'src/app/shared/services/auth.service';
import { ChatRoomService } from 'src/app/shared/services/chat-room.service';
import { SocketService } from 'src/app/shared/services/socket.service';

// Define a type for a message object
interface Message {
  time: string;
  date: Date;
  sender: string;
  receiver: string;
  message: string;
  sent?: boolean;
}
@Component({
  selector: 'app-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.css'],
})
export class ChatComponent implements OnInit {

  delay = (ms) => new Promise((res) => setTimeout(res, ms));

  me: User;

  socket;
  
  rooms: any[] = [];
  searchMessageLoading = false;
  searchSideActivate = false;
  userChatId = null;
  userChat = null;


  throttle = 300;
  scrollDistance = 1;
  scrollUpDistance = 2;
  page = 1;

  moreLoading = false

  constructor(
    private chatRoomService: ChatRoomService,
    private socketService: SocketService,
    private authService: AuthService
  ) {
    this.socket = this.socketService.setupChatSocketConnection();
  }

  ngOnInit(): void {
    this.searchMessageLoading = true;
    this.authService.user.pipe().subscribe((appUser) => {
      this.me = appUser;
    });
    if (this.me?._id) {
      this.searchMessageLoading = true;
      this.getRooms();
    }
  }

  ngAfterViewInit(){

  }


  getRooms() {
    this.chatRoomService.chatRooms
      .subscribe(async (data) => {

        if (data !== undefined) {
          if (data?.updateManyOkId?.toString() === '630a2e5d4989aae851a657e4') {
            if (this.rooms.length) {
              if (data?.chatRoomIdDeleted?.toString()) {
                // this.rooms = this.rooms.filter((element) => {
                //   return (
                //     element.id.toString() !== data?.chatRoomIdDeleted?.toString()
                //   );
                // });
              }
            }
          } else {
            if (data?.userId === '630a2e5d4989aae851a657e4') {
              if (data.rooms.length > 0) {
                let roomsJoined = []
                if(data?.newRoomSocket){
                  this.rooms = this.rooms.filter((element) => {
                    return (
                      element.id.toString() !== data?.rooms[0]._id.toString()
                    );
                  });
                  roomsJoined = [...data?.rooms,...this.rooms];
                }else{
                  roomsJoined = [...this.rooms, ...data?.rooms];
                }
                const arrUniq = [...new Map(roomsJoined.map(v => [v.id, v])).values()]
                this.rooms = arrUniq
                // console.log("this.rooms: ",this.rooms)
                this.moreLoading =  false
                this.searchMessageLoading = false;
              }else{
                this.moreLoading =  false
                this.searchMessageLoading = false;
              }
            }
          }


        }
      });


  }

  getUnReadTotal(room) {
    if (room?.usersUnReadTotal) {
      for (let total of room?.usersUnReadTotal) {
        if (room?.anotherUser?._id.toString() !== total._userId.toString()) {
          return total.unReadTotal;
        }
      }
    }
  }
  getLastUnReadText(room) {
    if (room?.usersLastUnReadText) {
      for (let lastText of room?.usersLastUnReadText) {
        if (room?.anotherUser?._id.toString() !== lastText._userId.toString()) {
          return lastText.text;
        }
      }
    }
  }
  
  getLastTextDate(room) {
    if (room?.usersLastUnReadText) {
      for (let lastText of room?.usersLastUnReadText) {
        if (room?.anotherUser?._id.toString() !== lastText._userId.toString()) {
          return new Date(lastText.createdAt);
        }
      }
    }
  }

  async selectUserChatId(user){
    this.userChat = null
    this.userChatId = null
    await this.delay(50);
    this.userChat = user
    this.userChatId = user._id
  }

  loadData(event) {
    this.page = this.page + 1;
    this.socket.emit('getRooms', {
      userId: '630a2e5d4989aae851a657e4',
      page: this.page,
      resultsPerPage: 15
    });
    this.moreLoading =  true
    if (event !== null) {
      event.target.complete();
    }
  }

  onScrollDown(ev: any) {
    // console.log("scrolled down!!", ev);
    this.loadData(null)
  }
}
