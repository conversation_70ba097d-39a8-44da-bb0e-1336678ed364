import { AbstractControl } from '@angular/forms';
export class passwordValidator{
  translate =  {
    _Password_must_be_at_least_8_characters_long_: undefined,
    _The_password_must_have_a_number_: undefined,
    _The_password_must_have_lowercase_characters_: undefined,
    _The_password_must_have_upercase_characters_: undefined,
    _The_password_must_have_special_characters_: undefined,
  }
  constructor(public data) {
    this.translate = data
  }

  public passwordValidatorMethod = ( control: AbstractControl) => {
    if(this.translate){
      if (control && control.value !== null && control.value !== undefined) {
        const value = control.value as string;
        if (value.length < 8) {
            if(this.translate?._Password_must_be_at_least_8_characters_long_){
              return {
                invalidPassword: true,
                message: this.translate?._Password_must_be_at_least_8_characters_long_,
              };
            }else{
              return null;
            }
        }
    
        if (!/\d/.test(value)) {
              if(this.translate?._The_password_must_have_a_number_){
                return {
                  invalidPassword: true,
                  message: this.translate?._The_password_must_have_a_number_,
                };
              }else{
                return null;
              }
        }
    
        if (!/[a-z]/.test(value)) {
              if(this.translate?._The_password_must_have_lowercase_characters_){
                return {
                  invalidPassword: true,
                  message: this.translate?._The_password_must_have_lowercase_characters_,
                };
              }else{
                return null;
              }
        }
    
        if (!/[A-Z]/.test(value)) {
              if(this.translate?._The_password_must_have_upercase_characters_){
                return {
                  invalidPassword: true,
                  message: this.translate?._The_password_must_have_upercase_characters_,
                };
              }else{
                return null;
              }
        }
    
        if (!/\W/.test(value)) {
              if(this.translate?._The_password_must_have_special_characters_){
                return {
                  invalidPassword: true,
                  message: this.translate?._The_password_must_have_special_characters_,
                };
              }else{
                return null;
              }
        }
        }
    }

    return null;
  }
}

