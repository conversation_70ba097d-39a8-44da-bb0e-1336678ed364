import { Component, OnInit, TemplateRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { PromotionService } from 'src/app/shared/services/promotion.service';
import { ngxLoadingAnimationTypes } from 'ngx-loading';
import { LoadJsService } from 'src/app/shared/services/load-js.service';

@Component({
  selector: 'app-pormotion-details',
  templateUrl: './pormotion-details.component.html',
  styleUrls: ['./pormotion-details.component.css']
})
export class PormotionDetailsComponent implements OnInit {
  
  public ngxLoadingAnimationTypes = ngxLoadingAnimationTypes;
  public loadingTemplate: TemplateRef<any>;

  promotionId = ''
  lang = 'en';
  loading = false

  promotion = null

  promotionAvailableShared = []
  specificUsers = []
  nativeNotificationUsers = []

  constructor(
    public loadJsService: LoadJsService,
    private router: Router,
    private toastrService: ToastrService,
    private promotionService: PromotionService,
    private activatedRoute: ActivatedRoute
  ) { }

  ngOnInit(): void {
    this.loading = true
    this.activatedRoute.params.subscribe( async paramMap => {
      if(paramMap['lang']){
        this.lang = paramMap['lang'];
      }
      if(paramMap['promotionId']){
          this.promotionId = paramMap['promotionId']
          this.getPromotionData(this.promotionId)
      }
    })
  }

  getPromotionData(promotionId){
    this.promotionService.getPromotionData(promotionId).subscribe( respond  => {
          if( respond.status = "success"){
            console.log("respond.data: ",respond.data.data)
            this.promotion = respond.data.data.promotion
            this.promotionAvailableShared = respond.data.data.promotionAvailableShared
            this.specificUsers = respond.data.data.specificUsers
            this.nativeNotificationUsers = respond.data.data.nativeNotificationUsers

            this.loading = false
          }
      },
      respond_error => {
          this.loading = false
          let error_message = respond_error.error.message;
          this.toastrService.error(error_message);            
    })
  }


  onRemove(item, i, activeStatus){
    this.promotionService.changeActiveStatus(item._id,activeStatus).subscribe( 
      result =>{
        if(result.status == "success"){
              let _name_ = result.data.linkUrl
              if(activeStatus === false ){
                this.toastrService.error(
                  _name_ +" has been deactivated" 
                   ,  "Successfully deactivated" );
               }else{
                this.toastrService.success(
                  _name_ +  "has been recovered" 
                   ,  "Successfully recovered"  );
               }
              this.promotion.active = activeStatus
        }

    },
    respond_error => {
      this.toastrService.error(
        respond_error?.error.message
         , respond_error?.name);         
      }
    )
  }

  onPause(item, i, pauseStatus){
    this.promotionService.changePauseStatus(item._id,pauseStatus).subscribe( 
      result =>{
        if(result.status == "success"){
              let _name_ = result.data.linkUrl
              if(pauseStatus === true ){
                this.toastrService.error(
                  _name_ +" has been stoped" 
                   ,  "Successfully paused" );
               }else{
                this.toastrService.success(
                  _name_ +  "has been started again" 
                   ,  "Successfully started"  );
               }
              this.promotion.pause = pauseStatus
        }

    },
    respond_error => {
      this.toastrService.error(
        respond_error?.error.message
         , respond_error?.name);         
      }
    )
  }

  goToLink(url: string){
    window.open(url, "_blank");
  }

  redirectTo(uri:string){
    this.router.navigateByUrl(this.lang+ uri)
      .then(() =>{
        this.loadJsService.removeScripts()
      });
  }

}

