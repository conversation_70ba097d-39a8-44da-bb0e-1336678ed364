import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { AuthService } from 'src/app/shared/services/auth.service';
import { SearchListShownService } from 'src/app/shared/services/search-list-shown.service';

@Component({
  selector: 'app-search-list-shown',
  templateUrl: './search-list-shown.component.html',
  styleUrls: ['./search-list-shown.component.css'],
})
export class SearchListShownComponent implements OnInit {
  timeout: any = null;
  resultSearch: any = null;
  searchText = '';

  public pageNo: number = 1;
  public pageNoTotal: number = 1;
  count: number = 0;
  resultsPerPage: number = 7;
  data: any[] = [];
  loading = false;

  me: User;
  userSelected: User;

  constructor(
    private toastrService: ToastrService,
    private searchListShownService: SearchListShownService,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private modalService: NgbModal) {
      this.authService.user.pipe().subscribe((appUser) => {
        this.me = appUser;
      });
    }

  ngOnInit(): void {
    this.loading = true;
    this.activatedRoute.queryParams.subscribe((params) => {
      this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;
    });
    this.getAll();
  }

  openUserSearchModal(searchUser) {
    this.modalService.open(searchUser, { centered: true });
  }
  openSelectImgModal(selectImgModal) {
    this.modalService.open(selectImgModal, { centered: true });
  }


  public setPage(page: number) {
    this.data = [];
    this.loading = true;
    this.router
      .navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: page },
        queryParamsHandling: 'merge', // preserve the existing query params in the route
        skipLocationChange: false, // do trigger navigation
      })
      .finally(() => {
          if(this.searchText === ''){
            this.getAll();
          }else{
            this.getAllResultSearch()
          }
      });
  }

  getAll() {
    this.searchListShownService.getAll(this.pageNo, this.resultsPerPage).subscribe(
      async (result) => {
        if (result.status == 'success') {
          this.count = result.data.count;
          this.data = result.data.data;
          this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
          this.loading = false;
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }

  resultsPerPageChanged(event){
    this.loading = true;
    this.count =  1
    this.pageNoTotal = 1
    this.data = []
    this.resultsPerPage = Number(event)
    this.setPage(1)
  }

  async selectUser(user){
    if(this.data.some(e => e.user._id === user._id)){
      this.toastrService.error(
        'This user is already selected'
      );
    }else{
      this.data = [{user: user},...this.data]
    }
    this.modalService.dismissAll()
  }



  async removeUser(userId){
    this.searchListShownService.findByIdAndDelete(userId).subscribe(
      async (result) => {
        if (result.status == 'success') {
          this.data = this.data.filter(function( obj ) {
            return obj.user._id !== userId;
          });
          this.setPage(1)
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );

  }

  searchUser(searchText) {
    clearTimeout(this.timeout);
    var $this = this;
    this.timeout = setTimeout(function () {
      if (searchText.keyCode != 13) {
        $this.loading = true
        $this.searchSocket(searchText.target.value);
      }else{
        $this.loading = true
        $this.getAll()
      }
    }, 1000);
  } 
  public searchSocket(searchText) {
    this.searchText = searchText.toString();
    if(this.searchText == ''){
      this.loading = true
      this.setPage(1)
      return
    }
    this.data = []
    this.setPage(1)
  }
  getAllResultSearch() {
    if(this.searchText == '') return
    this.loading = true;
    this.count =  1
    this.pageNoTotal = 1
    this.data = []
    this.searchListShownService.search(this.searchText, this.me._id,this.pageNo,this.resultsPerPage).subscribe(
      (result) => {
        if (result.status == 'success') {
          this.count = result.data.count;
          this.data = result.data.data;
          this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
          this.loading = false;
        }
      },
      (respond_error) => {
        this.loading = false;
        // this.toastrService.error(
        //   respond_error?.error.message,
        //   respond_error?.name
        // );
      }
    );
  }

  save(){
    this.loading = true;
    this.searchListShownService.create(this.data).subscribe(
      async (result) => {
        if (result.status == 'success') {
          // this.count = result.data.count;
          // this.data = result.data.data;
          // this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
          this.loading = false;
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }

}
