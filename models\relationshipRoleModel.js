const mongoose = require('mongoose');

const relationshipRoleSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      trim: true
    },
    category: {
        type: mongoose.Schema.ObjectId,
        ref: 'PermissionCategory',
        required: [true, 'Relationship Role must have a category!']
    },
    createdBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Relationship Role must been created from a User!']
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }

);

relationshipRoleSchema.pre('save', async function( next) {
  next();
});

relationshipRoleSchema.pre(/^find/, function(next) {
  next();
})

const RelationshipRole = mongoose.model('RelationshipRole', relationshipRoleSchema);

module.exports = RelationshipRole;
