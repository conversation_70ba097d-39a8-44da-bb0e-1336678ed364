import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
  CanActivate,
  Router,
  ActivatedRoute,
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { take, map, switchMap, tap } from 'rxjs/operators';
import { CanLoad, Route, UrlSegment } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root',
})
export class IsAuthenticatedGuard implements CanLoad {
  constructor(
    private router: Router,
    private auth: AuthService
  ) {}

  canLoad(
    route: Route,
    segments: UrlSegment[]
  ): Observable<boolean> | Promise<boolean> | boolean {
    console.log("isAuthenticated")
    return this.auth.userIsAuthenticated.pipe(
      take(1),
      switchMap((isAuthenticated) => {
        if (!isAuthenticated) {
          return this.auth.autoLogin();
        } else {
          return this.auth.user.pipe(
            map((user) => {
              if (user) {
                return true;
              } else {
                // if(this.plt.is('hybrid')){
                //   this.router.navigateByUrl('/sign-in');
                // }else{
                //   this.router.navigateByUrl('/web-sign-in-up');
                // }
                this.router.navigateByUrl('/en/auth');

                return false;
              }
            })
          );
        }
      }),
      tap((isAuthenticated) => {
        if (!isAuthenticated) {
          this.router.navigateByUrl('/en/auth');
          // if(this.plt.is('hybrid')){
          //   this.router.navigateByUrl('/sign-in');
          // }else{
          //   this.router.navigateByUrl('/web-sign-in-up');
          // }
        }
      })
    );
  }
}
