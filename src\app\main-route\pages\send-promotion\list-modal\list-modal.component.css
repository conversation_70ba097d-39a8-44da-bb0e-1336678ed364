button {
  background-color: var(--action-color-50);
  border: 1px solid var(--action-color);
  color: white;
  border-radius: 10px;
  height: 30px;
  width: 80px;
}
.buttonReset {
  background-color: var(--action-color);
  color: white;
  border-radius: 10px;
  height: 30px;
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
}

.control {
  display: block;
  position: relative;
  padding-left: 30px;
  margin-bottom: 15px;
  cursor: pointer;
  font-size: 18px;
}
.control input {
  position: absolute;
  z-index: -1;
  opacity: 0;
}
.control__indicator {
  position: absolute;
  top: 2px;
  left: 0;
  height: 20px;
  width: 20px;
  background: #e6e6e6;
}
.control--radio .control__indicator {
  border-radius: 50%;
}
.control:hover input ~ .control__indicator,
.control input:focus ~ .control__indicator {
  background: var(--action-color-50);
}
.control input:checked ~ .control__indicator {
  background: var(--action-color);
  border-radius: 5px;
}
.control:hover input:not([disabled]):checked ~ .control__indicator,
.control input:checked:focus ~ .control__indicator {
  background: #0e647d;
}
.control input:disabled ~ .control__indicator {
  background: #e6e6e6;
  opacity: 0.6;
  pointer-events: none;
}
.control__indicator:after {
  content: "";
  position: absolute;
  display: none;
}
.control input:checked ~ .control__indicator:after {
  display: block;
}
.control--checkbox .control__indicator:after {
  left: 8px;
  top: 4px;
  width: 3px;
  height: 8px;
  border: solid #fff;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
.control--checkbox input:disabled ~ .control__indicator:after {
  border-color: #7b7b7b;
}
.control--radio .control__indicator:after {
  left: 7px;
  top: 7px;
  height: 6px;
  width: 6px;
  border-radius: 50%;
  background: #fff;
}
.control--radio input:disabled ~ .control__indicator:after {
  background: #7b7b7b;
}
p,
span,
div {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
