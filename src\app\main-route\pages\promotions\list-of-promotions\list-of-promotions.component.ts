import { Component, OnInit, TemplateRef } from '@angular/core';
import { UntypedFormBuilder } from '@angular/forms';
import { ActivatedRoute ,Router} from '@angular/router';
import { ngxLoadingAnimationTypes } from 'ngx-loading';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { LoadJsService } from 'src/app/shared/services/load-js.service';
import { PromotionService } from 'src/app/shared/services/promotion.service';

@Component({
  selector: 'app-list-of-promotions',
  templateUrl: './list-of-promotions.component.html',
  styleUrls: ['./list-of-promotions.component.css']
})
export class ListOfPromotionsComponent implements OnInit {

  lang = 'en';
  searchForm;
  
  loading = false

  promotions: any[] = []

  public ngxLoadingAnimationTypes = ngxLoadingAnimationTypes;
  public loadingTemplate: TemplateRef<any>;

  startLoading = false
  public pageNo: number = 1;
  public pageNoTotal: number = 1;
  promotionCount : number = 0;
  resultsPerPage : number = 5;

  constructor(
    private promotionService: PromotionService,
    private formBuilder: UntypedFormBuilder,
    private toastrService: ToastrService,
    public loadJsService: LoadJsService,
    private activatedRoute: ActivatedRoute,
    private router: Router
  ) { 
    this.searchForm = this.formBuilder.group({
      search: '',
    });
    this.loadJsService.loadScripts()
  }

  ngOnInit(): void {
    this.loading = true

    this.activatedRoute.params.subscribe( async paramMap => {
      if(paramMap['lang']){
        this.lang = paramMap['lang'];
      }
    })

    this.activatedRoute.queryParams.subscribe(params => {
      this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;
    })

    this.getAll()


  }

  public setPage(page: number) {
    this.promotions = []
    this.startLoading = true
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { page: page },
      queryParamsHandling: 'merge', // preserve the existing query params in the route
      skipLocationChange: false  // do trigger navigation
    }).finally(() => {
      this.getAll()
      // this.viewScroller.setOffset([120, 120]);
      // this.viewScroller.scrollToAnchor('deals'); // Anchore Link
    });
  }

  getAll(){
    this.promotionService.getAll(this.pageNo,this.resultsPerPage).subscribe( 
     async result =>{
        if(result.status == "success"){
              // this.users = await result.data.data.filter( user  =>{
              //   return  user._id !== this.user._id
              // });
              this.promotionCount = result.data.promotionCount

              console.log("result.data: ",result.data)
              this.pageNoTotal = Math.round(this.promotionCount / this.resultsPerPage) + 1 
              this.promotions = result.data.data
              this.loading = false
              this.startLoading = false
          }

      },
      respond_error => {
       this.toastrService.error(
         respond_error?.error.message
          , respond_error?.name);         
       }
    )
  }

  onRemove(item, i, activeStatus){

    this.promotionService.changeActiveStatus(item._id,activeStatus).subscribe( 
      result =>{
        if(result.status == "success"){
              let _name_ = result.data.linkUrl

              if(activeStatus === false ){
                this.toastrService.error(
                  _name_ +" has been deactivated" 
                   ,  "Successfully deactivated" );
               }else{
                this.toastrService.success(
                  _name_ +  "has been recovered" 
                   ,  "Successfully recovered"  );
               }
              this.promotions[i].active = activeStatus
        }

    },
    respond_error => {
      this.toastrService.error(
        respond_error?.error.message
         , respond_error?.name);         
      }
    )
  }

  onPause(item, i, pauseStatus){
    this.promotionService.changePauseStatus(item._id,pauseStatus).subscribe( 
      result =>{
        if(result.status == "success"){
              let _name_ = result.data.linkUrl
              if(pauseStatus === true ){
                this.toastrService.error(
                  _name_ +" has been stoped" 
                   ,  "Successfully paused" );
               }else{
                this.toastrService.success(
                  _name_ +  "has been started again" 
                   ,  "Successfully started"  );
               }
               this.promotions[i].pause = pauseStatus
        }

    },
    respond_error => {
      this.toastrService.error(
        respond_error?.error.message
         , respond_error?.name);         
      }
    )
  }
  

  ngOnDestroy(){
    this.loadJsService.removeScripts()
  }
  redirectTo(uri:string){
    this.router.navigateByUrl(this.lang+ uri)
      .then(() =>{
        this.loadJsService.removeScripts()
      });
  }

}
