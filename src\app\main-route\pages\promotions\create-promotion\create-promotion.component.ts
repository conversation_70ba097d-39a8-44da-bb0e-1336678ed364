import {
  Component,
  ElementRef,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/services/auth.service';
import { FilterService } from 'src/app/shared/services/filter.service';
import { LoadJsService } from 'src/app/shared/services/load-js.service';
import {
  FormControl,
  FormGroup,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { User } from 'src/app/shared/models/user';
import { PromotionService } from 'src/app/shared/services/promotion.service';
import { CompressImageService } from 'src/app/shared/services/compress-image.service';
import { ngxLoadingAnimationTypes } from 'ngx-loading';
import { FiberBankTransactionService } from 'src/app/shared/services/fiber-bank-transaction.service';
import { UserService } from 'src/app/shared/services/user.service';
// import { ModalService } from 'src/app/_modal';
import { PromotionAvailableService } from 'src/app/shared/services/promotion-available.service';
import { IAngularMyDpOptions, IMyDateModel } from 'angular-mydatepicker';
import * as moment from 'moment';
import { SocketService } from 'src/app/shared/services/socket.service';

@Component({
  selector: 'app-create-promotion',
  templateUrl: './create-promotion.component.html',
  styleUrls: ['./create-promotion.component.css'],
})
export class CreatePromotionComponent implements OnInit {
  public selectedMoment = new Date();
  loading = false;

  cost: number = 0;
  allowedToShare: number = 0;
  linkUrl: string = '';

  form = new UntypedFormGroup({});
  lang = 'en';
  me: User;
  disableSubmit = false;

  selectedFile: File[] = [];
  img = null;
  imageSize = '0';
  imageName = '';

  public ngxLoadingAnimationTypes = ngxLoadingAnimationTypes;
  public loadingTemplate: TemplateRef<any>;

  followersCountActive = false;
  followersCountMin = undefined;
  followersCountMax = undefined;

  followingCountActive = false;
  followingCountMin = undefined;
  followingCountMax = undefined;

  postCountActive = false;
  postCountMin = undefined;
  postCountMax = undefined;

  earnCountActive = false;
  earnCountMin = undefined;
  earnCountMax = undefined;

  userIsAllowedToShare = 'once';

  totalUsersWithFilter = 0;

  activeTab = 'promotionDetails';
  timeout: any = null;
  filters: any = {
    followersCountActive: false,
    followingCountActive: false,
    postCountActive: false,
    earnCountActive: false,
  };

  modalOpen = false;

  userFilteredData = [];
  pageNumber = 0;
  loadingData = false;
  resultLength = 0;

  checkboxFalseUsersId = [];
  checkboxFalseUsers = [];
  allUsers = [];

  timeAllowedToShareMinutes = 5;
  minutesDiffernce = 10;
  startPromoteDateTime = new Date();

  socket;

  constructor(
    private socketService: SocketService,
    private router: Router,
    private userService: UserService,
    private promotionAvailableService: PromotionAvailableService,
    private authService: AuthService,
    // private modalService: ModalService,
    private toastrService: ToastrService,
    private compressImageService: CompressImageService,
    private promotionService: PromotionService,
    private fiberBankTransactionService: FiberBankTransactionService,
    private activatedRoute: ActivatedRoute,
    public loadJsService: LoadJsService,
    private formBuilder: UntypedFormBuilder
  ) {
    this.socket = this.socketService.setupSocketConnection();
    this.loading = true;
  }

  ngOnInit(): void {
    this.authService.user.pipe().subscribe((appUser) => {
      this.me = appUser;
    });
    this.activatedRoute.params.subscribe(async (params) => {
      if (params['lang']) {
        this.lang = params['lang'];
      }
    });
    this.prepareForm();
    this.loadJsService.loadScripts();
    this.loading = false;
  }

  search(searchText, inputForm) {
    clearTimeout(this.timeout);
    var $this = this;
    this.timeout = setTimeout(function () {
      if (searchText.keyCode != 13) {
        $this.getUserFiltered(searchText.target.value, inputForm);
      }
    }, 1000);
  }

  getUserFiltered(value, inputForm) {
    if (inputForm === 'followersCountMin') {
      if (value !== '') {
        this.filters['followersCountMin'] = value;
      } else {
        delete this.filters.followersCountMin;
      }
    }
    if (inputForm === 'followersCountMax') {
      if (value !== '') {
        this.filters['followersCountMax'] = value;
      } else {
        delete this.filters.followersCountMax;
      }
    }

    if (inputForm === 'followingCountMin') {
      if (value !== '') {
        this.filters['followingCountMin'] = value;
      } else {
        delete this.filters.followingCountMin;
      }
    }
    if (inputForm === 'followingCountMax') {
      if (value !== '') {
        this.filters['followingCountMax'] = value;
      } else {
        delete this.filters.followingCountMax;
      }
    }

    if (inputForm === 'postCountMin') {
      if (value !== '') {
        this.filters['postCountMin'] = value;
      } else {
        delete this.filters.postCountMin;
      }
    }
    if (inputForm === 'postCountMax') {
      if (value !== '') {
        this.filters['postCountMax'] = value;
      } else {
        delete this.filters.postCountMax;
      }
    }

    if (inputForm === 'earnCountMin') {
      if (value !== '') {
        this.filters['earnCountMin'] = value;
      } else {
        delete this.filters.earnCountMin;
      }
    }
    if (inputForm === 'earnCountMax') {
      if (value !== '') {
        this.filters['earnCountMax'] = value;
      } else {
        delete this.filters.earnCountMax;
      }
    }
    this.prepareFilter();
  }

  prepareFilter() {
    this.filters.followersCountActive = this.followersCountActive;
    this.filters.followingCountActive = this.followingCountActive;
    this.filters.postCountActive = this.postCountActive;
    this.filters.earnCountActive = this.earnCountActive;
    this.loading = true;
    this.userService.getUserFiltered(this.filters).subscribe(
      (respond) => {
        if ((respond.status = 'success')) {
          this.allUsers = [];
          this.allUsers = respond.data.data;
          console.log('respond.data: ', respond.data.data);
          this.totalUsersWithFilter = respond.data.totalUsersWithFilter;
          this.loading = false;
        }
      },
      (respond_error) => {
        this.loading = false;
        this.disableSubmit = false;
        let error_message = respond_error.error.message;
        this.toastrService.error(error_message);
      }
    );
  }

  async prepareForm() {
    this.form = this.formBuilder.group({
      name: new UntypedFormControl(null, [Validators.required]),
      surname: new UntypedFormControl(null, [Validators.required]),
    });
  }

  async submit() {
    let startEnd = this.startPromoteDateTime;
    console.log('this.allUsers: ', this.allUsers);
    let totalUsersWithFilter = await this.allUsers.filter((data) => {
      if (this.checkboxFalseUsersId.indexOf(data._id) === -1) {
        return data._id;
      }
    });

    let startTime = startEnd;
    startEnd = moment(startTime)
      .add(this.timeAllowedToShareMinutes, 'm')
      .toDate();

    this.disableSubmit = true;
    this.loading = true;
    this.selectedFile;

    if (this.cost * this.allowedToShare <= 0) {
      this.disableSubmit = false;
      this.loading = false;
      this.toastrService.error(
        'Budget of this promotion need to be more than 0 coins'
      );
      return;
    }
    if (this.selectedFile.length == 0) {
      this.disableSubmit = false;
      this.loading = false;
      this.toastrService.error(
        'Empty post without images is not allowed to be posted'
      );
      return;
    } else {
      this.disableSubmit = false;

      // this.promotionService.create(
      //   this.me._id,
      //   this.cost,
      //   this.allowedToShare,
      //   this.followersCountActive,
      //   this.followersCountMin,
      //   this.followersCountMax,
      //   this.followingCountActive,
      //   this.followingCountMin,
      //   this.followingCountMax,
      //   this.postCountActive,
      //   this.postCountMin,
      //   this.postCountMax,
      //   this.earnCountActive,
      //   this.earnCountMin,
      //   this.earnCountMax,
      //   this.timeAllowedToShareMinutes,
      //   this.minutesDiffernce,
      //   this.startPromoteDateTime,
      //   this.linkUrl,
      //   this.selectedFile
      // ).subscribe( async respond  => {
      //   this.loading = false
      //   this.disableSubmit = false
      //         if( respond.status = "success"){
      //           let reason = "Budget from " +this.linkUrl + " for Fiber Bank Account to promote"
      //           let i = 1
      //           console.log("totalUsersWithFilter: ",totalUsersWithFilter)
      //           for(let user of totalUsersWithFilter){
      //               startTime = moment(startEnd).add(this.minutesDiffernce, 'm').toDate();
      //               startEnd = moment(startTime).add(this.timeAllowedToShareMinutes, 'm').toDate();
      //               await this.createPromotionAvailable(
      //                 startTime,
      //                 startEnd,
      //                 respond.data.data._id,
      //                 user._id
      //               )
      //               if(i === totalUsersWithFilter.length){
      //                 this.socket.emit('ipromotion', {
      //                   promotionId: respond.data.data._id,
      //                   sendStatus: true,
      //                   meId: user._id,
      //                   startTime: this.startPromoteDateTime
      //                 });
      //               }
      //               i = i + 1
      //           }
      //           this.toastrService.success( "Successfully promotion has been created" );
      //           this.img = null
      //           this.selectedFile = []
      //           this.cost = 0
      //           this.allowedToShare = 0
      //           this.linkUrl = ''
      //           this.loading = false
      //           this.disableSubmit = false
      //           this.router.navigate(["/" + this.lang + "/list-of-promotions"]).then(() => {});
      //           // this.create(this.cost*this.allowedToShare,reason)
      //         }
      //     },
      //     respond_error => {
      //         this.loading = false
      //         this.disableSubmit = false
      //         let error_message = respond_error.error.message;
      //         this.toastrService.error(error_message);
      //     }
      // );
    }
  }

  create(cost, reason) {
    this.fiberBankTransactionService
      .create(cost, reason, false, true, this.me._id)
      .subscribe(
        (respond) => {
          if ((respond.status = 'success')) {
            // this.toastrService.success( "Successfully promotion has been created" );
            this.img = null;
            this.selectedFile = [];
            this.cost = 0;
            this.allowedToShare = 0;
            this.linkUrl = '';
            this.loading = false;
            this.disableSubmit = false;
            this.router
              .navigate(['/' + this.lang + '/list-of-promotions'])
              .then(() => {});
          }
        },
        (respond_error) => {
          this.toastrService.error(
            respond_error?.error.message,
            respond_error?.name
          );
        }
      );
  }

  createFormData(event) {
    this.imageSize = (event.target.files[0].size / (1024 * 1024)).toFixed(2);
    this.imageName = event.target.files[0].name;

    this.compressImageService
      .compress(event.target.files[0])
      .subscribe((result) => {
        // this.selectedFile = result;
        this.selectedFile = [...this.selectedFile, result];
      });
    var reader = new FileReader();
    reader.readAsDataURL(event.target.files[0]);

    reader.onload = (_event) => {
      this.compressImageService
        .compressImage(reader.result, 600, 338)
        .then((compressed) => {
          this.img = compressed;
        });
    };
  }

  openModal(id: string) {
    this.modalOpen = true;
    this.resultLength = 0;
    this.pageNumber = 0;
    // this.checkboxFalseUsersId = []
    this.getUserFilteredData('next');
    // this.modalService.open(id);
  }
  closeModal(id: string) {
    this.modalOpen = false;
    // this.modalService.close(id);
  }

  getUserFilteredData(reason) {
    this.loadingData = true;
    if (reason.toString() === 'next') {
      this.pageNumber = this.pageNumber + 1;
    } else {
      this.pageNumber = this.pageNumber - 1;
    }
    this.userService
      .getUserFilteredData(this.filters, this.pageNumber, 10)
      .subscribe(
        (respond) => {
          if ((respond.status = 'success')) {
            let totalUsersWithFilter = [];
            if (respond.data.totalUsersWithFilter.length) {
              totalUsersWithFilter = respond.data.totalUsersWithFilter.map(
                (data) => {
                  let dataCopy = data;
                  let result = this.checkboxFalseUsersId.indexOf(data._id);
                  dataCopy['checked'] = result === -1 ? true : false;
                  return dataCopy;
                }
              );
            }

            this.userFilteredData = totalUsersWithFilter;

            if (reason.toString() === 'next') {
              this.resultLength =
                this.resultLength + totalUsersWithFilter.length;
            } else {
              this.resultLength =
                this.resultLength - totalUsersWithFilter.length;
            }

            this.loadingData = false;
            // this.totalUsersWithFilter = respond.data.totalUsersWithFilter
            // this.loading = false
          }
        },
        (respond_error) => {
          this.loadingData = false;
          this.disableSubmit = false;
          let error_message = respond_error.error.message;
          this.toastrService.error(error_message);
        }
      );
  }

  async createPromotionAvailable(timeStart, timeEnd, promotion, user) {
    this.promotionAvailableService
      .create(timeStart, timeEnd, user, promotion)
      .subscribe(
        (respond) => {
          if ((respond.status = 'success')) {
          }
        },
        (respond_error) => {
          let error_message = respond_error.error.message;
          this.toastrService.error(error_message);
        }
      );
  }

  changeCheckbox(user, event) {
    if (event.currentTarget.checked) {
      this.checkboxFalseUsersId.splice(
        this.checkboxFalseUsersId.indexOf(user._id),
        1
      );
    } else {
      this.checkboxFalseUsersId = [...this.checkboxFalseUsersId, user._id];
    }
  }
}
