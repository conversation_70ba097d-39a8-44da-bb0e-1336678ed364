import { Component, Input, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { User } from 'src/app/shared/models/user';
import { AuthService } from 'src/app/shared/services/auth.service';
import { ImageService } from 'src/app/shared/services/image.service';

@Component({
  selector: 'app-img',
  templateUrl: './app-img.component.html',
  styleUrls: ['./app-img.component.scss'],
})
export class ImgComponent implements OnInit {

  @Input() buffer;
  @Input() class = '';
  src : any = 'assets/blank-image.jpg'
  
  loading = true

  @Input() meId = '';
  @Input() postId = '';
  @Input() postUserId = '';
  @Input() promotionId = '';
  @Input() isPromotion = false;

  me: User;

  constructor(
    private authService: AuthService,
    private imageService: ImageService,
    private domSanitizer: DomSanitizer
  ) { 
    this.authService.user.pipe().subscribe((appUser) => {
      this.me = appUser;
    });
  }

  ngOnInit() {
    if(this.buffer){
      this.loadImage()
    }
    if(this.postId !== '' && this.isPromotion == false){
      this.getPostImages()
    }
    if(this.promotionId !== '' && this.isPromotion){
      this.getImageByPromotionId()
    }
  }

  getPostImages() {
    this.imageService
      .getPostImages(this.postId, this.meId, this.postUserId)
      .subscribe(
        (result) => {
          if (result.status == 'success') {
            this.buffer = result.data.images[0].data
            this.loadImage()
          }
        },
        (respond_error) => {});

  }

  getImageByPromotionId() {
        this.imageService
          .getImageByPromotionId(this.promotionId, this.me._id)
          .subscribe(
            (result) => {
              if (result.status == 'success') {
                // console.log("result.data: ",result.data)
                if(result.data.images.length !== 0){
                  this.buffer = result.data.images[0].data
                  this.loadImage()
                }
              }
            },
            (respond_error) => {
              // this.toastrService.error(
              //   respond_error?.error.message,
              //   respond_error?.name
              // );
            }
          );
  }

  async loadImage(){
    let base64 =   await this.prepareBase64(this.buffer)
    this.src =  await this.prepareBypassSecurity(base64)
    this.loading = false
  }
  async prepareBase64(data){
    let TYPED_ARRAY = await  new Uint8Array(data.data);
    const STRING_CHAR  = TYPED_ARRAY.reduce((data, byte)=> {
      return data + String.fromCharCode(byte);
      }, '')
    let base64String = await btoa(STRING_CHAR);
    return  base64String
  }

  async prepareBypassSecurity(base64String){
    return  this.domSanitizer
   .bypassSecurityTrustResourceUrl('data:image/jpg;base64,'+ await base64String)
  }
  
}
