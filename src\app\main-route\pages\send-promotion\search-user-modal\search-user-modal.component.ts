import { Component, OnInit } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-search-user-modal',
  templateUrl: './search-user-modal.component.html',
  styleUrls: ['./search-user-modal.component.css'],
})
export class SearchUserModalComponent {
  constructor(
    private modalService: NgbModal,
    public activeModal: NgbActiveModal
  ) {}
  openSearchModal(searchModal) {
    this.modalService.open(searchModal, { centered: true });
  }
}
