const catchAsync = require("./../utils/catchAsync");
const User = require("./../models/userModel");
const Gift = require("./../models/giftModel");
const Post = require("./../models/postModel");

exports.create = catchAsync(async (req, res, next) => {
  // console.log("req.body.data: ",req.body.data)
  if (req.body.data !== null) {
    await Gift.create(req.body.data);
  }
  res.status(201).json({
    status: "success",
    data: {},
  });
});

exports.getAll = catchAsync(async (req, res, next) => {
  let resultsPerPage = req.body.resultsPerPage;
  let page = req.body.page;
  page = page - 1;

  let data = await Gift.find()
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ orderIndex: 1 });

  let count = await Gift.countDocuments();

  res.status(200).json({
    status: "success",
    data: {
      data: data,
      count: count,
    },
  });
});

exports.getData = catchAsync(async (req, res, next) => {
  let data = await Gift.findById(req.params.id);
  res.status(201).json({
    status: "success",
    data: data,
  });
});

exports.edit = catchAsync(async (req, res, next) => {
  let data = await Gift.findById(req.params.id);
  if (data !== null) {
    await Gift.findByIdAndUpdate(req.params.id, req.body.data);
  }
  res.status(201).json({
    status: "success",
  });
});
