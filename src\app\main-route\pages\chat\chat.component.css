.chat-section {
  display: grid;
  grid-template-columns: 1fr 2fr;
}
.sent-message {
  background-color: var(--action-color-50);
  place-self: end;
  width: -moz-fit-content;
  width: fit-content;
  display: flex;
  flex-direction: column;
  max-width: 80%;
  padding: 1em;
  border-radius: 24px 0 24px 24px;
}

.received-message {
  background-color: var(--bs-gray-800);
  width: -moz-fit-content;
  width: fit-content;
  display: flex;
  flex-direction: column;
  max-width: 80%;
  padding: 1em;
  border-radius: 0px 24px 24px 24px;
}

.sent-message-wrapper {
  /* background-color: var(--action-color); */
  place-self: end;
  width: -moz-fit-content;
  width: fit-content;
  display: flex;
  max-width: 80%;
  padding: 1em;
  border-radius: 24px 0 24px 24px;
  align-items: flex-start;
  gap: 3px;
}

.received-message-wrapper {
  /* background-color: var(--bs-gray-800); */
  width: -moz-fit-content;
  width: fit-content;
  display: flex;
  flex-direction: column;
  max-width: 80%;
  padding: 1em;
  border-radius: 24px;
}

.listChatWrapper {
  list-style-type: none;
  background-color: transparent !important;
  display: flex;
}
.seen {
  opacity: 0.5;
}
p,
span,
div {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
