const mongoose = require('mongoose');

const payPerViewSchema = new mongoose.Schema({
  post: {
    type: mongoose.Schema.ObjectId,
    ref: 'Post',
    required: [true, 'Like must have Post!']
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: [true, 'Like must been created from a User!']
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

payPerViewSchema.pre('save', async function( next) {
    await this.populate({
            path: 'user',
        }).execPopulate();;

        next();
    });
  
  
  
  payPerViewSchema.pre(/^find/, function(next) {
    this.populate({
      path: 'user',
    });
    next();
  })

const PayPerView = mongoose.model('PayPerView', payPerViewSchema);

module.exports = PayPerView;
