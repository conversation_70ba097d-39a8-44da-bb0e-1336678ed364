import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { OnlinePaymentService } from 'src/app/shared/services/online-payment.service';
import { AuthService } from 'src/app/shared/services/auth.service';
import { User } from 'src/app/shared/models/user';

@Component({
  selector: 'app-list-of-online-payment',
  templateUrl: './list-of-online-payment.component.html',
  styleUrls: ['./list-of-online-payment.component.css'],
})
export class ListOfOnlinePaymentComponent implements OnInit {

  lang = 'en';
  searchForm;

  loading = false;

  data: any[] = [];


  startLoading = false;
  public pageNo: number = 1;
  public pageNoTotal: number = 1;
  count: number = 0;
  resultsPerPage: number = 7;

  selectedOnlinePayment = null

  me: User;

  timeout: any = null;
  resultSearch: any = null;
  searchText = '';

  constructor(
    private authService: AuthService,
    private onlinePaymentService: OnlinePaymentService,
    private toastrService: ToastrService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private modalService: NgbModal
  ) {
    this.authService.user.pipe().subscribe((appUser) => {
      this.me = appUser;
      // if (!this.me) {
      //   this.router.navigate(['/sign-in/']);
      //   return;
      // }
    });
  }

  ngOnInit(): void {
    this.loading = true;
    this.startLoading = true;
    this.activatedRoute.params.subscribe(async (paramMap) => {
      if (paramMap['lang']) {
        this.lang = paramMap['lang'];
      }
    });

    this.activatedRoute.queryParams.subscribe((params) => {
      this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;
    });

    this.getAll();
  }

  public setPage(page: number) {
    this.data = [];
    this.startLoading = true;
    this.router
      .navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: page },
        queryParamsHandling: 'merge', // preserve the existing query params in the route
        skipLocationChange: false, // do trigger navigation
      })
      .finally(() => {
        if(this.searchText === ''){
          this.getAll();
        }else{
          this.getAllResultSearch()
        }
        // this.viewScroller.setOffset([120, 120]);
        // this.viewScroller.scrollToAnchor('deals'); // Anchore Link
      });
  }

  getAll() {
    this.onlinePaymentService.getAll(this.pageNo, this.resultsPerPage).subscribe(
      async (result) => {
        if (result.status == 'success') {
          // this.users = await result.data.data.filter( user  =>{
          //   return  user._id !== this.user._id
          // });
          this.count = result.data.count;
          // console.log('result.data.data: ', result.data);
          this.data = result.data.data;

          this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
          this.loading = false;
          this.startLoading = false;
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }
  resultsPerPageChanged(event){
    this.loading = true;
    this.startLoading = true;
    this.resultsPerPage = Number(event)
    this.count =  1
    this.pageNoTotal = 1
    this.data = []
    this.setPage(1)
  }
  ngOnDestroy() {
  }
  redirectTo(uri: string) {
    this.router.navigateByUrl(this.lang + uri).then(() => {
    });
  }

  openOptionsModal(optionsContent) {
    this.modalService.open(optionsContent, { centered: true });
  }


  selectOnlinePayment(data){
    this.selectedOnlinePayment = data
  }


  searchUser(searchText) {
    clearTimeout(this.timeout);
    var $this = this;
    this.timeout = setTimeout(function () {
      if (searchText.keyCode != 13) {
        $this.startLoading = true
        $this.loading = true
        $this.searchSocket(searchText.target.value);
      }else{
        $this.startLoading = true
        $this.loading = true
        $this.setPage(1)
        $this.getAll()
      }
    }, 1000);
  } 

  public searchSocket(searchText) {
    this.searchText = searchText.toString();
    if(this.searchText == ''){
      this.startLoading = true
      this.loading = true
      this.setPage(1)
      return
    }
    this.data = []
    this.setPage(1)
  }

  getAllResultSearch() {
    if(this.searchText == '') return
    this.loading = true;
    this.startLoading = true;
    this.data = []

    this.onlinePaymentService.searchOnlinePayment(this.searchText, this.me._id,this.pageNo,this.resultsPerPage).subscribe(
      (result) => {
        if (result.status == 'success') {
          // console.log("this.resultSearch: ",result.resultSearch)
          this.count = result.data.count;
          console.log('result.data.data: ', result.data);
          this.data = result.data.data;
          this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
          this.loading = false;
          this.startLoading = false;

        }
      },
      (respond_error) => {
        this.loading = false;
        this.startLoading = false;
        // this.toastrService.error(
        //   respond_error?.error.message,
        //   respond_error?.name
        // );
      }
    );
  }


}
