const mongoose = require('mongoose');

const likeSchema = new mongoose.Schema({
  post: {
    type: mongoose.Schema.ObjectId,
    ref: 'Post',
    required: [true, 'Like must have Post!']
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: [true, 'Like must been created from a User!']
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

likeSchema.pre('save', async function( next) {

    // await this.populate({
    //   path: 'post',
    // })
    
    await this.populate({
        path: 'user',
      }).execPopulate();;

    next();
});

likeSchema.pre(/^find/, function(next) {
    // this.populate({
    //     path: 'post',
    // });
    this.populate({
      path: 'user',
    });
    next();
})

const Like = mongoose.model('Like', likeSchema);

module.exports = Like;
