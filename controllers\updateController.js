const catchAsync = require('./../utils/catchAsync');
const Update = require('./../models/updateModel');

exports.create = catchAsync(async (req, res, next) => {
    
const doc = await Update.create(req.body);

res.status(201).json({
  status: 'success',
  data: {
    data: doc
  }
});

});


exports.getAll = catchAsync(async (req, res, next) => {
  let updates = await Update.find({}).sort({updatedAt:-1});

  res.status(200).json({
    status: 'success',
    data: {
      data: updates
    }
  });

});




exports.getAllVersions = catchAsync(async (req, res, next) => {
  let resultsPerPage =  req.body.resultsPerPage  >= 5 ? req.body.resultsPerPage : 5;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1

  let data =
   await Update.find()
  .limit( resultsPerPage)
  .skip( resultsPerPage * page)
  .sort({ _id: -1 })

  let count = await Update.countDocuments()

  res.status(200).json({
    status: 'success',
    data: {
      data: data,
      count: count
    }
  });

});



exports.getData = catchAsync(async (req, res, next) => {
  let data = await Update.findById(req.params.id)
  res.status(201).json({
      status: 'success',
      data: data
  });
});


exports.edit = catchAsync(async (req, res, next) => {
  console.log("req.params.id: ",req.params.id)
  console.log("req.body.data: ",req.body.data)

  let data = await Update.findById(req.params.id)
  if(data !== null){
      await Update.findByIdAndUpdate(req.params.id , req.body.data);
  }
  res.status(201).json({
      status: 'success'
  });
});