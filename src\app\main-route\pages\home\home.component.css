.dotted {
  border: 1px dotted rgb(229, 255, 0);
}
.dashContentWrapper {
  display: grid;
  grid-template-columns: 0.7fr 2fr 0.3fr;
  height: 90%;
}
.dashLeftContent {
  display: flex;
  flex-direction: column;
  gap: 1em;
  padding: 1em;
  overflow-x: scroll;
  overflow-y: scroll;
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */
}

.dashLeftContent::-webkit-scrollbar {
  display: none;
}
.dashLeftHeaderWrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5em;
}
.dashLeftHeader {
  width: fit-content;
  background: var(--action-color);
  padding: 1em;
  border-radius: 23px;
  color: var(--text-dark);
  font-weight: 700;
}
.dashLeftHeaderContent {
  font-weight: 500 !important;
  opacity: 0.7;
}
.leftContentTabs {
  display: flex;
  gap: 5px;

  flex-wrap: wrap;
}
.leftContentTabs > span {
  /* width: 100%; */
  background-color: var(--app-content-secondary-color);
  padding: 4px 1em;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}
.leftContentTabs > span.active {
  background-color: #eaeaea;
  padding: 4px 1em;
  border-radius: 4px;
  color: var(--app-content-secondary-color);
}
.finStats {
  font-size: var(--font-size-xl);
  font-weight: 500;
  color: var(--c-text-secondary);
  margin: 0;
}
.finStatsNum {
  font-size: var(--font-size-xxxl);
  font-weight: 500;
  color: var(--c-text-primary);
  margin: 5px 0.5em;
}
.finStatsTarget {
  font-size: var(--font-size-s);
  font-weight: 600;
  color: var(--c-text-tertiary);
  margin: 0;
}
.quantityOfItemHeader {
  margin: 0 0 10px;
  font-size: var(--font-size-l);
  color: var(--c-text-secondary);
}
.quantityOfItem > span {
  color: var(--c-gray-200);
}
.quantityOfItem :nth-child(1) {
  color: #c000ff73;
}
/* !Graph */
.quiz-chartTip {
  padding: 5px 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.1);
  position: absolute;
  z-index: 50;
  max-width: 250px;
}

.quiz-graph {
  height: fit-content;
  width: 100%;
}

.quiz-graph .x-labels {
  text-anchor: middle;
}

.quiz-graph .y-labels {
  text-anchor: end;
}

.quiz-graph .quiz-graph-grid {
  stroke: #ccc;
  stroke-dasharray: 0;
  stroke-width: 1;
}

.label-title {
  text-anchor: middle;
  text-transform: uppercase;
  font-size: 12px;
  fill: rgba(128, 128, 128, 0.419);
}

.quiz-graph-dot,
.quiz-graph-start-dot {
  fill: rgb(255, 153, 0);
  stroke-width: 2;
  stroke: rgb(255, 255, 255);
}

@keyframes animation {
  100% {
    width: 0%;
  }
}

h1,
table {
  margin: 1em auto;
  max-width: 600px;
}

th {
  border-right: solid 1px black;
  width: 25%;
  text-align: left;
  font-size: 12px;
}

td {
  width: 75%;
}

td div.value {
  background-color: var(--action-color-50);
  height: 2em;
  animation: animation reverse 1s linear;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-radius: 0 10px 10px 0;
}

tr > * {
  padding: 0.5em 2em 0.5em 0;
}

.value {
  text-align: right;
  color: white;
  line-height: 1em;
}
/* !end */

.quantityOfItem {
  display: grid;
  grid-template-columns: 5% 45% 25% 25%;
}
.nucleus {
  width: 23%;
  height: 30%;
  border-radius: 50%;
  /* background: var(--app-content-secondary-color); */
  position: absolute;
  /* opacity: 0.5; */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: url(/assets/nucleus.svg);
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  background-repeat: no-repeat;
}
.nucleus:hover {
  /* background: var(--app-bg); */
  transition: 500ms linear;
  scale: 1.15;
  box-shadow: 0 14px 28px rgba(69, 39, 84, 0.25),
    0 10px 10px rgba(109, 58, 135, 0.22);
}
.percentageDashboard {
  fill: #fff;
  font-size: 0.4em;
  text-anchor: middle;
  font-weight: 400;
}
.mWrapper {
  width: 5%;
  height: 6.5%;
  transform: translate(-50%, -50%);
  background: var(--sidebar-hover-link);
  position: absolute;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.mWrapper:hover {
  background: var(--app-bg);
  transition: 500ms linear;
  scale: 1.1;
  box-shadow: 0 14px 28px rgba(69, 39, 84, 0.25),
    0 10px 10px rgba(109, 58, 135, 0.22);
}
.mText {
  position: absolute;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}
.lWrapper {
  width: 6.5%;
  height: 8%;
  transform: translate(-50%, -50%);
  background: var(--app-bg);
  position: absolute;
  border-radius: 50%;
  border: 7px solid rgb(181, 253, 255);
  display: flex;
  align-items: center;
  justify-content: center;
}
.lWrapper:hover {
  background: var(--app-bg);
  transition: 500ms linear;
  scale: 1.1;
  box-shadow: 0 14px 28px rgba(207, 111, 255, 0.25),
    0 10px 10px rgba(207, 111, 255, 0.22);
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
}
.KWrapper {
  width: 11.5%;
  height: 15%;
  transform: translate(-50%, -50%);
  background: url(/assets/gradients/dark.svg);
  position: absolute;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.KWrapper p {
  margin: 0;
  font-size: 15px;
}
.KWrapper:hover {
  background: url(/assets/gradients/light.svg);
  transition: 500ms linear;
  scale: 1.2;
  box-shadow: 0 14px 28px rgba(207, 111, 255, 0.25),
    0 10px 10px rgba(207, 111, 255, 0.22);
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
}

.dashRightContent {
  display: grid;
  grid-template-rows: 0.5fr 1fr 1fr;
  height: 90%;
  gap: 1em;
  justify-content: end;
}

.dashRightContent > div {
  background-color: #060f29;
  border-radius: 15px;
  padding: 0.6em 5px;
  max-width: 105px;
  display: flex;
  align-items: center;
}

.finRightNum {
  font-size: var(--font-size-xl);
  font-weight: 500;
  color: var(--c-text-primary);
  margin: 0;
  text-align: end;
  width: 90%;
}

.finRightStats {
  font-size: var(--font-size-l);
  font-weight: 500;
  color: var(--c-text-secondary);
  margin: 0;
  text-align: end;
  width: 90%;
}

.finRightTarget {
  font-size: 10px;
  font-weight: 600;
  color: var(--c-text-tertiary);
  margin: 0;
  text-align: end;
  width: 90%;
}
tr > * {
  padding: 0px 0px 5px 5px;
}
td div.value {
  background-color: var(--action-color-50);
  height: 0.5em;
  animation: animation reverse 1s linear;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-radius: 0 10px 10px 0;
}
th {
  border-right: solid 1px rgba(0, 0, 0, 0);
  width: 25%;
  text-align: left;
  font-size: 11px;
}
p,
span,
div {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
