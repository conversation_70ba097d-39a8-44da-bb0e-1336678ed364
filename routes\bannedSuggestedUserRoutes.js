const express = require('express');
const bannedSuggestedUserController = require('./../controllers/bannedSuggestedUserController');

const router = express.Router();

router
.route('/')
.post(
    bannedSuggestedUserController.create
);



router
.route('/getAll')
.post(
    bannedSuggestedUserController.getAll
);


router
.route('/findByIdAndDelete')
.post(
    bannedSuggestedUserController.findByIdAndDelete
);

router
  .route('/search')
  .post(bannedSuggestedUserController.search);


module.exports = router;