// const  = require('multer');
// const sharp = require('sharp');
const User = require("./../models/userModel");
const Transaction = require("./../models/transactionModel");
const catchAsync = require("./../utils/catchAsync");
const AppError = require("./../utils/appError");
const factory = require("./handlerFactory");
const Email = require("./../utils/email");
const FiberBankTransaction = require("./../models/fiberBankTransactionModel");
const MyGiftModel = require("./../models/myGiftModel");

const Post = require("./../models/postModel");
const Like = require("./../models/likeModel");
const Comment = require("./../models/commentModel");
const Gift = require("./../models/postGiftsModel");
const Following = require("./../models/followingModel");

exports.resizePhoto = catchAsync(async (req, res, next) => {
  if (!req.files.file) return next();

  // sharp(req.files.file.path)
  // .rotate()
  // .resize(600)
  // .flatten(true)
  // .flatten({ background: {  r: 255, g: 255, b: 255}})
  // .jpeg({ mozjpeg: true })
  // .toBuffer()
  // .then( data => {
  //   req.body.buffer = data
  //   next();

  //  })
  // .catch( err => {
  //     next();
  //  });
});

const filterObj = (obj, ...allowedFields) => {
  const newObj = {};
  Object.keys(obj).forEach((el) => {
    if (allowedFields.includes(el)) newObj[el] = obj[el];
  });
  return newObj;
};

exports.getMe = (req, res, next) => {
  req.params.id = req.user.id;
  next();
};

exports.updateMe = catchAsync(async (req, res, next) => {
  // 1) Create error if user POSTs password data
  if (req.body.password || req.body.passwordConfirm) {
    return next(
      new AppError(
        "This route is not for password updates. Please use /updateMyPassword.",
        400
      )
    );
  }

  let respond = {
    _id: JSON.parse(req.body._id),
    name: JSON.parse(req.body.name),
    surname: JSON.parse(req.body.surname),
  };

  const updatedUser = await User.findByIdAndUpdate(
    JSON.parse(req.body._id),
    respond,
    { new: true }
  );
  if (!updatedUser) {
    return next(new AppError("No document found with that ID", 404));
  }
  res.status(200).json({
    status: "success",
    data: {
      data: updatedUser,
    },
  });
});

exports.updateUserRole = catchAsync(async (req, res, next) => {
  const updatedUser = await User.findByIdAndUpdate(req.params.id, {
    role: req.body.role,
  });

  res.status(200).json({
    status: "success",
    data: {
      user: updatedUser,
    },
  });
});

exports.deleteMe = catchAsync(async (req, res, next) => {
  await User.findByIdAndUpdate(req.user.id, { active: false });

  res.status(204).json({
    status: "success",
    data: null,
  });
});

exports.changeActiveStatus = catchAsync(async (req, res, next) => {
  // console.log("req.body: ",req.body)
  let user = null;
  user = await User.findByIdAndUpdate(
    req.body._id,
    { active: req.body.active },
    { new: true }
  );
  if (user !== null) {
    res.status(200).json({
      status: "success",
      data: user,
    });
  }
});

exports.createUser = (req, res) => {
  res.status(500).json({
    status: "error",
    message: "This route is not defined! Please use /signup instead",
  });
};

exports.getAllUsers = catchAsync(async (req, res, next) => {
  const users = await User.find({}, {}, {});
  let doc = users;

  res.status(200).json({
    status: "success",
    data: {
      data: doc,
    },
  });
});

exports.getAllBankAccounts = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 20 ? req.body.resultsPerPage : 20;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;
  let users = await User.find(
    { _id: { $ne: req.params.id }, active: { $ne: false } },
    { bio: 0, photoCover: 0 }
  )
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ _id: -1 });

  // const users = await User.find({},{},{});

  // for(let user of users){
  //   const following = await Following
  //   .find({follower:user._doc._id},{},{})
  //   const followers = await Following
  //   .find({user:user._doc._id},{},{})
  //   const posts = await Post.find({user:user._doc._id},{},{});
  //   let totalItems = 0
  //   let totalLikes = 0
  //   let totalComments = 0
  //   let totalEarned = 0
  //   for (let i = 0; i < posts.length; i++) {

  //     const likesCount =  await Like.countDocuments({post:posts[i]._doc._id})
  //     const commentCount = await Comment.countDocuments({post:posts[i]._doc._id})
  //     const gifts = await Gift.find({post:posts[i]._doc._id},{},{});

  //     for (let j = 0; j < gifts.length; j++) {
  //       totalEarned = totalEarned + gifts[j].cost
  //     }

  //     totalComments = totalComments + commentCount
  //     totalLikes = totalLikes + likesCount
  //     totalItems = totalItems + 1
  //   }
  //   let data ={
  //     followersCount: followers.length,
  //     followingCount: following.length,
  //     commentsCount: totalComments,
  //     likesCount: totalLikes,
  //     postCount : totalItems,
  //     earnCount : totalEarned,
  //   }

  //   let userUpdated = await User.findByIdAndUpdate(user._doc._id, data, {new: true});

  //   console.log("userUpdated: ",userUpdated)
  // }
  // const receiverFiberBank = await FiberBankTransaction.aggregate([
  //   { $match: { isReceiverFiberBank : {'$ne': false } } },
  //   { $group: { _id: null, total: { $sum: "$total" } } }
  // ])
  // const senderFiberBank = await FiberBankTransaction.aggregate([
  //   { $match: { isSenderFiberBank : {'$ne': false } } },
  //   { $group: { _id: null, total: { $sum: "$total" } } }
  // ])
  // let receiver = 0
  // if(receiverFiberBank.length){
  //   receiver = receiverFiberBank[0].total
  // }
  // let sender = 0
  // if(senderFiberBank.length){
  //   sender = senderFiberBank[0].total
  // }

  // fiberBankAccount = receiver - sender

  // for(let user of users){
  //   let income = await Transaction.aggregate([
  //     { $match: { receiverId: user._doc._id } },
  //     { $group: { _id: null, total: { $sum: "$total" } } }
  //   ])
  //   let fiberFee = await Transaction.aggregate([
  //     { $match: { receiverId: user._doc._id , isSellingGiftTransaction : {'$ne': false } } },
  //   ])
  //   let total = 0
  //   for(let fee of fiberFee){
  //     total  = await fee.total - (fee.total * (fee.fiberFee/100));
  //   }
  //   user._doc["fiberFee"] = total

  //   if(income.length){
  //     user._doc["income"] = income[0].total - total
  //   }else{
  //     user._doc["income"] = 0
  //   }
  //   let myGift = await MyGiftModel.aggregate([
  //     { $match: { user : user._doc._id } },
  //     { $group: { _id: null, cost: { $sum: "$cost" } } }
  //   ])

  //   if(myGift.length){
  //     user._doc["myGiftCost"] = myGift[0].cost
  //   }else{
  //     user._doc["myGiftCost"] = 0
  //   }

  //   let outcome = await Transaction.aggregate([
  //     { $match: { senderId: user._doc._id , isSellingGiftTransaction : {'$ne': true }} },
  //     { $group: { _id: null, total: { $sum: "$total" } } }
  //   ])
  //   if(outcome.length){
  //     user._doc["outcome"] = outcome[0].total
  //   }else{
  //     user._doc["outcome"] = 0
  //   }

  //   usersBankAccountsCost = usersBankAccountsCost + user._doc.coins
  //   fiberFeeCollected = fiberFeeCollected + user._doc["fiberFee"]
  //   walletGiftsCost = walletGiftsCost + user._doc["myGiftCost"]
  // }

  // let doc =  users

  // res.status(200).json({
  //   status: 'success',
  //   data: {
  //     data: doc,
  //     usersBankAccountsCost: usersBankAccountsCost,
  //     fiberFeeCollected: fiberFeeCollected,
  //     fiberBankAccount : fiberBankAccount,
  //     walletGiftsCost: walletGiftsCost
  //   }
  // });

  res.status(200).json({
    status: "success",
    data: {
      data: users,
      usersBankAccountsCost: 0,
      fiberFeeCollected: 0,
      fiberBankAccount: 0,
      walletGiftsCost: 0,
    },
  });
});

exports.getUserFiltered = catchAsync(async (req, res, next) => {
  let filter = {};

  if (req.body.followersCountActive) {
    filter["followersCount"] = {};

    if (req.body.followersCountMin !== undefined) {
      filter.followersCount["$gte"] = Number(req.body.followersCountMin);
    }
    if (req.body.followersCountMax !== undefined) {
      filter.followersCount["$lte"] = Number(req.body.followersCountMax);
    }

    if (
      req.body.followersCountMin === undefined &&
      req.body.followersCountMax === undefined
    ) {
      delete filter.followersCount;
    }
  } else {
    delete filter.followersCount;
  }

  if (req.body.followingCountActive) {
    filter["followingCount"] = {};
    if (req.body.followingCountMin !== undefined) {
      filter.followingCount["$gte"] = Number(req.body.followingCountMin);
    }
    if (req.body.followingCountMax !== undefined) {
      filter.followingCount["$lte"] = Number(req.body.followingCountMax);
    }

    if (
      req.body.followingCountMin === undefined &&
      req.body.followingCountMax === undefined
    ) {
      delete filter.followingCount;
    }
  } else {
    delete filter.followingCount;
  }

  if (req.body.postCountActive) {
    filter["postCount"] = {};

    if (req.body.postCountMin !== undefined) {
      filter.postCount["$gte"] = Number(req.body.postCountMin);
    }
    if (req.body.postCountMax !== undefined) {
      filter.postCount["$lte"] = Number(req.body.postCountMax);
    }

    if (
      req.body.postCountMin === undefined &&
      req.body.postCountMax === undefined
    ) {
      delete filter.postCount;
    }
  } else {
    delete filter.postCount;
  }

  if (req.body.earnCountActive) {
    filter["earnCount"] = {};
    if (req.body.earnCountMin !== undefined) {
      filter.earnCount["$gte"] = Number(req.body.earnCountMin);
    }
    if (req.body.earnCountMax !== undefined) {
      filter.earnCount["$lte"] = Number(req.body.earnCountMax);
    }

    if (
      req.body.earnCountMin === undefined &&
      req.body.earnCountMax === undefined
    ) {
      delete filter.earnCount;
    }
  } else {
    delete filter.earnCount;
  }

  let totalUsersWithFilter = 0;
  function isEmpty(obj) {
    return Object.keys(obj).length === 0;
  }
  let data = [];
  if (!isEmpty(filter)) {
    data = await User.find(filter, {
      active: 0,
      isOnline: 0,
      role: 0,
      bio: 0,
      name: 0,
      photoCover: 0,
      surname: 0,
      email: 0,
      photoProfile: 0,
    });
    totalUsersWithFilter = await User.countDocuments(filter);

    console.log("totalUsersWithFilter: ", totalUsersWithFilter);
  } else {
  }

  res.status(200).json({
    status: "success",
    data: {
      data: data,
      totalUsersWithFilter,
    },
  });
});

exports.getUserFilteredData = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 10 ? req.body.resultsPerPage : 10;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;
  let filter = {};

  if (req.body.followersCountActive) {
    filter["followersCount"] = {};

    if (req.body.followersCountMin !== undefined) {
      filter.followersCount["$gte"] = Number(req.body.followersCountMin);
    }
    if (req.body.followersCountMax !== undefined) {
      filter.followersCount["$lte"] = Number(req.body.followersCountMax);
    }

    if (
      req.body.followersCountMin === undefined &&
      req.body.followersCountMax === undefined
    ) {
      delete filter.followersCount;
    }
  } else {
    delete filter.followersCount;
  }

  if (req.body.followingCountActive) {
    filter["followingCount"] = {};
    if (req.body.followingCountMin !== undefined) {
      filter.followingCount["$gte"] = Number(req.body.followingCountMin);
    }
    if (req.body.followingCountMax !== undefined) {
      filter.followingCount["$lte"] = Number(req.body.followingCountMax);
    }

    if (
      req.body.followingCountMin === undefined &&
      req.body.followingCountMax === undefined
    ) {
      delete filter.followingCount;
    }
  } else {
    delete filter.followingCount;
  }

  if (req.body.postCountActive) {
    filter["postCount"] = {};

    if (req.body.postCountMin !== undefined) {
      filter.postCount["$gte"] = Number(req.body.postCountMin);
    }
    if (req.body.postCountMax !== undefined) {
      filter.postCount["$lte"] = Number(req.body.postCountMax);
    }

    if (
      req.body.postCountMin === undefined &&
      req.body.postCountMax === undefined
    ) {
      delete filter.postCount;
    }
  } else {
    delete filter.postCount;
  }

  if (req.body.earnCountActive) {
    filter["earnCount"] = {};
    if (req.body.earnCountMin !== undefined) {
      filter.earnCount["$gte"] = Number(req.body.earnCountMin);
    }
    if (req.body.earnCountMax !== undefined) {
      filter.earnCount["$lte"] = Number(req.body.earnCountMax);
    }

    if (
      req.body.earnCountMin === undefined &&
      req.body.earnCountMax === undefined
    ) {
      delete filter.earnCount;
    }
  } else {
    delete filter.earnCount;
  }

  let totalUsersWithFilter = 0;
  let totalUsersCount = 0;
  function isEmpty(obj) {
    return Object.keys(obj).length === 0;
  }

  if (!isEmpty(filter)) {
    totalUsersWithFilter = await User.find(filter, {
      bio: 0,
      photoCover: 0,
      photoProfile: 0,
    })
      .limit(resultsPerPage)
      .skip(resultsPerPage * page)
      .sort({ _id: -1 });

    totalUsersCount = await User.countDocuments(filter);
  } else {
  }

  res.status(200).json({
    status: "success",
    data: {
      data: [],
      totalUsersWithFilter,
      totalUsersCount: totalUsersCount,
    },
  });
});

exports.searchWithFilters = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 10 ? req.body.resultsPerPage : 10;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;

  let name = req.body.searchText.split(" ")[0];
  let surname =
    req.body.searchText.split(" ")[1] === undefined
      ? name
      : req.body.searchText.split(" ")[1];

  let nameRegex = name.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");
  let surnameRegex = surname.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");

  nameRegex = new RegExp(nameRegex, "i");
  surnameRegex = new RegExp(surnameRegex, "i");

  let filter = {
    $or: [
      { name: { $regex: nameRegex } },
      { surname: { $regex: surnameRegex } },
    ],
  };

  if (req.body.filters.followersCountActive) {
    filter["followersCount"] = {};

    if (req.body.filters.followersCountMin !== undefined) {
      filter.followersCount["$gte"] = Number(
        req.body.filters.followersCountMin
      );
    }
    if (req.body.filters.followersCountMax !== undefined) {
      filter.followersCount["$lte"] = Number(
        req.body.filters.followersCountMax
      );
    }

    if (
      req.body.filters.followersCountMin === undefined &&
      req.body.filters.followersCountMax === undefined
    ) {
      delete filter.followersCount;
    }
  } else {
    delete filter.followersCount;
  }

  if (req.body.filters.followingCountActive) {
    filter["followingCount"] = {};
    if (req.body.filters.followingCountMin !== undefined) {
      filter.followingCount["$gte"] = Number(
        req.body.filters.followingCountMin
      );
    }
    if (req.body.followingCountMax !== undefined) {
      filter.followingCount["$lte"] = Number(
        req.body.filters.followingCountMax
      );
    }

    if (
      req.body.filters.followingCountMin === undefined &&
      req.body.filters.followingCountMax === undefined
    ) {
      delete filter.followingCount;
    }
  } else {
    delete filter.followingCount;
  }

  if (req.body.filters.postCountActive) {
    filter["postCount"] = {};

    if (req.body.filters.postCountMin !== undefined) {
      filter.postCount["$gte"] = Number(req.body.filters.postCountMin);
    }
    if (req.body.postCountMax !== undefined) {
      filter.postCount["$lte"] = Number(req.body.filters.postCountMax);
    }

    if (
      req.body.filters.postCountMin === undefined &&
      req.body.filters.postCountMax === undefined
    ) {
      delete filter.postCount;
    }
  } else {
    delete filter.postCount;
  }

  if (req.body.filters.earnCountActive) {
    filter["earnCount"] = {};
    if (req.body.filters.earnCountMin !== undefined) {
      filter.earnCount["$gte"] = Number(req.body.filters.earnCountMin);
    }
    if (req.body.filters.earnCountMax !== undefined) {
      filter.earnCount["$lte"] = Number(req.body.filters.earnCountMax);
    }

    if (
      req.body.filters.earnCountMin === undefined &&
      req.body.filters.earnCountMax === undefined
    ) {
      delete filter.earnCount;
    }
  } else {
    delete filter.earnCount;
  }

  let totalUsersWithFilter = 0;
  let totalUsersCount = 0;
  function isEmpty(obj) {
    return Object.keys(obj).length === 0;
  }

  if (!isEmpty(filter)) {
    totalUsersWithFilter = await User.find(filter, {
      bio: 0,
      photoCover: 0,
      photoProfile: 0,
    })
      .limit(resultsPerPage)
      .skip(resultsPerPage * page)
      .sort({ _id: -1 });

    totalUsersCount = await User.countDocuments(filter);
  } else {
  }

  res.status(200).json({
    status: "success",
    data: {
      data: [],
      totalUsersWithFilter,
      totalUsersCount: totalUsersCount,
    },
  });
});

exports.searchUser = catchAsync(async (req, res, next) => {
  if (req.body.searchText !== "") {
    let resultsPerPage =
      req.body.resultsPerPage >= 10 ? req.body.resultsPerPage : 10;
    let page = req.body.page >= 1 ? req.body.page : 1;
    page = page - 1;
    req.body.searchText = req.body.searchText.trim();

    let name = req.body.searchText.split(" ")[0];
    let surname =
      req.body.searchText.split(" ")[1] === undefined
        ? name
        : req.body.searchText.split(" ")[1];

    let nameRegex = name.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");
    let surnameRegex = surname.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");

    nameRegex = new RegExp(nameRegex, "i");
    surnameRegex = new RegExp(surnameRegex, "i");

    User.find(
      {
        $or: [
          { name: { $regex: nameRegex } },
          { surname: { $regex: surnameRegex } },
        ],
        _id: { $ne: req.body.userId },
      },
      {
        active: 0,
        isOnline: 0,
        role: 0,
        bio: 0,
        photoCover: 0,
        photoProfile: 0,
      }
    )
      .limit(resultsPerPage)
      .skip(resultsPerPage * page)
      .sort({ _id: -1 })
      .then(async (result) => {
        res.status(200).json({
          status: "success",
          resultSearch: result,
        });
      });
  } else {
    res.status(200).json({
      status: "success",
      resultSearch: result,
    });
  }
});

exports.getUserUntrusted = catchAsync(async (req, res, next) => {
  let data = [];
  let totalUsersWithFilter = 0;
  let resultsPerPage =
    req.body.resultsPerPage >= 10 ? req.body.resultsPerPage : 10;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;

  data = await User.find(
    { untrusted: true },
    { bio: 0, photoCover: 0, photoProfile: 0 }
  )
    .limit(resultsPerPage)
    .skip(resultsPerPage * page);
  totalUsersWithFilter = await User.countDocuments({ untrusted: true });

  res.status(200).json({
    status: "success",
    data: {
      data: data,
      totalUsersWithFilter: totalUsersWithFilter,
    },
  });
});

exports.getUserVerified = catchAsync(async (req, res, next) => {
  let data = [];
  let totalUsersWithFilter = 0;
  let resultsPerPage =
    req.body.resultsPerPage >= 10 ? req.body.resultsPerPage : 10;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;

  data = await User.find(
    { isVerified: true },
    { bio: 0, photoCover: 0, photoProfile: 0 }
  )
    .limit(resultsPerPage)
    .skip(resultsPerPage * page);
  totalUsersWithFilter = await User.countDocuments({ isVerified: true });

  res.status(200).json({
    status: "success",
    data: {
      data: data,
      totalUsersWithFilter: totalUsersWithFilter,
    },
  });
});

exports.getAll = catchAsync(async (req, res, next) => {
  let resultsPerPage = req.body.resultsPerPage;
  let page = req.body.page;
  page = page - 1;

  let filter = {};
  if (req.body.status !== "All") {
    if (req.body.status === "untrusted") {
      filter.untrusted = true;
    }
    if (req.body.status === "isVerified") {
      filter.isVerified = true;
    }
    if (req.body.status === "deleted") {
      filter.active = false;
    }
  }

  let data = await User.find(filter, { photoCover: 0, photoProfile: 0 })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ _id: -1 });

  let countList = await User.find(filter, { photoProfile: 0, photoCover: 0 });
  let count = countList.length;

  res.status(200).json({
    status: "success",
    data: {
      data: data,
      count: count,
    },
  });
});

exports.search = catchAsync(async (req, res, next) => {
  if (req.body.searchText !== "") {
    let resultsPerPage = req.body.resultsPerPage;
    let page = req.body.page;
    page = page - 1;
    req.body.searchText = req.body.searchText.trim();

    let filter = {};

    let name = req.body.searchText.split(" ")[0];
    let surname =
      req.body.searchText.split(" ")[1] === undefined
        ? name
        : req.body.searchText.split(" ")[1];

    let nameRegex = name.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");
    let surnameRegex = surname.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");

    nameRegex = new RegExp(nameRegex, "i");
    surnameRegex = new RegExp(surnameRegex, "i");

    filter = {
      $or: [
        { name: { $regex: nameRegex } },
        { surname: { $regex: surnameRegex } },
      ],
    };

    if (req.body.status !== "All") {
      if (req.body.status === "untrusted") {
        filter.untrusted = true;
      }
      if (req.body.status === "isVerified") {
        filter.isVerified = true;
      }
      if (req.body.status === "deleted") {
        filter.active = false;
      }
    }

    User.find(filter, { photoProfile: 0, photoCover: 0 })
      .limit(resultsPerPage)
      .skip(resultsPerPage * page)
      .sort({ _id: -1 })
      .then(async (result) => {
        // console.log("result: ",result)
        let countList = await User.find(filter, {
          photoProfile: 0,
          photoCover: 0,
        });
        let count = countList.length;
        res.status(200).json({
          status: "success",
          data: {
            data: result,
            count: count,
          },
        });
      });
  } else {
    res.status(200).json({
      status: "success",
      resultSearch: result,
    });
  }
});

exports.getUser = factory.getOne(User);

exports.findByIdAndUpdate = catchAsync(async (req, res, next) => {


  let user = null;
  user = await User.findByIdAndUpdate(req.body.userId, req.body.updateData, {
    new: true,
    photoCover: 0,
    photoProfile: 0,
  });
  if (user !== null) {
    res.status(200).json({
      status: "success",
      data: user,
    });
  }
});

exports.getProfileBalance = catchAsync(async (req, res, next) => {
  let myBalance = await User.find(
    {
      _id: req.params.id,
      active: true,
    },
    {
      active: 0,
      isOnline: 0,
      role: 0,
      bio: 0,
      name: 0,
      photoCover: 0,
      surname: 0,
      email: 0,
      photoProfile: 0,
    }
  );
  let coins = myBalance[0].coins;
  res.status(200).json({
    status: "success",
    data: {
      myBalance: coins,
    },
  });
});

// Do NOT update passwords with this!
exports.updateUser = factory.updateOne(User);
exports.deleteUser = factory.deleteOne(User);
