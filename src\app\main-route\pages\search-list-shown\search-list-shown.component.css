.bloggers-section {
  display: grid;
  grid-template-columns: 3fr 336px;
  padding: 32px !important;
  max-height: 1000px;
}
@media screen and (max-width: 893px) {
  .bloggers-section {
    grid-template-columns: 1fr !important;

    grid-template-rows: 1fr 1fr !important;
    overflow: scroll;
  }
  .searchModalAtm {
    transform: translateY(-42px);
  }
}
.dark-modal .modal-content {
  background-color: #292b2c !important;
  color: white;
}
.dark-modal .close {
  color: white;
}
.light-blue-backdrop {
  background-color: #5cb3fd;
}
.optionStyle {
  font-size: 16px;
  text-align: center;
  width: 100%;
  background-color: var(--app-bg);
  line-height: 2;
  border: none;
  color: white;
  font-weight: 600;
  height: 40px;
}
p,
span,
div {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
