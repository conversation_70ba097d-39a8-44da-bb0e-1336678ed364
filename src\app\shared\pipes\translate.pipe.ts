import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'translate',
  pure: true
})
export class TranslatePipe implements PipeTransform {
  delay = ms => new Promise(res => setTimeout(res, ms));
  respond = ' '
  
   transform(value: any, lang: any): any {
    this.respond = ' '
    if(value){
      if((value.find(e => e.lang === lang))?.value){
        this.respond =  (value.find(e => e.lang === lang)).value
      }

      return this.respond
    }else{
      return ' '
    }

  }


}
