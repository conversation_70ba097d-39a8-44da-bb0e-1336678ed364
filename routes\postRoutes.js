const express = require('express');
const postController = require('./../controllers/postController');

const router = express.Router();
var multipart = require('connect-multiparty');

var multipartMiddleware = multipart();

// Protect all routes after this middleware
// router.use(authController.protect);

router
.route('/getAllBloggers')
.post(postController.getAllBloggers)



router
.route('/getVideo/:id')
.get(postController.getVideo)

router
.route('/getAll/:id')
.get(postController.getAll)


router
.route('/getAllPostForUserId/:id')
.post(postController.getAllPostForUserId)

router
.route('/getHomePost/:id')
.post(postController.getHomePost)

router
.route('/getHomePostV2/:id')
.post(postController.getHomePostV2)

router
.route('/getOnePost/:id')
.post(postController.getOnePost)

router
.route('/getAllVideosPosts')
.post(postController.getAllVideosPosts)

router
.route('/suggestedPosts/:id')
.post(postController.suggestedPosts)

router
.route('/getAllCreatedPosts/:id')
.post(postController.getAllCreatedPosts)

router
.route('/getAllCreatedPostsV2/:id')
.post(postController.getAllCreatedPostsV2)



router
.route('/getAllPromotionPosts/:id')
.post(postController.getAllPromotionPosts)


router
.route('/getLikesPost/:id')
.post(postController.getLikesPost)

router
.route('/getCommentsPost/:id')
.post(postController.getCommentsPost)

router
.route('/getGiftsPost/:id')
.post(postController.getGiftsPost)




router
.route('/createEditedPost')
.post(
  postController.createEditedPost
);

router
.route('/findPostByIdAndUpdate/:id')
.post(
  postController.findPostByIdAndUpdate
);


router
.route('/uploadPostVideo')
.post(
  multipartMiddleware,
  postController.uploadPostVideo
);





router
.route('/getAllDeletedPosts/:id')
.post(postController.getAllDeletedPosts)

router
.route('/deleteMyPost/:id')
.patch(postController.deleteMyPost)

router
.route('/recoverMyPost/:id')
.patch(postController.recoverMyPost)


router
.route('/getVideoFirstImage')
.post(postController.getVideoFirstImage)

router
.route('/getVideoFirstBlurImage')
.post(postController.getVideoFirstBlurImage)

router
.route('/getPostImages')
.post(postController.getPostImages)

router
.route('/getPostBlurImages')
.post(postController.getPostBlurImages)

router
.route('/getProfileImage')
.post(postController.getProfileImage)

router
.route('/getCoverImage/:id')
.get(postController.getImage)

router
.route('/getPhotoCoverImage')
.post(postController.getPhotoCoverImage)


router
.route('/getImage/:id')
.get(postController.getImage)

router
.route('/getPromotionImage')
.post(
  postController.getPromotionImage)

  router
  .route('/:id')
  .post(postController.getPostData)
  
  
  router
.route('/')
.post(
  multipartMiddleware,
  postController.resizePhoto,
  postController.resizePhotoBlur,
  postController.createPost
);

// router.use(authController.restrictTo('admin'));

module.exports = router;