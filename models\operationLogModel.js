const mongoose = require('mongoose');

const operationLogSchema = new mongoose.Schema({
    comment: {
        type: String,
        trim: true
   },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: [true, 'must been created from a User!']
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

operationLogSchema.pre('save', async function( next) {

    // await this.populate({
    //   path: 'post',
    // })
    
    await this.populate({
        path: 'user',
      }).execPopulate();;

    next();
  });
  
  
  
  operationLogSchema.pre(/^find/, function(next) {
    // this.populate({
    //     path: 'post',
    // });
    this.populate({
      path: 'user',
    });
    next();
  })

const OperationLog = mongoose.model('OperationLog', operationLogSchema);

module.exports = OperationLog;
