const mongoose = require('mongoose');

const postGiftsSchema = new mongoose.Schema(
  {
    gift: Number,
    title : {
      type: String,
      trim: true
    },
    cost: {
      type: Number,
      default: 0
    },
    src: {
      type: String,
      trim: true
    },
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Gift must been created from a User!']
    },
    isPrivate:{
      type: Boolean,
      default: false,
    },
    post: {
        type: mongoose.Schema.ObjectId,
        ref: 'Post',
        required: [true, 'Gift must have Post!']
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }

);
postGiftsSchema.pre('save', async function( next) {

  await this.populate({
    path: 'user',
  }).execPopulate();;

  next();
});

postGiftsSchema.pre(/^find/, function(next) {
  this.populate({
    path: 'user',
  });
  next();
})

const PostGifts = mongoose.model('PostGifts', postGiftsSchema);

module.exports = PostGifts;
