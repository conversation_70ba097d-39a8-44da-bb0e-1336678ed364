const catchAsync = require('./../utils/catchAsync');
const CentralBank = require('./../models/centralBankModel');
const Transaction = require('./../models/transactionModel');
const MainData = require('./../models/mainDataModel');
const User = require('./../models/userModel');

exports.create = catchAsync(async (req, res, next) => {
let transaction =  null
let centralBank =  null
let mainData =  null

let centralBankTotalAmount =  0
let theMinisterOfFinanceTotalaAmount =  0

if(req.body.transaction){
  transaction = await Transaction.create(req.body.transaction);
}
if(req.body.centralBank){
      mainData = await MainData.find({variableName: ['central-bank-total-amount','the-minister-of-finance-total-amount']})
      for(let data of mainData ){
          if(data.variableName == 'the-minister-of-finance-total-amount'){
            if(req.body.centralBank.type == 'tax-collection'){
              theMinisterOfFinanceTotalaAmount = data.value
              theMinisterOfFinanceTotalaAmount = theMinisterOfFinanceTotalaAmount - req.body.centralBank.total
              if(transaction){
                await MainData.findByIdAndUpdate(data._id ,{value: theMinisterOfFinanceTotalaAmount})
              }
            }
          }
          if(data.variableName == 'central-bank-total-amount'){
            if(req.body.centralBank.type == 'loan-to-central-bank'){
              centralBankTotalAmount = data.value
              centralBankTotalAmount = centralBankTotalAmount + req.body.centralBank.creditsAmount
              if(transaction){
                await MainData.findByIdAndUpdate(data._id ,{value: centralBankTotalAmount})
              }
            }
            if(req.body.centralBank.type == 'selling-credits'){
              centralBankTotalAmount = data.value
              centralBankTotalAmount = centralBankTotalAmount - req.body.centralBank.creditsAmount
              if(transaction){
                await MainData.findByIdAndUpdate(data._id ,{value: centralBankTotalAmount})
              }
              let userPostUpdatedData = {
                coins:0,
                earnCount: 0
              }
              if(req.body.centralBank.userSelected._id){
                userPostUpdatedData.coins = req.body.centralBank.userSelected.coins + req.body.centralBank.creditsAmount
                userPostUpdatedData.earnCount = req.body.centralBank.userSelected.earnCount + req.body.centralBank.creditsAmount
                let userUpdated = await User.findByIdAndUpdate(req.body.centralBank.userSelected._id, userPostUpdatedData, {new: true});
              }
            }
          }
      }
      req.body.centralBank['transaction']= transaction._id
      centralBank = await CentralBank.create(req.body.centralBank)
      // .populate({
      //   path: 'createdBy',
      //   select: ['id','_id','name','email','coins','followersCount','followingCount',
      //   'postCount','commentsCount','likesCount','earnCount',
      //   'surname','isVerified','photoColor','untrusted']
      // })
}

    res.status(201).json({
    status: 'success',
    centralBank:centralBank
    });

});


exports.getAll = catchAsync(async (req, res, next) => {
    let resultsPerPage =  req.body.resultsPerPage 
    let page = req.body.page 
    page = page - 1
    
    let data =
     await CentralBank.find()
     .populate({
      path: 'createdBy',
      select: ['id','_id','name','email','coins','followersCount','followingCount',
      'postCount','commentsCount','likesCount','earnCount',
      'surname','isVerified','photoColor','untrusted']
    })
    .limit( resultsPerPage)
    .skip( resultsPerPage * page)
    .sort({ _id: -1 })
    
    let count = await CentralBank.countDocuments()

    res.status(200).json({
      status: 'success',
      data: {
        data: data,
        count: count
      }
    });
  
});



exports.search = catchAsync(async (req, res, next) => {
  if(req.body.searchText !== ''){
      let resultsPerPage =   req.body.resultsPerPage 
      let page = req.body.page
      page = page - 1
      req.body.searchText = req.body.searchText.trim()

      let name = req.body.searchText.split(" ")[0]; 
      let surname = req.body.searchText.split(" ")[1] === undefined ? name : req.body.searchText.split(" ")[1]; 

      let nameRegex = name.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
      let surnameRegex = surname.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
      
      nameRegex = new RegExp(nameRegex, 'i') 
      surnameRegex = new RegExp(surnameRegex, 'i') 

      let filter = {
        $or: [
          { 'createdBy.name':  { $regex: nameRegex } },
          { 'createdBy.surname':  { $regex: surnameRegex } },
        ]
      }

      CentralBank.find(filter)
      .populate({
        path: 'createdBy',
        select: ['id','_id','name','email','coins','followersCount','followingCount',
        'postCount','commentsCount','likesCount','earnCount',
        'surname','isVerified','photoColor','untrusted']
      })
      .limit(resultsPerPage)
      .skip(resultsPerPage * page)
      .sort({ _id: -1 })
      .then(  async result => {
        // console.log("result: ",result)
        let countList = await CentralBank.find(filter,{createdBy:0})
        let count = countList.length
        res.status(200).json({
          status: 'success',
          data: {
            data: result,
            count: count
          }
        });
      })

    }else{
      res.status(200).json({
        status: 'success',
        resultSearch: result 
      });
    }
});