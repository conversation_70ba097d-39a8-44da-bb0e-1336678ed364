.input-image{
    visibility:hidden;
  }
  .projects-add-version {
    /* padding: 32px !important; */
    display: grid;
    grid-template-columns: 1fr 1fr !important;
  }
  .input-image-add-version {
    display: flex;
    flex-direction: column;
    width: 100%;
    justify-content: center;
    align-items: center;
    border-radius: 13px;
    border: 4px dotted var(--sidebar);
    gap: 5px;
    padding: 1em;
  }
  
  .input-add-version {
    display: flex;
    width: 100%;
    flex-direction: column;
    gap: 4px;
  }

  .input-add-version input {
    width: 100%;
    height: 100%;
    min-height: 40px;
    border: none;
    background-color: var(--sidebar);
    border-radius: 15px;
    font-family: var(--body-font);
    font-size: 14px;
    font-weight: 500;
    padding: 0 16px 0 16px;
    box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
    text-align: center;
    color: #fff;
  }
  .input-add-version-TX {
    display: flex;
    width: 100%;
    flex-direction: column;
    gap: 4px;
  }

  .input-add-version-TX textarea {
    width: 100%;
    height: 100%;
    min-height: 90px;
    border: none;
    background-color: var(--sidebar);
    border-radius: 15px;
    font-family: var(--body-font);
    font-size: 14px;
    font-weight: 500;
    padding: 0 16px 0 16px;
    box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
    text-align:start;
    color: #fff;
  }

  .add-version-input {
    width: 100%;
    height: 100%;
    min-height: 40px;
    border: none;
    background-color: var(--sidebar);
    border-radius: 15px;
    font-family: var(--body-font);
    font-size: 14px;
    font-weight: 500;
    padding: 0 16px 0 16px;
    box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
    color: #fff;
  }
  .promotion-sent-image {
    width: 50%;
    border-radius: 15px;
    object-fit: contain;
  }
  
  .promotion-sent-content-image {
    height: 40px;
    width: 40px;
    border-radius: 10px;
  }
  
  .promotion-sent-image-preview {
    height: 100%;
    width: 100%;
    border-radius: 10px;
    position: absolute;
    object-fit: cover;
  }
  
  .promotion-sent-image-preview-home {
    height: 70%;
    width: 100%;
    border-radius: 10px 10px 0 0;
    position: absolute;
    object-fit: cover;
  }
  ::ng-deep .mat-step-header .mat-step-icon-selected {
    background-color: var(--action-color);
  }
  ::ng-deep .mat-stepper-vertical {
    background-color: var(--projects-section);
    border: none;
    overflow: auto;
    border-radius: 20px;
  }
  mat-slider {
    width: 300px;
  }
  ::ng-deep .custom-slider .ngx-slider .ngx-slider-tick {
    border-radius: 0;
    background: #d1fff7;
  }
  ::ng-deep .custom-slider .ngx-slider .ngx-slider-selection {
    background: var(--action-color);
  }
  ::ng-deep .custom-slider .ngx-slider .ngx-slider-tick.ngx-slider-selected {
    background: var(--action-color);
  }
  ::ng-deep .mat-stepper-vertical-line::before {
    content: "";
    position: absolute;
    left: 0;
    border-left-width: 2px;
    border-left-style: solid;
    border-left-color: var(--action-color-50);
  }
  ::ng-deep .mat-step-header .mat-step-label.mat-step-label-active{
    color: white;
  }
  
  button {
    background-color: var(--action-color-50);
    border: 1px solid var(--action-color);
    color: white;
    border-radius: 10px;
    height: 30px;
    width: 80px;
  }
  .buttonReset {
    background-color: var(--action-color);
    color: white;
    border-radius: 10px;
    height: 30px;
    width: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
  }
  .test {
    font-size: 30px;
  }
  ::ng-deep .mat-step-header .mat-step-label.mat-step-label-active{
    color: white;
  }
  @media screen and (max-width: 980px) {
    p,
    span,
    div {
      color: white;
      font-size: 14px;
      font-weight: 600;
    }
  }
  
  @media screen and (max-width: 520px) {
    p,
    span,
    div {
      color: white;
      font-size: 13px;
      font-weight: 600;
    }
  }
  
  @media screen and (max-height: 980px) {
    :root {
      --font-size-xxxl: 35px;
      --font-size-xxl: 25px;
      --font-size-xl: 25px;
      --font-size-l: 20px;
      --font-size-m: 15px;
      --font-size-s: 10px;
      --font-size-xs: 5px;
    }
    p,
    span,
    div {
      color: white;
      font-size: 13px;
      font-weight: 600;
    }
  }.control {
    display: block;
    position: relative;
    padding-left: 30px;
    margin-bottom: 15px;
    cursor: pointer;
    font-size: 18px;
  }
  .control input {
    position: absolute;
    z-index: -1;
    opacity: 0;
  }
  .control__indicator {
    position: absolute;
    top: 0px;
    left: 0;
    height: 20px;
    width: 20px;
    background: var(--action-color-30);
    border-radius: 5px;
  }
  .control--radio .control__indicator {
    border-radius: 50%;
  }
  /* .control:hover input ~ .control__indicator,
    .control input:focus ~ .control__indicator {
      background: var(--action-color-50);
    } */
  .control input:checked ~ .control__indicator {
    background: var(--action-color);
    border-radius: 5px;
  }
  /* .control:hover input:not([disabled]):checked ~ .control__indicator,
    .control input:checked:focus ~ .control__indicator {
      background:var(--action-color-50);
    } */
  .control input:disabled ~ .control__indicator {
    background: #e6e6e6;
    opacity: 0.6;
    pointer-events: none;
    border-radius: 5px;
  }
  .control__indicator:after {
    content: "";
    position: absolute;
    display: none;
  }
  .control input:checked ~ .control__indicator:after {
    display: block;
  }
  .control--checkbox .control__indicator:after {
      left: 8px;
      top: 4px;
      width: 4px;
      height: 11px;
      border: 2px solid #fff;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
      border-radius: 5px;
  }
  .control--checkbox input:disabled ~ .control__indicator:after {
    border-color: #7b7b7b;
  }
  .control--radio .control__indicator:after {
    left: 7px;
    top: 7px;
    height: 6px;
    width: 6px;
    border-radius: 50%;
    background: #fff;
  }
  .control--radio input:disabled ~ .control__indicator:after {
    background: #7b7b7b;
  }