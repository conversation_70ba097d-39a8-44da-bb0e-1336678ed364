const mongoose = require('mongoose');

const permissionSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      trim: true
    },
    category: {
        type: mongoose.Schema.ObjectId,
        ref: 'PermissionCategory',
        required: [true, 'Permission must have a category!']
    },
    createdBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Permission must been created from a User!']
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }

);

permissionSchema.pre('save', async function( next) {
  next();
});

permissionSchema.pre(/^find/, function(next) {
  next();
})

const Permission = mongoose.model('Permission', permissionSchema);

module.exports = Permission;
