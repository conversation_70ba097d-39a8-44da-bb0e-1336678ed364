const catchAsync = require('./../utils/catchAsync');

const PermissionCategory = require('./../models/permissionCategoryModel');


exports.create = catchAsync(async (req, res, next) => {

    console.log("req.body: ",req.body)
      let data = await PermissionCategory.create(req.body);   
      if(data !== null){
        res.status(201).json({
          status: 'success',
          data: data
         });
      }
});


exports.getAll = catchAsync(async (req, res, next) => {
    let updates = 
    await  PermissionCategory.find({})    
    .sort({ _id: 1 })

  
    res.status(200).json({
      status: 'success',
      data: {
        data: updates
      }
    });
  
  });