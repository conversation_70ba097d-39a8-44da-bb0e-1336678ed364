const catchAsync = require('./../utils/catchAsync');
const MainData = require('./../models/mainDataModel');

exports.create = catchAsync(async (req, res, next) => {
    
const doc = await MainData.create(req.body);

    res.status(201).json({
    status: 'success',
    data: {
        data: doc
    }
    });

});


exports.getAll = catchAsync(async (req, res, next) => {
    let resultsPerPage =  req.body.resultsPerPage  >= 5 ? req.body.resultsPerPage : 5;
    let page = req.body.page >= 1 ? req.body.page : 1;
    page = page - 1
  
    let mainDatas =
     await MainData.find()
    .limit( resultsPerPage)
    .skip( resultsPerPage * page)
    .sort({ _id: -1 })
    
    let count = await MainData.countDocuments()

    res.status(200).json({
      status: 'success',
      data: {
        data: mainDatas,
        count: count
      }
    });
  
  });


  exports.getByVariableName = catchAsync(async (req, res, next) => {

    let mainData =
     await MainData.find({variableName: req.body.variableName })
    
    res.status(200).json({
      status: 'success',
      data: {
        mainData: mainData
      }
    });
  
  });