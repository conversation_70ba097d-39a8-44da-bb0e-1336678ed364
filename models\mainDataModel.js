const mongoose = require('mongoose');

const mainDataSchema = new mongoose.Schema({
  variableName : {
    type: String,
    trim: true
  },
  value:{
    type: Number,
    default: 0
  },
  createdBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: [true, 'Main Data must been created from a User!']
  },
  active: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

const MainData = mongoose.model('MainData', mainDataSchema);

module.exports = MainData;
