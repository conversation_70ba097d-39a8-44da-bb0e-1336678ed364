<div class="projects-section">
  <div class="tiles">
    <article class="tile">
      <p
        style="
          color: #e3e3e3;
          font-size: 32px;
          font-weight: 600;
          margin: 0px;
          text-align: center;
        "
      >
        {{centralBankTotalAmount}} <span style="font-size: 15px">CREDITS</span>
      </p>
      <div style="display: flex; justify-content: space-evenly">
        <div>
          <div
            style="justify-content: space-between; margin: 0 3em 0 0"
            class="disp-flex a-i-center"
          >
            <span style="color: #e3e3e3; font-size: 30px; font-weight: 700;line-height: 2.4;"
              >0</span
            >
          </div>
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: start;
              gap: 1em;
            "
          >
            <img
              class="upAnimation"
              src="../assets/animatedIcons/inflow_credit.svg"
              alt="arrowUp"
            />
            <div style="display: grid">
              <p
                style="
                  margin: 0;
                  color: #e3e3e3;
                  font-size: 19px;
                  line-height: 1;
                  display: flex;
                  align-items: center;
                  font-weight: 500;
                "
              >
                0
              </p>
              <p
                style="
                  margin: 0;
                  color: #e3e3e3;
                  font-size: 14px;
                  display: flex;
                  font-weight: 500;
                  opacity: 0.8;
                "
              >
                this month
              </p>
            </div>
          </div>
        </div>
        <div>
          <div
            style="justify-content: space-between; margin: 0 3em 0 0"
            class="disp-flex a-i-center"
          >
            <span style="color: #e3e3e3; font-size: 30px; font-weight: 700;line-height: 2.4;"
              >0</span
            >
          </div>
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: start;
              gap: 1em;
            "
          >
            <img
              class="downAnimation"
              src="../assets/animatedIcons/outflow_credit.svg"
              alt="arrowUp"
            />
            <div style="display: grid">
              <p
                style="
                  margin: 0;
                  color: #e3e3e3;
                  font-size: 19px;
                  line-height: 1;
                  display: flex;
                  align-items: center;
                  font-weight: 500;
                "
              >
                0
              </p>
              <p
                style="
                  margin: 0;
                  color: #e3e3e3;
                  font-size: 14px;
                  display: flex;
                  font-weight: 500;
                  opacity: 0.8;
                "
              >
                this month
              </p>
            </div>
          </div>
        </div>
      </div>
    </article>
    <article class="tile">
      <p
        style="
          color: #e3e3e3;
          font-size: 32px;
          font-weight: 600;
          margin: 0px;
          text-align: center;
        "
      >
        0 <span style="font-size: 15px">EUR</span>
      </p>
      <div style="display: flex; justify-content: space-evenly">
        <div>
          <div
            style="justify-content: space-between; margin: 0 3em 0 0"
            class="disp-flex a-i-center"
          >
            <span style="color: #e3e3e3; font-size: 30px; font-weight: 700;line-height: 2.4;"
              >0</span
            >
          </div>
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: start;
              gap: 1em;
            "
          >
            <img
              class="upAnimation"
              src="../assets/animatedIcons/inflow_real.svg"
              alt="arrowUp"
            />
            <div style="display: grid">
              <p
                style="
                  margin: 0;
                  color: #e3e3e3;
                  font-size: 19px;
                  line-height: 1;
                  display: flex;
                  align-items: center;
                  font-weight: 500;
                "
              >
                0
              </p>
              <p
                style="
                  margin: 0;
                  color: #e3e3e3;
                  font-size: 14px;
                  display: flex;
                  font-weight: 500;
                  opacity: 0.8;
                "
              >
                this month
              </p>
            </div>
          </div>
        </div>
        <div>
          <div
            style="justify-content: space-between; margin: 0 3em 0 0"
            class="disp-flex a-i-center"
          >
            <span style="color: #e3e3e3; font-size: 30px; font-weight: 700;line-height: 2.4;"
              >0</span
            >
          </div>
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: start;
              gap: 1em;
            "
          >
            <img
              class="downAnimation"
              src="../assets/animatedIcons/outflow_real.svg"
              alt="arrowUp"
            />
            <div style="display: grid">
              <p
                style="
                  margin: 0;
                  color: #e3e3e3;
                  font-size: 19px;
                  line-height: 1;
                  display: flex;
                  align-items: center;
                  font-weight: 500;
                "
              >
                0
              </p>
              <p
                style="
                  margin: 0;
                  color: #e3e3e3;
                  font-size: 14px;
                  display: flex;
                  font-weight: 500;
                  opacity: 0.8;
                "
              >
                this month
              </p>
            </div>
          </div>
        </div>
      </div>
    </article>
  </div>
  <div
    style="
      /* display: none !important; */
      display: grid;
      grid-template-rows: 4em 2em 1fr 4em;
      background-color: var(--sidebar);
      padding: 5px 1em;
      border-radius: 20px;
      height: 65%;
    "
  >
    <div class="disp-flex a-i-center">
      <span class="no-wrap-1-line">ATM Transactions</span>
      <div class="m-l-auto disp-flex">
        <div class="search-bar">
          <input
          (keyup)="searchUser($event)"
           type="text" placeholder="Search for user..." />
        </div>
        <div>
          <button
            (click)="openOptionsModal(optionsContent)"
            class="add-btn"
            title="Add New Project"
          >
            +
          </button>
        </div>
      </div>
    </div>
    <div
      style="
        display: grid;
        grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.5fr;
        background-color: rgba(26, 37, 59, 0.5);
        align-items: center;
        margin-bottom: 5px;
        border-radius: 10px;
        gap: 1em;
      "
    >
      <span style="margin-left: 1em" class="no-wrap-1-line">Created By</span>
      <span class="no-wrap-1-line">Created At</span>
      <span class="no-wrap-1-line">Real Money</span>
      <span class="no-wrap-1-line">Credits</span>
      <span class="no-wrap-1-line">Type</span>
      <span class="disp-flex j-c-center">
        <!-- Actions -->
      </span>
    </div>
    <ul
      style="padding-inline-start: 0px; margin-bottom: 0rem; overflow-y: scroll"
      class=""
    >
      <ng-container *ngIf="startLoading || loading">
        <li
          *ngFor="let i of [].constructor(15)"
          class="skeleton1"
          style="
            display: grid;
            grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.5fr;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 40%;
                height: 16px;
                margin-left: 13px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 53%;
                height: 16px;
              "
            ></span>
          </div>

          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 53%;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 53%;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 53%;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid"></div>
        </li>
      </ng-container>

      <div
        *ngIf="data?.length === 0 && !startLoading && !loading"
        style="
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
        "
      >
        <img
          style="width: 70%; max-width: 299px"
          src="/assets/icons/animatedIconsTable/banklistempty_infinite.svg"
          alt=""
        />
        <p style="font-size: 16px">List is empty</p>
      </div>

      <li
        *ngFor="let dat of data; let i = index"
        style="
          display: grid;
          grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.5fr;
          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
        "
      >
        <div class="disp-flex gap-05">
          <div style="" class="disp-flex gap-05">
            <ng-container *ngIf="
            !dat?.inflowCredits && 
            !dat?.inflowRealMoney  &&
            !dat?.outflowCredits &&
            !dat?.outflowRealMoney">
              <div class="upAnimation"></div>
              <div class="upAnimation"></div>
            </ng-container>
            
            <img
              *ngIf="dat?.inflowCredits"
              style="box-shadow: none !important"
              class="upAnimation"
              src="../assets/animatedIcons/inflow_credit.svg"
              alt="arrowUp"
            />
            <img
            *ngIf="dat?.inflowRealMoney"
              style="box-shadow: none !important"
              class="upAnimation"
              src="../assets/animatedIcons/inflow_real.svg"
              alt="arrowUp"
            />

            <img
            *ngIf="dat?.outflowCredits"
              style="box-shadow: none !important"
              class="upAnimation"
              src="../assets/animatedIcons/outflow_credit.svg"
              alt="arrowUp"
            />
            <img
            *ngIf="dat?.outflowRealMoney"
              style="box-shadow: none !important"
              class="upAnimation"
              src="../assets/animatedIcons/outflow_real.svg"
              alt="arrowUp"
            />
          </div>
          <div class="disp-grid">
            <span class="no-wrap-1-line"
              >{{ dat?.createdBy?.name }} {{ dat?.createdBy?.surname }}
            </span>
            <span style="opacity: 0.7" class="no-wrap-1-line"
              >{{ dat?.createdBy?.email }}</span
            >
          </div>
        </div>
        <span class="no-wrap-1-line">
          {{ dat?.createdAt | date : "MMM d, y, h:mm a" }}
        </span>
        <div class="disp-grid">
          <span class="no-wrap-1-line"
            >{{ dat?.realMoneyAmount }} {{ dat?.realMoneyCurrency }}</span
          >
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">{{ dat?.creditsAmount }} credits</span>
        </div>
        <span class="status-inProgress no-wrap-1-line">
          {{ dat?.type }}
        </span>
        <div class="disp-flex j-c-center">
          <span
            (click)="
            selectTransaction(dat);
            openStatusChangeModal(statusContent)"
            class="no-wrap-1-line"
            >•••</span
          >
        </div>
      </li>
    </ul>
    <div class="list-number disp-flex" style="">
      <div
        class="showingInfoWrapper"
        style="
          margin: 0 0 0.75rem;
          display: flex !important;
          align-items: center;
          gap: 1em;
        "
      >
        <span class="showingInfo"
          >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
          {{ pageNo * resultsPerPage - (resultsPerPage - data.length) }} of
          {{ count }}</span
        >
        <div style="border: 1px solid gray; border-radius: 15px">
          <div>
            <input style="display: none" id="dropdownInput" type="checkbox" />
            <div
              style="
                display: flex;
                justify-content: center;
                height: 45px;
                width: 50px;
              "
              class="dropdown"
            >
              <select
                style="
                  display: flex;
                  background: transparent;
                  border: none;
                  color: white;
                  font-size: 18px;
                  width: 90%;
                  font-weight: 600;
                "
                (ngModelChange)="resultsPerPageChanged($event)"
                [(ngModel)]="resultsPerPage">
                <option class="optionStyle colorCancel">4</option>
                <option class="optionStyle colorCancel">7</option>
                <option class="optionStyle colorCancel">10</option>
                <option class="optionStyle colorCancel">15</option>
                <option class="optionStyle colorCancel">20</option>
                <option class="optionStyle colorCancel">30</option>
                <option class="optionStyle colorCancel">50</option>
              </select>
              <label for="dropdownInput" class="overlay"></label>
            </div>
          </div>
        </div>
      </div>

      <nav>
        <ul class="pager">
          <li class="pager__item pager__item--prev">
            <a
              *ngIf="pageNo !== 1"
              (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Previous
            </a>
          </li>
          <li *ngIf="pageNo !== 1" class="pager__item">
            <a (click)="setPage(1)" class="pager__link">...</a>
          </li>
          <li
            *ngFor="
              let item of [].constructor(pageNoTotal) | slice : 0 : 5;
              let i = index
            "
            [ngClass]="pageNo + i === pageNo ? 'active' : ''"
            class="pager__item"
          >
            <a
              *ngIf="pageNo + i <= pageNoTotal"
              (click)="setPage(pageNo + i)"
              class="pager__link"
              >{{ pageNo + i }}</a
            >
          </li>
          <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
            <a (click)="setPage(pageNoTotal)" class="pager__link">...</a>
          </li>
          <li
            *ngIf="pageNo !== pageNoTotal"
            class="pager__item pager__item--next"
          >
            <a
              (click)="pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Next
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>

  <ng-template #optionsContent let-modal>
    <div
      style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1"
    >
      <div class="modal-body">
        <!-- header -->
        <div style="display: flex; justify-content: end; width: 100%">
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; margin-left: auto"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->

        <div style="margin: 1em 0">
          <div style="display: flex; flex-direction: column; gap: 2em">
            <!-- Skeletopn Start -->
            <div
              style="
                display: none !important;
                display: grid;
                width: 100%;
                gap: 1em;
              "
            >
              <div
                style="
                  padding: 0.5em 1em !important;
                  height: 64px;
                  align-items: center;
                  gap: 1em;
                "
                class="skeleton1 disp-flex j-c-center a-i-center"
              >
                <div
                  style="height: 18px; width: 179px; border-radius: 3px"
                  class="skeleton2"
                ></div>
              </div>
            </div>
            <!-- Skeletopn End -->
            <div
              style="border: 1px solid gray; border-radius: 15px; height: 50px"
            >
              <label
                for="currency"
                style="
                  margin: -11px 0px 0 14px;
                  font-size: 16px;
                  font-weight: 600;
                  background: var(--app-bg);
                  width: fit-content;
                  padding: 0 13px;
                  display: flex;
                "
              >
                Type
              </label>
              <div>
                <input
                  style="display: none"
                  id="dropdownInput"
                  type="checkbox"
                />
                <div
                  style="display: flex; justify-content: center"
                  class="dropdown"
                >
                  <select
                    style="
                      display: flex;
                      background: transparent;
                      border: none;
                      color: white;
                      font-size: 18px;
                      width: 49%;
                      font-weight: 600;
                    "                
                    (ngModelChange)="selectChanged($event)"
                    [(ngModel)]="optionSelected">
                    <option  style="" class="optionStyle">
                      Loan to Central Bank
                    </option>
                    <option  style="" class="optionStyle">
                      Tax Collection
                    </option>
                    <option  style="" class="optionStyle">
                      Selling Credits
                    </option>
                  </select>
                  <label for="dropdownInput" class="overlay"></label>
                </div>
              </div>
            </div>
            <!-- LOAN TO CENTRAL BANK -->
            <div
            *ngIf="optionSelected == 'Loan to Central Bank' "
              style="display: grid; grid-template-columns: 1fr 1fr; gap: 1em">
              <div>
                <span style="margin-left: 1em">Credits</span>
                <div style="display: flex; align-items: center">
                  <div class="input-bar">
                    <input 
                    [(ngModel)]="creditsAmount"
                    class="" type="number" placeholder="eg. 100" />
                  </div>
                </div>
              </div>
              <div>
                <span style="margin-left: 1em">Real Money</span>
                <div style="display: flex; align-items: center">
                  <div style="align-items: center" class="input-bar">
                    <input 
                    [(ngModel)]="realMoneyAmount"
                    class="" type="number" placeholder="eg. 100" />
                    <div>
                      <input
                        style="display: none"
                        id="dropdownInput"
                        type="checkbox"
                      />
                      <div
                        style="display: flex; justify-content: center"
                        class="dropdown"
                      >
                        <select
                          style="
                            display: flex;
                            background: transparent;
                            border: none;
                            color: white;
                            font-size: 18px;
                            font-weight: 600;
                          "                            
                          (ngModelChange)="realMoneyCurrency = $event"
                          [(ngModel)]="realMoneyCurrency">
                            <option  style="" class="optionStyle">
                              EUR
                            </option>
                            <option  style="" class="optionStyle">
                              ALL
                            </option>
                            <option  style="" class="optionStyle">
                              GBP
                            </option>
                            <option  style="" class="optionStyle">
                              USD
                            </option>
                        </select>
                        <label for="dropdownInput" class="overlay"></label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- TAX COLLACTION -->
            <div
            *ngIf="optionSelected == 'Tax Collection' "
              style="
                display: grid;
                grid-template-columns: 1fr;
                gap: 4px;
              "
            >
              <div
                style="
                  display: flex;
                  justify-content: center;
                  flex-direction: column;
                  align-items: center;
                  gap: 1em;
                "
              >
                <img
                  style="height: 50px; width: 50px"
                  src="assets/iconsTab/minFinanceSel.svg"
                  alt=""
                />
                
                <p style="margin: 0">{{theMinisterOfFinanceTotalaAmount}} credits</p>
                <p style="margin: 0">From Ministry of Finance to Central Bank</p>
              </div>
              <div style="display: flex; align-items: center">
                <div class="input-bar">
                  <input 
                  [(ngModel)]="creditsAmount"
                  class="" type="number" placeholder="eg. 100 credits" />
                </div>
              </div>
            </div>
            <!-- SELLING CREDITS -->
            <div
              *ngIf="optionSelected == 'Selling Credits' "
              style="
                display: grid;
                grid-template-columns: 1fr;
                gap: 1em;
              ">
                <div
                *ngIf="userSelected == null"
                  style="
                    display: flex;
                    justify-content: center;
                    flex-direction: column;
                    align-items: center;
                    gap: 1em;
                  "
                >
                  <span>You have not selected any user...</span>
                  <span
                  (click)="openUserSearchModal(searchContent)"
                    style="
                      border: 1px solid var(--action-color);
                      padding: 5px 2em;
                      border-radius: 10px;
                    ">Select User</span>
                </div>
              <div>
                <div
                *ngIf="userSelected"
                  style="
                    display: flex;
                    gap: 1em;
                    align-items: center;
                    background-color: var(--app-container);
                    padding: 1em;
                    border-radius: 15px;
                  "
                >
                  <div
                    style="
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      position: relative;
                    "
                  >
                  <app-avatar-photo
                  [buffer]="userSelected?.photoProfile?.data"
                  [userId]="userSelected?._id"
                  [circleColor]="userSelected?.photoColor"
                  [name]="userSelected?.name"
                  [surname]="userSelected?.surname"
                  [class]="'userProfileTransaction'"
                  [classAvatarInitials]="'classAvatarInitialsSecond'"
                ></app-avatar-photo>
                    <img
                    *ngIf="userSelected?.isVerified"
                      style="
                        height: 20px;
                        width: 20px;
                        position: absolute;
                        bottom: 0;
                        right: 0;"
                      src="/assets/icons/fiberVerified.svg"
                      alt=""
                    />
                    <img
                    *ngIf="userSelected?.untrusted"
                      style="
                        height: 20px;
                        width: 20px;
                        position: absolute;
                        bottom: 0;
                        right: 0;
                      "
                      src="/assets/icons/fiberUnverified.svg"
                      alt=""
                    />
                  </div>

                  <div class="disp-grid">
                    <span>{{userSelected?.name}} {{userSelected?.surname}}</span> <span>{{userSelected?.email}}</span>
                  </div>
                  <span style="font-size: 17px" class="m-l-auto"
                    >{{userSelected?.coins}} Credits</span
                  >
                </div>
              </div>
              <div
                style="display: flex; gap: 1em; justify-content: space-between"
              >
                <div>
                  <span style="margin-left: 1em">Outflow Credits</span>
                  <div style="display: flex; align-items: center">
                    <div class="input-bar">
                      <input 
                      [(ngModel)]="creditsAmount"
                      class="" type="number" placeholder="eg. 100" />
                    </div>
                  </div>
                </div>
                <div>
                  <span style="margin-left: 1em">Inflow real money</span>
                  <div style="display: flex; align-items: center">
                    <div style="align-items: center" class="input-bar">
                      <input 
                      [(ngModel)]="realMoneyAmount"
                      class="" type="number" placeholder="eg. 100" />
                      <div>
                        <input
                          style="display: none"
                          id="dropdownInput"
                          type="checkbox"
                        />
                        <div
                          style="display: flex; justify-content: center"
                          class="dropdown"
                        >
                          <select
                            style="
                              display: flex;
                              background: transparent;
                              border: none;
                              color: white;
                              font-size: 18px;
                              font-weight: 600;
                            "                    
                            (ngModelChange)="realMoneyCurrency = $event"
                            [(ngModel)]="realMoneyCurrency">
                            <option  style="" class="optionStyle">
                              EUR
                            </option>
                            <option  style="" class="optionStyle">
                              ALL
                            </option>
                            <option  style="" class="optionStyle">
                              GBP
                            </option>
                            <option  style="" class="optionStyle">
                              USD
                            </option>
                          </select>
                          <label for="dropdownInput" class="overlay"></label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              style="display: flex; justify-content: end; align-items: center">
              <div (click)="create();
                            modal.close('Close click')" class="saveButton">
                Save
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>

  <ng-template #statusContent let-modal>
    <div
      style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1"
    >
      <div class="modal-body">
        <!-- header -->
        <div style="display: flex; justify-content: end; width: 100%">
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; margin-left: auto"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->

        <div style="margin: 1em 0">
          <div style="display: flex; flex-direction: column; gap: 2em">
            <!-- Skeletopn Start -->

            <div
              style="
                display: none !important;
                display: grid;
                width: 100%;
                gap: 1em;
              "
            >
              <div
                style="
                  padding: 0.5em 1em !important;
                  height: 64px;
                  align-items: center;
                  gap: 1em;
                "
                class="skeleton1 disp-flex j-c-center a-i-center"
              >
                <div
                  style="
                    height: 18px;
                    width: 118px;
                    border-radius: 3px;
                    margin: 0 0 0 50%;
                    transform: translateX(-50%);
                  "
                  class="skeleton2"
                ></div>
                <div
                  style="
                    height: 12px;
                    width: 15px;
                    border-radius: 6px;
                    margin-left: auto;
                  "
                  class="skeleton2"
                ></div>
              </div>
              <div
                style="
                  padding: 0.5em 1em !important;
                  height: 64px;
                  align-items: center;
                  gap: 1em;
                "
                class="skeleton1 disp-flex j-c-center a-i-center"
              >
                <div
                  style="height: 18px; width: 179px; border-radius: 3px"
                  class="skeleton2"
                ></div>
              </div>
            </div>
            <!-- Skeletopn End -->
            <div
              style="
                display: flex;
                gap: 1em;
                align-items: center;
                background-color: var(--app-container);
                padding: 1em;
                border-radius: 15px;
              "
            >
              <div
                style="
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  position: relative;
                "
              >
              <app-avatar-photo
              [buffer]="transactionSelected?.createdBy?.photoProfile?.data"
              [userId]="transactionSelected?.createdBy?._id"
              [circleColor]="transactionSelected?.createdBy?.photoColor"
              [name]="transactionSelected?.createdBy?.name"
              [surname]="transactionSelected?.createdBy?.surname"
              [class]="'userProfileTransaction'"
              [classAvatarInitials]="'classAvatarInitialsSecond'"
            ></app-avatar-photo>

                <img
                *ngIf="transactionSelected?.createdBy?.isVerified"
                  style="
                    height: 20px;
                    width: 20px;
                    position: absolute;
                    bottom: 0;
                    right: 0;
                  "
                  src="/assets/icons/fiberVerified.svg"
                  alt=""
                />
                <img
                  *ngIf="transactionSelected?.createdBy?.untrusted"
                  style="
                    height: 20px;
                    width: 20px;
                    position: absolute;
                    bottom: 0;
                    right: 0;
                  "
                  src="/assets/icons/fiberUnverified.svg"
                  alt=""
                />
              </div>
              <div class="disp-grid gap-05">
                <div style="display: flex; gap: 5px">
                  <div class="disp-grid a-i-center">
                    <span style="font-size: 16px">
                      {{transactionSelected?.createdBy?.name }} 
                      {{transactionSelected?.createdBy?.surnmae}}</span>
                    <span style="font-size: 11px; opacity: 0.7"
                      >{{transactionSelected?.createdBy?.email}}</span
                    >
                  </div>
                  <div class="disp-grid infoCreditsWrapper">
                    <span style="text-align: center">Credits</span
                    ><span style="text-align: center">{{transactionSelected?.createdBy?.coins}}</span>
                  </div>
                </div>
                <div class="disp-flex gap-1">
                  <div class="disp-grid infoWrapper">
                    <span style="text-align: center">Post</span
                    ><span style="text-align: center">{{transactionSelected?.createdBy?.postCount}}</span>
                  </div>
                  <div class="disp-grid infoWrapper">
                    <span style="text-align: center">Followers</span
                    ><span style="text-align: center">{{transactionSelected?.createdBy?.followersCount}}</span>
                  </div>
                  <div class="disp-grid infoWrapper">
                    <span style="text-align: center">Following</span
                    ><span style="text-align: center">{{transactionSelected?.createdBy?.followingCount}}</span>
                  </div>
                  <div class="disp-grid infoWrapper">
                    <span style="text-align: center">Earned</span
                    ><span style="text-align: center">{{transactionSelected?.createdBy?.earnCount}}</span>
                  </div>
                </div>
              </div>
              <!-- <span
                style="
                  font-size: 12px;
                  border: 1px solid var(--action-color);
                  padding: 5px;
                  border-radius: 5px;
                "
                class="m-l-auto"
                >View Profile</span> -->
            </div>
            <div style="display: flex; width: 100%; gap: 2em">
              <div
                style="
                  border: 1px solid gray;
                  border-radius: 15px;
                  height: 50px;

                  width: 100%;
                "
              >
                <span
                  style="
                    margin: -11px 0px 0 14px;
                    font-size: 13px;
                    font-weight: 600;
                    background: var(--app-bg);
                    width: fit-content;
                    padding: 0 13px;
                    display: flex;
                  "
                >
                  Inflow Credits
                </span>
                <p 
                *ngIf="transactionSelected?.inflowCredits"
                style="text-align: center; margin: 0; font-size: 18px">{{transactionSelected?.creditsAmount}} credits</p>
              </div>
              <div
                style="
                  border: 1px solid gray;
                  border-radius: 15px;
                  height: 50px;

                  width: 100%;
                "
              >
                <span
                  style="
                    margin: -11px 0px 0 14px;
                    font-size: 13px;
                    font-weight: 600;
                    background: var(--app-bg);
                    width: fit-content;
                    padding: 0 13px;
                    display: flex;
                  "
                >
                  Outflow Credits
                </span>
                <p 
                *ngIf="transactionSelected?.outflowCredits"
                style="text-align: center; margin: 0; font-size: 18px">{{transactionSelected?.creditsAmount}} credits</p>
              </div>
            </div>

            <div style="display: flex; width: 100%; gap: 2em">
              <div
                style="
                  border: 1px solid gray;
                  border-radius: 15px;
                  height: 50px;

                  width: 100%;
                "
              >
                <span
                  style="
                    margin: -11px 0px 0 14px;
                    font-size: 13px;
                    font-weight: 600;
                    background: var(--app-bg);
                    width: fit-content;
                    padding: 0 13px;
                    display: flex;
                  "
                >
                  Inflow Real Money
                </span>
                <p 
                *ngIf="transactionSelected?.inflowRealMoney"
                style="text-align: center; margin: 0; font-size: 18px">{{transactionSelected?.realMoneyAmount}} {{transactionSelected?.realMoneyCurrency}} </p>
              </div>
              <div
                style="
                  border: 1px solid gray;
                  border-radius: 15px;
                  height: 50px;

                  width: 100%;
                "
              >
                <span
                  style="
                    margin: -11px 0px 0 14px;
                    font-size: 13px;
                    font-weight: 600;
                    background: var(--app-bg);
                    width: fit-content;
                    padding: 0 13px;
                    display: flex;
                  "
                >
                  Outflow Real Money
                </span>
                <p 
                *ngIf="transactionSelected?.outflowRealMoney"
                style="text-align: center; margin: 0; font-size: 18px">{{transactionSelected?.realMoneyAmount}} {{transactionSelected?.realMoneyCurrency}}</p>
              </div>
            </div>
            <div class="disp-flex a-i-center gap-1">
              <!-- <img
                class="upAnimation"
                src="../assets/animatedIcons/inflow_credit.svg"
                alt="arrowUp"
              /> -->
              <div
                style="
                  border: 1px solid gray;
                  border-radius: 15px;
                  height: 50px;

                  width: 100%;
                "
              >
                <span
                  style="
                    margin: -11px 0px 0 14px;
                    font-size: 13px;
                    font-weight: 600;
                    background: var(--app-bg);
                    width: fit-content;
                    padding: 0 13px;
                    display: flex;
                  "
                >
                  Type
                </span>
                <p style="text-align: center; margin: 0; font-size: 18px">
                  {{transactionSelected?.type}}
                </p>
              </div>
            </div>
            <div class="disp-flex a-i-center gap-1">
              <div
                style="
                  border: 1px solid gray;
                  border-radius: 15px;
                  height: 50px;

                  width: 100%;
                "
              >
                <span
                  style="
                    margin: -11px 0px 0 14px;
                    font-size: 13px;
                    font-weight: 600;
                    background: var(--app-bg);
                    width: fit-content;
                    padding: 0 13px;
                    display: flex;
                  "
                >
                  Reason
                </span>
                <p style="text-align: center; margin: 0; font-size: 10px">
                  {{transactionSelected?.reason}}
                </p>
              </div>
            </div>

            <div
              style="
                border: 1px solid gray;
                border-radius: 15px;
                height: 50px;

                width: 100%;
              "
            >
              <span
                style="
                  margin: -11px 0px 0 14px;
                  font-size: 13px;
                  font-weight: 600;
                  background: var(--app-bg);
                  width: fit-content;
                  padding: 0 13px;
                  display: flex;
                "
              >
                Created At
              </span>
              <p style="text-align: center; margin: 0; font-size: 18px">
                {{transactionSelected?.createdAt | date : 'MMMM d, y, h:mm'}}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>

  <ng-template #searchContent let-modal>
    <app-search-content
    [(me)]="me"
    (closeModal)="modal.dismiss('Cross click')"
    (userSelected)="
    modal.dismiss('Cross click');
    selectUser($event)"
    ></app-search-content>
  </ng-template>
</div>
