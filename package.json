{"name": "fiber-al", "version": "1.3.1", "main": "main.js", "scripts": {"ng": "ng", "start": "electron .", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "test-config": "node test-config.js"}, "dependencies": {"@angular/animations": "^14.3.0", "@angular/cdk": "^13.3.9", "@angular/common": "^14.2.0", "@angular/compiler": "^14.2.0", "@angular/core": "^14.2.0", "@angular/forms": "^14.2.0", "@angular/localize": "^14.2.0", "@angular/material": "^13.2.6", "@angular/material-moment-adapter": "^13.3.0", "@angular/platform-browser": "^14.2.0", "@angular/platform-browser-dynamic": "^14.2.0", "@angular/router": "^14.2.0", "@ng-bootstrap/ng-bootstrap": "^13.0.0", "@ngx-loading-bar/http-client": "^6.0.2", "@popperjs/core": "^2.10.2", "angular-mydatepicker": "^0.11.5", "angular-ng-autocomplete": "^2.0.8", "bcryptjs": "^2.4.3", "bluebird": "^3.5.1", "body-parser": "^1.18.2", "bootstrap": "^5.2.0", "buffer": "^6.0.3", "busboy-body-parser": "^0.3.2", "color-rgba": "^2.4.0", "compression": "^1.7.4", "connect-multiparty": "^2.2.0", "core-js": "^2.4.1", "cors": "^2.8.5", "date-fns": "^2.29.3", "dotenv": "^16.0.3", "ejs": "^3.1.8", "express": "^4.18.1", "express-form-data": "^2.0.18", "html-metadata-parser": "^2.0.4", "html-to-text": "^8.2.0", "jsonwebtoken": "^9.0.0", "moment": "^2.29.4", "mongoose": "^6.2.1", "mongoose-find-by-reference": "^1.0.5", "mongoose-paginate": "^5.0.3", "morgan": "^1.9.0", "multer": "^1.4.5-lts.1", "ng-multiselect-dropdown": "^0.3.8", "ng-pick-datetime": "^7.0.0", "ngx-electron": "^2.2.0", "ngx-image-cropper": "^6.2.1", "ngx-infinite-scroll": "^15.0.0", "ngx-loading": "^13.0.1", "ngx-online-status": "^2.0.0", "ngx-slider-v2": "^15.0.4", "ngx-toastr": "^14.2.2", "nodemailer": "^6.7.6", "rxjs": "~7.5.0", "socket.io-client": "^4.5.1", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.1", "@angular/cli": "^14.2.1", "@angular/compiler-cli": "^14.2.0", "@types/jasmine": "~3.10.0", "@types/node": "^12.11.1", "electron-packager": "^17.1.1", "electron-rebuild": "^3.2.9", "jasmine-core": "~4.0.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": "~4.8.2"}}