<div style="" class="projects-section users-section">
  <div
    style="
      /* display: none !important; */
      display: grid;
      grid-template-rows: 4em 1fr 4em;
      background-color: var(--sidebar);
      padding: 5px 1em;
      border-radius: 20px;
      height: 76vh;
    "
  >
    <div class="disp-flex a-i-center">
      <span class="no-wrap-1-line">Native Notification List</span>
      <div class="m-l-auto disp-flex">
        <div
        (click)="redirectTo('send-notification')">
          <button class="add-btn" title="Add New Project">+</button>
        </div>
      </div>
    </div>

    <div
    *ngIf="data?.length === 0 && !loading"
      style="
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
      "
    >
      <img
        style="width: 70%; max-width: 299px"
        src="/assets/icons/animatedIconsTable/list_empty.svg"
        alt=""
      />
      <p style="font-size: 16px">List is empty</p>
    </div>
    <ul
      style="padding-inline-start: 0px; margin-bottom: 0rem; overflow-y: scroll"
      class=""
    >
      <ng-container *ngIf="loading">
        <li
          *ngFor="let i of [].constructor(10)"
          class="skeleton1"
          style="
            display: grid;
            grid-template-columns: 1.5fr 0.5fr 1fr 1fr 1.5fr 1fr 0.5fr;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 40%;
                height: 16px;
                margin-left: 13px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 53%;
                height: 16px;
              "
            ></span>
          </div>

          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 53%;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 53%;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 53%;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid"></div>
        </li>
      </ng-container>


      <li
        *ngFor="let dat of data; let i = index" 
        style="
          display: flex;
          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          margin-bottom: 5px;
          padding: 0 1em;
          border-radius: 10px;
        "
      >
        <div
          style="
            background: var(--app-bg);
            height: 100%;
            display: flex;
            align-items: center;
            flex: 1 0 70%;
            border-radius: 10px;
            padding: 5px 1em;
            gap: 5px;
          "
        >
          <app-avatar-photo
            [userId]="'630a2e5d4989aae851a657e4'"
            [circleColor]="me?.photoColor"
            [name]="'Fiber'"
            [surname]="'Al'"
            [class]="'notification-sent-content-image-preview'"
            [classAvatarInitials]="'initialsClass'"
          ></app-avatar-photo>
          <div style="display: grid">
            <span style="font-size: 11px; opacity: 0.7">
              {{dat?.socketTitle}}
            </span>
          </div>
          <span style="margin-left: auto; opacity: 0.7; font-size: 10px">
            {{dat?.createdAt | date : "MMM d, h:mm a"  }} </span>
          <img
          *ngIf="dat?.thumbnailId"
          style="width: 40px; border-radius: 10px"
          src="{{ backendImageUrl + dat.thumbnailId.toString() }}"
          alt="thumbnail"
        />
        </div>
        <div
          style="
            height: 100%;
            display: flex;
            align-items: center;
            flex: 1 0 30%;
          "
        >
          <div>{{dat?._id}}</div>
          <!-- <img
            style="width: 25px; margin-left: auto"
            src="/assets/icons/edit.svg"
            alt=""
          /> -->
        </div>
      </li>
    </ul>
    <div class="list-number disp-flex" style="">
      <div
        class="showingInfoWrapper"
        style="
          margin: 0 0 0.75rem;
          display: flex !important;
          align-items: center;
          gap: 1em;
        "
      >
        <span class="showingInfo">
          Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
          {{
            pageNo * resultsPerPage - (resultsPerPage - data.length)
          }}
          of {{ count }}
        </span>
        <div style="border: 1px solid gray; border-radius: 15px">
          <div>
            <input style="display: none" id="dropdownInput" type="checkbox" />
            <div
              style="
                display: flex;
                justify-content: center;
                height: fit-content;
                width: 50px;
                padding: 5px 0;
              "
              class="dropdown"
            >
              <select
              (ngModelChange)="resultsPerPageChanged($event)"
              [(ngModel)]="resultsPerPage" 
                style="
                  display: flex;
                  background: transparent;
                  border: none;
                  color: white;
                  font-size: 18px;
                  width: 90%;
                  font-weight: 600;
                "
              >
                <option
                  style="
                    font-size: 16px;
                    text-align: center;
                    width: 100%;
                    background-color: var(--app-bg);
                    line-height: 2;
                    border: none;
                    color: white;
                    font-weight: 600;
                    height: 40px;
                  "
                >
                  4
                </option>
                <option
                  style="
                    font-size: 16px;
                    text-align: center;
                    width: 100%;
                    background-color: var(--app-bg);
                    line-height: 2;
                    border: none;
                    color: white;
                    font-weight: 600;
                    height: 40px;
                  "
                >
                  7
                </option>
                <option
                  style="
                    font-size: 16px;
                    text-align: center;
                    width: 100%;
                    background-color: var(--app-bg);
                    line-height: 2;
                    border: none;
                    color: white;
                    font-weight: 600;
                    height: 40px;
                  "
                >
                  10
                </option>
                <option
                  style="
                    font-size: 16px;
                    text-align: center;
                    width: 100%;
                    background-color: var(--app-bg);
                    line-height: 2;
                    border: none;
                    color: white;
                    font-weight: 600;
                    height: 40px;
                  "
                >
                  15
                </option>
                <option
                  style="
                    font-size: 16px;
                    text-align: center;
                    width: 100%;
                    background-color: var(--app-bg);
                    line-height: 2;
                    border: none;
                    color: white;
                    font-weight: 600;
                    height: 40px;
                  "
                >
                  20
                </option>
                <option
                  style="
                    font-size: 16px;
                    text-align: center;
                    width: 100%;
                    background-color: var(--app-bg);
                    line-height: 2;
                    border: none;
                    color: white;
                    font-weight: 600;
                    height: 40px;
                  "
                >
                  30
                </option>
                <option
                  style="
                    font-size: 16px;
                    text-align: center;
                    width: 100%;
                    background-color: var(--app-bg);
                    line-height: 2;
                    border: none;
                    color: white;
                    font-weight: 600;
                    height: 40px;
                  "
                >
                  50
                </option>
              </select>
              <label for="dropdownInput" class="overlay"></label>
            </div>
          </div>
        </div>
      </div>

      <nav>
        <ul class="pager">
          <li class="pager__item pager__item--prev">
      <a
        *ngIf="pageNo !== 1"
        (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
        style="width: fit-content !important; padding: 0 10px"
        class="pager__link"
      >
        Previous
      </a>
    </li>
    <li *ngIf="pageNo !== 1" class="pager__item">
      <a (click)="setPage(1)" class="pager__link">...</a>
    </li>
    <li
      *ngFor="
        let item of [].constructor(pageNoTotal) | slice : 0 : 5;
        let i = index
      "
      [ngClass]="pageNo + i === pageNo ? 'active' : ''"
      class="pager__item"
    >
      <a
        *ngIf="pageNo + i <= pageNoTotal"
        (click)="setPage(pageNo + i)"
        class="pager__link"
        >{{ pageNo + i }}</a
      >
    </li>
    <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
      <a (click)="setPage(pageNoTotal)" class="pager__link"
        >...</a
      >
    </li>
    <li
      *ngIf="pageNo !== pageNoTotal"
      class="pager__item pager__item--next"
    >
      <a
        (click)="
          pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''
        "
        style="width: fit-content !important; padding: 0 10px"
        class="pager__link"
      >
        Next
      </a>
    </li>
        </ul>
      </nav>
    </div>
  </div>
</div>
