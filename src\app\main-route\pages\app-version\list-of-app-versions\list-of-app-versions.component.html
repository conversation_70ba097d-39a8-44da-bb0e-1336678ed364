<!-- Container-fluid starts-->
<div class="container-fluid">
  <div class="page-header">
    <div class="row">
      <div class="col-lg-6">
        <div class="page-header-left">
          <h3>
            List of app versions
            <small>Fiber Admin Panel </small>
          </h3>
        </div>
      </div>
      <div class="col-lg-6">
        <ol class="breadcrumb pull-right">
          <li class="breadcrumb-item">
            <a>
              <i data-feather="home"></i>
            </a>
          </li>
          <li class="breadcrumb-item">App versions</li>
          <li class="breadcrumb-item active">List of app versions</li>
        </ol>
      </div>
    </div>
  </div>
</div>
<!-- Container-fluid Ends-->

<ngx-loading
  [(show)]="loading"
  [config]="{
    animationType: ngxLoadingAnimationTypes.circleSwish,
    primaryColour: '#ffffff',
    backdropBorderRadius: '3px'
  }"
></ngx-loading>

<!-- Container-fluid starts-->
<div class="container-fluid" *ngIf="!loading">
  <div class="card">
    <div class="card-header">
      <a
        [routerLink]="['/' + lang + '/new-app-version']"
        class="btn btn-primary mt-md-0 mt-2"
        ><small>Create App Version </small></a
      >
    </div>

    <div class="card-body">
      <div class="table-responsive table-desi">
        <table class="all-package coupon-table table table-striped">
          <thead>
            <tr>
              <th>App Version</th>
            </tr>
          </thead>

          <tbody>
            <tr *ngFor="let appVersion of appVersions; let i = index">
              <td>{{ appVersion.current }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
<!-- Container-fluid Ends-->
