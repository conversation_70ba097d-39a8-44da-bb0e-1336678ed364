@media screen and (max-width: 980px) {
    p,
    span,
    div {
      color: white;
      font-size: 14px;
      font-weight: 600;
    }
  }
  
  @media screen and (max-width: 520px) {
    p,
    span,
    div {
      color: white;
      font-size: 13px;
      font-weight: 600;
    }
  }
  
  @media screen and (max-height: 980px) {
    :root {
      --font-size-xxxl: 35px;
      --font-size-xxl: 25px;
      --font-size-xl: 25px;
      --font-size-l: 20px;
      --font-size-m: 15px;
      --font-size-s: 10px;
      --font-size-xs: 5px;
    }
    p,
    span,
    div {
      color: white;
      font-size: 13px;
      font-weight: 600;
    }
  }
  @media screen and (max-height: 980px) {

    tr > * {
      padding: 0px 0px 5px 5px;
    }
    td div.value {
      background-color: var(--action-color-50);
      height: 0.3em;
      animation: animation reverse 1s linear;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      border-radius: 0 10px 10px 0;
    }
    th {
      border-right: solid 1px rgba(0, 0, 0, 0);
      width: 25%;
      text-align: left;
      font-size: 8px;
    }
   
  }