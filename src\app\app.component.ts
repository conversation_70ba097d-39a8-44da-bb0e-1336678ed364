import { Component, HostListener } from '@angular/core';
import { fromEvent, Observable, Subscription } from 'rxjs';
import { User } from './shared/models/user';
import { AuthService } from './shared/services/auth.service';
import { LoadJsService } from './shared/services/load-js.service';
import { NavigationStart, NavigationError, NavigationEnd } from '@angular/router';
import { Router, ActivatedRoute, Params, RoutesRecognized } from '@angular/router';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent {


  private authSub: Subscription;

  constructor(
    private activatedRoute : ActivatedRoute,
    private authService: AuthService,
    public loadJsService: LoadJsService) {
    }

  ngOnInit() {
    this.activatedRoute.params.subscribe(params => {
   });

    this.authSub = this.authService.autoLogin().subscribe( () =>{});
  }

  ngOnDestroy() {
    if (this.authSub) {
      this.authSub.unsubscribe();
    }
  }

} 
