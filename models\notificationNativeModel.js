const mongoose = require('mongoose');
const { MongooseFindByReference } = require('mongoose-find-by-reference');

const notificationNativeSchema = new mongoose.Schema(
  { 
    nativeTitle : {
        type: String,
        trim: true
    },
    socketTitle : {
        type: String,
        trim: true
    },
    description : {
        type: String,
        trim: true
    },
    directLink: {
        type: String,
        trim: true,
        default: '/notification-details'
    },
    thumbnailId:{
        type: String,
        trim: true,
    },
    active: {
        type: Boolean,
        default: true
    },
    createdBy: {
        type: mongoose.ObjectId,
        ref: 'User',
        required: [true, 'Notification Native must been created from a User!'],
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
  }

);

notificationNativeSchema.pre('save', async function( next) {
    next();
});
  
notificationNativeSchema.pre(/^find/, function(next) {
    next();
  })

notificationNativeSchema.plugin(MongooseFindByReference);

const NotificationNative = mongoose.model('NotificationNative', notificationNativeSchema);

module.exports = NotificationNative;
