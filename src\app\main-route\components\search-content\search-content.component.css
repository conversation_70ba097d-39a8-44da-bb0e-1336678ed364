.exchange-section {
  display: grid;
  grid-template-columns: 336px 3fr;
  padding: 32px !important;
  max-height: 1000px;
}
.exchange-tiles-Wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 1em;
}

.exchange-tile {
  height: 50%;
  background-color: var(--filter-reset);
  border-radius: 20px;
  display: grid;
  padding: 2em;

  /* box */
}
.exchange-animation {
  position: absolute;
  height: 17%;
  width: 17%;
  max-height: 80px;
  max-width: 80px;
  min-height: 30px;
  min-width: 30px;
  left: 50%;
  transform: translateX(-50%);
}
.exchange-animation > img {
  height: 100%;
  width: 100%;
  object-fit: contain;
}
.credit-to-euro {
  position: absolute;
  top: 101%;
  margin: 0;
  text-align: center;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0.5;
}
.exchange-button {
  background: var(--action-color);
  font-size: 20px;
  padding: 0.5em 2em;
  width: 100%;
  justify-content: center;
  display: flex;
  align-items: center;
  border: 1px solid var(--action-color);
  border-radius: 10px;
  margin: 0.3em 0 0;
}

.creditsInputWrapper {
  border-radius: 20px;
  background-color: var(--search-area-bg);
  padding-right: 12px;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 60%;
  max-width: 480px;
  color: var(--light-font);
  box-shadow: 0 2px 6px 0 rgba(136, 148, 171, 0.2),
    0 24px 20px -24px rgba(71, 82, 107, 0.1);
  overflow: hidden;
}
.dark .creditsInputWrapper {
  box-shadow: none;
}
.creditsInput {
  border: none;
  flex: 1;
  outline: none;
  height: 100%;
  padding: 0 20px;
  font-size: 20px;
  background-color: var(--search-area-bg);
  color: var(--main-color);
  font-weight: 600;
  width: 100%;
}
.creditsInput:placeholder {
  color: var(--main-color);
  opacity: 0.6;
}

@media screen and (max-width: 1248px) {
  .showingInfoWrapper {
    display: none !important;
  }
  .list-number {
    justify-content: center !important;
  }
}
@media screen and (max-width: 992px) {
  .creditsInputWrapper {
    width: 100%;
  }
  .pager__item {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }
  .pager__item.active,
  .pager__item:first-of-type,
  .pager__item:last-of-type,
  .pager__item:nth-of-type(2),
  .pager__item:nth-last-of-type(2) {
    position: initial;
    top: initial;
    left: initial;
  }
  .pager__item.active + li {
    position: initial;
    top: initial;
    left: initial;
  }
}

@media screen and (max-width: 893px) {
  .exchange-section {
    grid-template-columns: 1fr !important;

    grid-template-rows: 1fr 1fr !important;
    overflow: scroll;
  }
  .searchModalAtm {
    transform: translateY(-42px);
  }
}

/* // CARD */

.cardWrapper {
  width: 100%;
  border: none;
  border-radius: 10px;
  background-color: var(--link-color-active-bg);
  margin: 0em;
  padding: 1em;
}
.cardRec {
  display: flex;
  justify-content: center;
  align-items: start;
  gap: 5px;
}
.cardRec > .cardImage {
  width: 30%;
  /* border-radius: 15px; */
  overflow: hidden;
}
.cardContent {
  width: 100%;

  gap: 5px;
}

.stats {
  font-size: 14px;
  font-weight: 500;
  color: rgb(240, 240, 240) !important;
}
.articles {
  font-size: 10px;
  color: #a1aab9;
}
.cardNumbersWrapper > div {
  width: 100%;
}
.cardNumbers {
  background-color: var(--app-bg);
  display: grid;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  padding: 4px;
}
.cardNumbers > span {
  color: white;
  text-align: center;
  font-weight: 600;
  font-size: 11px;
}
/* button {
     height: 40px;
     margin: 1em 1em 0;
     background: var(--fiberAccent);
     border-radius: 10px;
   } */
.status-inProgress {
  background: var(--action-color-30);
  text-align: center;
  line-height: 2;
  border-radius: 10px;
  color: var(--action-color);
  font-size: 14px;
}
.status-success {
  background: #00800014;
  text-align: center;
  line-height: 2;
  border-radius: 10px;
  color: var(--green);
  font-size: 14px;
}

.status-canceled {
  background: #80000031;
  text-align: center;
  line-height: 2;
  border-radius: 10px;
  color: var(--red);
  font-size: 14px;
}

.status-waiting {
  background: #80690014;
  text-align: center;
  line-height: 2;
  border-radius: 10px;
  color: #fcb92c;
  font-size: 14px;
}
.searchModalAtm {
  position: absolute;
  background: var(--app-bg);
  display: flex;
  justify-content: start;
  align-items: center;
  flex-direction: column;
  z-index: 2;
  min-width: 336px;
  max-width: 336px;
  min-height: 516px;
  max-height: 516px;
  border-radius: 20px;
  transform: translateY(-42px);
}

/* !Settings Modal */
/** custom dropdown */
label.dropbtn {
  height: 30px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 1em;
}
label.dropbtn:hover {
  box-shadow: inset 0px 8px 16px 0px rgb(0 0 0 / 20%);
  border-radius: 19px;
}

/** full css dropdown interaction */
.dropdown {
  height: 59px;
}
.dropdown-content p {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  cursor: pointer;
  text-indent: 20px;
}
.dropdown-content p:hover {
  box-shadow: inset 0px 8px 16px 0px rgb(0 0 0 / 20%);
  /* border-radius: 15px; */
}
.dropdown-content {
  display: none;
}
.dropdown-content label {
  display: block;
  width: 100%;
  padding: 1rem 0;
}
input:checked ~ .dropdown .overlay {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  z-index: 190;
}
.optionStyle {
  font-size: 16px;
  text-align: center;
  width: 100%;
  background-color: var(--app-bg);
  line-height: 2;
  border: none;
  color: white;
  font-weight: 600;
  height: 40px;
}

.exchangeRateInputWrapper {
  border-radius: 20px;
  background-color: var(--app-bg);
  padding-right: 12px;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  color: var(--light-font);

  overflow: hidden;
}
.dark .exchangeRateInputWrapper {
  box-shadow: none;
}
.exchangeRateInput {
  border: none;
  flex: 1;
  outline: none;
  height: 100%;
  padding: 0 20px;
  font-size: 20px;
  background-color: var(--app-bg);
  color: var(--main-color);
  font-weight: 600;
  width: 100%;
  text-align: center;
}
.exchangeRateInput:placeholder {
  color: var(--main-color);
  opacity: 0.6;
}
.saveButton {
  background: var(--action-color);
  font-size: 17px;
  padding: 8px 2em;
  border-radius: 10px;
  cursor: pointer;
}
.saveButton:hover {
  background: var(--action-color-30);
  border: 1px solid var(--action-color);
}

.colorInProgress {
  color: var(--action-color);
}
.colorSuccess {
  color: var(--green);
}

.colorCancel {
  color: var(--red);
}

.colorWaiting {
  color: #fcb92c;
}

.reasonModalInputWrapper {
  border-radius: 20px;
  background-color: var(--app-bg);
  padding-right: 12px;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  color: var(--light-font);

  overflow: hidden;
}
.dark .reasonModalInputWrapper {
  box-shadow: none;
}
.reasonModalInput {
  border: none;
  flex: 1;
  outline: none;
  height: 100%;
  padding: 0 20px;
  font-size: 20px;
  background-color: var(--app-bg);
  color: var(--main-color);
  font-weight: 600;
  width: 100%;
  text-align: center;
  word-wrap: break-word;
  word-break: break-all;
}
.reasonModalInput:placeholder {
  color: var(--main-color);
  opacity: 0.6;
}
/* ng bootstrap modal */

.dark-modal .modal-content {
  background-color: #292b2c !important;
  color: white;
}
.dark-modal .close {
  color: white;
}
.light-blue-backdrop {
  background-color: #5cb3fd;
}
p,
span,
div {
  color: white;
  font-size: 16px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
