<div style="display: flex; flex-direction: row" class="projects-section suggested-section">
  <div style="width: 50%; height: 100%">
    <div style="
        display: grid;
        grid-template-rows: 4em 2em 1fr 4em;
        background-color: var(--sidebar);
        padding: 5px 1em;
        border-radius: 20px;
        /* height: fit-content; */
        max-height: 95%;
        min-height: 95%;
      ">
      <div style="gap: 1em" class="disp-flex a-i-center">
        <span class="no-wrap-1-line">Suggested List</span>
        <span style="
            margin-left: auto;
            height: 30px;
            padding: 0 2em;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--action-color);
            border-radius: 12px;
            cursor: pointer;
          ">Show more</span>
        <!-- <span
          style="
            height: 30px;
            width: 30px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 30px;
            background-color: var(--action-color);
            margin-bottom: 1px;
            cursor: pointer;
          "
          >+</span
        > -->
      </div>
      <div style="
          display: grid;
          grid-template-columns: 1fr 2fr 0.5fr;
          background-color: rgba(26, 37, 59, 0.5);
          align-items: center;
          margin-bottom: 5px;
          border-radius: 10px;
          gap: 1em;
          opacity: 0;
        ">
        <!-- <span style="margin-left: 1em" class="no-wrap-1-line"
          >User Details</span
        >
        <span class="no-wrap-1-line">Posts</span>
        <span class="no-wrap-1-line">Action</span> -->
      </div>
      <ul style="padding-inline-start: 0px; overflow: auto" class="">
        <!-- <ng-container *ngIf="startLoading"> -->
        <li *ngFor="let i of [].constructor(1)" class="skeleton1" style="
            display: grid;
            grid-template-columns: 1fr 1fr 0.5fr;
            padding: 0 1em;

            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          ">
          <div class="disp-flex a-i-center gap-05">
            <span class="skeleton2" style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 50px;
                height: 50px;
                margin-bottom: 4px;
                border-radius: 50px;
                margin-left: -10px;
              "></span>
            <span class="skeleton2" style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 77px;
                height: 17px;
                margin-bottom: 4px;
              "></span>
          </div>
          <div class="disp-flex a-i-center gap-05">
            <span class="skeleton2" style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 65px;
                height: 65px;
                margin-bottom: 4px;
                /* border-radius: 50px; */
              "></span>
            <span class="skeleton2" style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 50px;
                height: 17px;
                margin-bottom: 4px;
              "></span>
          </div>
          <div class="disp-grid j-c-center">
            <span class="skeleton2" style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 17px;
                height: 17px;
                margin-bottom: 4px;
                margin-left: 15px;
              "></span>
          </div>
        </li>
        <!-- </ng-container> -->
        <!-- Skeleton ENd -->

        <!-- List empty  -->
        <!-- <div
            *ngIf="data?.length === 0 && !startLoading"
              style="
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
              "
            >
              <img
                style="width: 70%; max-width: 299px"
                src="/assets/icons/animatedIconsTable/list_empty_transaction_infinite.svg"
                alt=""
              />
              <p style="font-size: 16px">List is empty</p>
            </div> -->
        <!-- List empty end -->

        <!-- List empty  -->
        <!-- *ngIf="data?.length === 0 && !startLoading" -->
        <div style="
            position: relative;
            min-height: 365px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            height: 50%;
          ">
          <img style="width: 70%; max-width: 299px" src="../../../../../assets/icons/animatedIconsTable/list_empty.svg"
            alt="" />
          <p style="font-size: 16px; text-align: center; margin-top: 20px">
            Suggestion list is empty
          </p>
        </div>

        <!-- List empty end -->
        <!-- *ngFor="
            let dat of data | searchFilter : searchForm.value.search;
            let i = index
          " -->
        <li *ngFor="let i of [].constructor(5)" style="
            display: grid;
            grid-template-columns: 1fr 1fr 0.5fr;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
            margin-top: 7.5px;
            margin-bottom: 7.5px;
          ">
          <div class="disp-flex gap-05">
            <div style="position: relative">
              <!-- <app-avatar-photo
              [buffer]="dat?.user?.photoProfile?.data"
              [userId]="dat?.user?._id"
              [circleColor]="dat?.user?.photoColor"
              [name]="dat?.user?.name"
              [surname]="dat?.user?.surname"
              [class]="'userImgBloggers'"
              [classAvatarInitials]="'initialsClass'"
            ></app-avatar-photo> -->
              <img style="height: 50px; width: 50px; border-radius: 25px" src="/assets/user2.jpg" alt="" />

              <!-- *ngIf="dat?.user?.isVerified" -->
              <img style="
                  height: 20px;
                  width: 20px;
                  position: absolute;
                  bottom: 0;
                  right: 0;
                " src="/assets/icons/fiberVerified.svg" alt="" />
              <!-- <img
              *ngIf="dat?.user?.untrusted"
              style="
                height: 15px;
                width: 15px;
                position: absolute;
                bottom: 0;
                right: 0;
              "
              src="/assets/icons/fiberUnverified.svg"
              alt=""
            /> -->
            </div>

            <div class="disp-grid a-i-center">
              <span style="font-size: 16px" class="no-wrap-1-line">
                <!-- {{ dat?.user?.name }} {{ dat?.user?.surname }} -->
                Amer
              </span>
              <span style="font-size: 16px" class="no-wrap-1-line">
                <!-- {{ dat?.user?.name }} {{ dat?.user?.surname }} -->
                Abdulai
              </span>
            </div>
          </div>

          <div style="gap: 0.5em; align-items: center" class="disp-flex">
            <div style="position: relative">
              <!-- <app-img
              [class]="'postBloggerImg'"
              [(meId)]="me._id"
              [(postId)]="post._id"
              [(postUserId)]="post.user"
            ></app-img> -->
              <!-- style="height: 50px; width: 50px; border-radius: 25px" -->
              <!-- <img class="postSuggestedImg" src="/assets/user2.jpg" alt="" /> -->

              <div style="position: relative; justify-content: center;align-items: center;">
                <video src="../../../../assets/video.mp4" class="postSuggestedImg"></video>
                <img style="position: absolute;height: 16px;width: 16px; top: 21px;right: 21px;" src="assets/icons/promotionIcons/play.svg"
                  alt="">
              </div>
            </div>
            <div style="display: flex; flex-direction: column">
              <span style="
                  font-size: 14px;
                  /* display: flex; */
                  align-items: center;
                  justify-content: center;
                ">Image</span>
              <span style="
                  font-size: 14px;
                  /* display: flex; */
                  align-items: center;
                  justify-content: center;
                ">
                PPV
              </span>
            </div>
          </div>

          <div style="gap: 1em" class="disp-flex j-c-center a-i-center">
            <!-- (click)="
            userSelected = dat.user; openSelectImgModal(selectImgModal)
          " -->

            <!-- (click)="removeUser(dat?.user?._id)" -->
            <img (click)="openDeleteSuggested(deleteSuggested)" style="
                position: relative;
                height: 15px;
                width: 15px;
                border-radius: 50%;
                cursor: pointer;
              " src="/assets/icons/close.svg" alt="" />
          </div>
        </li>
      </ul>
      <!-- <div class="list-number disp-flex" style="">
          <div
            class="showingInfoWrapper"
            style="
              margin: 0 0 0.75rem;
              display: flex !important;
              align-items: center;
              gap: 1em;
            "
          >
            <span class="showingInfo"
              >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
              {{ pageNo * resultsPerPage - (resultsPerPage - data.length) }} of
              {{ count }}</span
            >
            <div style="border: 1px solid gray; border-radius: 15px">
              <div>
                <input style="display: none" id="dropdownInput" type="checkbox" />
                <div
                  style="
                    display: flex;
                    justify-content: center;
                    height: 45px;
                    width: 50px;
                  "
                  class="dropdown"
                >
                  <select
                    style="
                      display: flex;
                      background: transparent;
                      border: none;
                      color: white;
                      font-size: 18px;
                      width: 90%;
                      font-weight: 600;
                    "
                    (ngModelChange)="resultsPerPageChanged($event)"
                    [(ngModel)]="resultsPerPage"
                  >
                    <option class="optionStyle colorCancel">7</option>
                    <option class="optionStyle colorCancel">10</option>
                    <option class="optionStyle colorCancel">15</option>
                    <option class="optionStyle colorCancel">20</option>
                    <option class="optionStyle colorCancel">30</option>
                    <option class="optionStyle colorCancel">50</option>
                  </select>
                  <label for="dropdownInput" class="overlay"></label>
                </div>
              </div>
            </div>
          </div>
          <nav>
            <ul class="pager">
              <li class="pager__item pager__item--prev">
                <a
                  *ngIf="pageNo !== 1"
                  (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
                  style="width: fit-content !important; padding: 0 10px"
                  class="pager__link"
                >
                  Previous
                </a>
              </li>
              <li *ngIf="pageNo !== 1" class="pager__item">
                <a (click)="setPage(1)" class="pager__link">...</a>
              </li>
              <li
                *ngFor="
                  let item of [].constructor(pageNoTotal) | slice : 0 : 5;
                  let i = index
                "
                [ngClass]="pageNo + i === pageNo ? 'active' : ''"
                class="pager__item"
              >
                <a
                  *ngIf="pageNo + i <= pageNoTotal"
                  (click)="setPage(pageNo + i)"
                  class="pager__link"
                  >{{ pageNo + i }}</a
                >
              </li>
              <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
                <a (click)="setPage(pageNoTotal)" class="pager__link">...</a>
              </li>
              <li
                *ngIf="pageNo !== pageNoTotal"
                class="pager__item pager__item--next"
              >
                <a
                  (click)="pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''"
                  style="width: fit-content !important; padding: 0 10px"
                  class="pager__link"
                >
                  Next
                </a>
              </li>
            </ul>
          </nav>
        </div> -->
    </div>
  </div>
  <div style="width: 100%; height: 100%">
    <div style="
        /* display: none !important; */
        display: grid;
        grid-template-rows: 4em 1fr 4em;
        background-color: var(--sidebar);
        padding: 5px 1em;
        border-radius: 20px;
        height: fit-content;
        max-height: 95%;
        min-height: 95%;

      ">
      <div class="disp-flex a-i-center">
        <span class="no-wrap-1-line">Recent Posts</span>
        <div class="m-l-auto disp-flex">
          <div class="search-bar" style="margin-left: auto;max-width: 220px;">
            <!-- (keyup)="searchUser($event)" -->
            <input style="
                margin-right: 20px;
              " type="text" placeholder="Search" />
          </div>
          <div class="plusButton">
            <img style="width: 80%; object-fit: contain; border-radius: 10px" src="/assets/icons/refresh.svg" alt="" />
          </div>
        </div>
      </div>

      <ul style="
          display: flex;
          flex-wrap: wrap;
          overflow: auto;
          justify-content: center;
        " class="">

        <!-- Skeleton start -->
        <li style="
           position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 2em;
            border-radius: 15px;
            gap: 1em;
            background: var(--app-container);
            flex: 1 0 250px;
            margin: 1rem;
            color: #fff;
            cursor: pointer;
            max-width: 250px;
            max-height: 336px;
          " class="skeleton1" *ngFor="let i of [].constructor(2)">
          <div style="display: flex;gap: 10px;">
            <div style="width: 50px; height: 50px;border-radius: 50px;" class="skeleton2"></div>
            <div style="display: grid;flex: 1;">
              <div style="width: 80%; height: 15px; align-self: center" class="skeleton2"></div>
              <div style="width: 50%; height: 11px; align-self: center" class="skeleton2"></div>
            </div>
          </div>
          <div style="width: 50%;aspect-ratio: 9/16 ; align-self: center" class="skeleton2"></div>
          <div style="width: 100%; height: 32px; align-self: center;border-radius: 10px;" class="skeleton2"></div>
        </li>
        <!-- Skeleton ENd -->

        <!-- List empty  -->
        <!-- <div style="
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
              ">
          <img style="width: 70%; max-width: 400px" src="/assets/icons/animatedIconsTable/list_empty.svg" alt="" />
          <p style="font-size: 16px">There are no posts</p>
        </div> -->
        <!-- List empty end -->

        <li *ngFor="let i of [].constructor(20)" style="
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 2em;
            border-radius: 15px;
            gap: 1em;
            background: var(--app-container);
            flex: 1 0 250px;
            margin: 1rem;
            color: #fff;
            max-width: 250px;
            max-height: 336px;
          ">
          <div style="
              position: relative;
              display: grid;
              grid-template-columns: 1fr;
              align-items: center;
              gap: 15px;
            ">
            <div style="gap: 5px">
              <div style="
                  display: flex;
                  flex: 1 0 30%;
                  border-radius: 10px;
                  align-items: center;
                  gap: 10px;
                ">
                <div style="height: 50px; width: 50px; position: relative">
                  <!-- <app-avatar-photo
                    [buffer]="dat?.user?.photoProfile?.data"
                    [userId]="dat?.user?._id"
                    [circleColor]="dat?.user?.photoColor"
                    [name]="dat?.user?.name"
                    [surname]="dat?.user?.surname"
                    [class]="'userImgBloggers'"
                    [classAvatarInitials]="'initialsClass'"
                  ></app-avatar-photo> -->
                  <img style="height: 50px; width: 50px; border-radius: 25px" src="/assets/user2.jpg" alt="" />
                  <!-- *ngIf="dat?.user?.isVerified" -->
                  <img style="
                      height: 20px;
                      width: 20px;
                      position: absolute;
                      bottom: 0;
                      right: 0;
                    " src="/assets/icons/fiberVerified.svg" alt="" />
                  <!-- <img
            *ngIf="dat?.user?.untrusted"
            style="
              height: 15px;
              width: 15px;
              position: absolute;
              bottom: 0;
              right: 0;
            "
            src="/assets/icons/fiberUnverified.svg"
            alt=""
          /> -->
                </div>
                <div style="display: grid">
                  <span style="color: white; font-size: 16px; font-weight: 600">Amer Abdullai</span>
                  <span style="
                      color: white;
                      font-size: 14px;
                      font-weight: 500;
                      opacity: 0.7;
                    ">10 hours ago</span>
                </div>
              </div>
            </div>
          </div>
          <div style="display: flex; align-items: center; justify-content: center">
            <!-- <img style="width: 50%; object-fit: contain; border-radius: 10px" src="/assets/user916.jpg" alt="" /> -->
            <video src="../../../../assets/video.mp4"
              style="width: 50%; object-fit: contain; border-radius: 10px"></video>

          </div>
          <div style="
              position: relative;
              align-items: center;
              justify-self: center;
              gap: 15px;
            ">
            <div (click)="openDelRoleModal(addToSuggested)" class="addSuggestedButton">
              <span style="text-align: center; color: white">Add to Suggested</span>
            </div>
          </div>
        </li>
      </ul>
      <!-- <div class="list-number disp-flex" style="">
          <div
            class="showingInfoWrapper"
            style="
              margin: 0 0 0.75rem;
              display: flex !important;
              align-items: center;
              gap: 1em;
            "
          >
            <span class="showingInfo"
              >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
              {{ pageNo * resultsPerPage - (resultsPerPage - data.length) }} of
              {{ count }}</span
            >
            <div style="border: 1px solid gray; border-radius: 15px">
              <div>
                <input style="display: none" id="dropdownInput" type="checkbox" />
                <div
                  style="
                    display: flex;
                    justify-content: center;
                    height: 45px;
                    width: 50px;
                  "
                  class="dropdown"
                >
                  <select
                    style="
                      display: flex;
                      background: transparent;
                      border: none;
                      color: white;
                      font-size: 18px;
                      width: 90%;
                      font-weight: 600;
                    "
                    (ngModelChange)="resultsPerPageChanged($event)"
                    [(ngModel)]="resultsPerPage"
                  >
                    <option class="optionStyle colorCancel">7</option>
                    <option class="optionStyle colorCancel">10</option>
                    <option class="optionStyle colorCancel">15</option>
                    <option class="optionStyle colorCancel">20</option>
                    <option class="optionStyle colorCancel">30</option>
                    <option class="optionStyle colorCancel">50</option>
                  </select>
                  <label for="dropdownInput" class="overlay"></label>
                </div>
              </div>
            </div>
          </div>
          <nav>
            <ul class="pager">
              <li class="pager__item pager__item--prev">
                <a
                  *ngIf="pageNo !== 1"
                  (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
                  style="width: fit-content !important; padding: 0 10px"
                  class="pager__link"
                >
                  Previous
                </a>
              </li>
              <li *ngIf="pageNo !== 1" class="pager__item">
                <a (click)="setPage(1)" class="pager__link">...</a>
              </li>
              <li
                *ngFor="
                  let item of [].constructor(pageNoTotal) | slice : 0 : 5;
                  let i = index
                "
                [ngClass]="pageNo + i === pageNo ? 'active' : ''"
                class="pager__item"
              >
                <a
                  *ngIf="pageNo + i <= pageNoTotal"
                  (click)="setPage(pageNo + i)"
                  class="pager__link"
                  >{{ pageNo + i }}</a
                >
              </li>
              <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
                <a (click)="setPage(pageNoTotal)" class="pager__link">...</a>
              </li>
              <li
                *ngIf="pageNo !== pageNoTotal"
                class="pager__item pager__item--next"
              >
                <a
                  (click)="pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''"
                  style="width: fit-content !important; padding: 0 10px"
                  class="pager__link"
                >
                  Next
                </a>
              </li>
            </ul>
          </nav>
        </div> -->
    </div>
  </div>
  <ng-template #deleteSuggested let-modal>
    <div style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1">
      <div class="modal-body">
        <!-- header -->
        <div style="
          display: flex;
          justify-content: center;
          width: 100%;
          position: relative;
        ">
          <img (click)="modal.dismiss('Cross click')" style="
            height: 17px;
            width: 17px;
            position: absolute;
            right: 0;
            cursor: pointer;
          " src="/assets/icons/close.svg" alt="" />
        </div>
        <!-- header -->

        <div style="margin: 1em 0">
          <div style="
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            gap: 2rem;
          ">
            <span style="color: white; font-weight: 600; font-size: 17px">Are you sure ou want to delete
              Suggested</span>
            <div style="
              display: flex;
              align-items: center;
              justify-content: center;
            ">
              <img style="width: 50%; object-fit: contain; border-radius: 10px" src="/assets/user916.jpg" alt="" />
            </div>

            <div style="
              width: 50%;
              display: flex;
              justify-content: space-between;
              gap: 1em;
            ">
              <div (click)="modal.dismiss('Cross click')" class="modalButtonsCancel">
                Cancel
              </div>

              <div (click)="modal.dismiss('Cross click')" class="modalButtonsDelete">
                Delete
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
  <ng-template #addToSuggested let-modal>
    <div style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1">
      <div class="modal-body">
        <!-- header -->
        <div style="
            display: flex;
            justify-content: center;
            width: 100%;
            position: relative;
          ">
          <img (click)="modal.dismiss('Cross click')" style="
              height: 17px;
              width: 17px;
              position: absolute;
              right: 0;
              cursor: pointer;
            " src="/assets/icons/close.svg" alt="" />
        </div>
        <!-- header -->

        <div style="margin: 1em 0">
          <div style="
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
              gap: 2rem;
            ">
            <span style="color: white; font-weight: 600; font-size: 17px">Add this post to Suggested</span>
            <div style="
                display: flex;
                align-items: center;
                justify-content: center;
              ">
              <img style="width: 50%; object-fit: contain; border-radius: 10px" src="/assets/user916.jpg" alt="" />
            </div>

            <div style="
                width: 50%;
                display: flex;
                justify-content: space-between;
                gap: 1em;
              ">
              <div (click)="modal.dismiss('Cross click')" class="modalButtonsCancel">
                Cancel
              </div>

              <div (click)="modal.dismiss('Cross click')" class="modalButtonsDelete">
                Add
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
</div>