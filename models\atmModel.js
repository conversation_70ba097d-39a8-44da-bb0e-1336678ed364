const mongoose = require('mongoose');
const { MongooseFindByReference } = require('mongoose-find-by-reference');

const atmSchema = new mongoose.Schema({
  withdrawCreditsAmount:{
    type: Number,
    default: 0,
    required: [true, 'Not possible withdraw credits without credits Amount!']
  },
  withdrawRealMoneyAmount:{
    type: Number,
    default: 0,
    required: [true, 'Not possible withdraw credits without real Money Amount!']
  },
  withdrawRealMoneyCurrency:{
    type: String,
    default: 'EUR'
  },
  withdrawSuccessful: {
    type: Boolean,
    default: false
  },
  feeCreditsAmount: {
    type: Number,
    default: 0
  },
  creditsAmount: {
    type: Number,
    required: [true, 'Not possible withdraw credits without credits Amount!']
  },
  realMoneyAmount: {
    type: Number,
    required: [true, 'Not possible withdraw credits without real Money Amount!']
  },
  detailDescription: {
    type: String
  },
  status: {
    type: String,
    enum: ['waiting','processing','succes','canceled','waitingConfirm','confirmed','declined'],
    default: 'waiting'
  },
  type: {
    type: String,
    enum: ['paypal','officeWithdraw'],
    required: [true, 'Not possible online Payment without type of online payment!']
  },
  email: {
    type: String,
    lowercase: true
  },
  active: {
    type: Boolean,
    default: true
  },
  fee:{
    type: mongoose.Schema.ObjectId,
    ref: 'MinistryOfFinance',
  },
  transaction:{
    type: mongoose.Schema.ObjectId,
    ref: 'Transaction',
  },
  user:{
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: [true, 'must been created from a User!']
  },
  userWhoUpdated:{
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  },
  updatedAt: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

atmSchema.pre(/^find/, function(next) {
  // this.populate({
  // path: 'user',
  // });
  next();
})
atmSchema.plugin(MongooseFindByReference);

const ATM = mongoose.model('ATM', atmSchema);

module.exports = ATM;