import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { LoadJsService } from 'src/app/shared/services/load-js.service';
import { PromotionService } from 'src/app/shared/services/promotion.service';

@Component({
  selector: 'app-list-promotions',
  templateUrl: './list-promotions.component.html',
  styleUrls: ['./list-promotions.component.css']
})
export class ListPromotionsComponent implements OnInit {

  lang = 'en';
  searchForm;
  
  loading = false

  promotions: any[] = []


  public pageNo: number = 1;
  public pageNoTotal: number = 1;
  promotionCount : number = 0;
  resultsPerPage : number = 12;

  count: number = 0;

  constructor(
    private promotionService: PromotionService,
    private formBuilder: UntypedFormBuilder,
    private toastrService: ToastrService,
    public loadJsService: LoadJsService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private modalService:NgbModal
  ) { 
    this.searchForm = this.formBuilder.group({
      search: '',
    });
    this.loadJsService.loadScripts()
  }

  ngOnInit(): void {
    this.loading = true

    this.activatedRoute.params.subscribe( async paramMap => {
      if(paramMap['lang']){
        this.lang = paramMap['lang'];
      }
    })

    this.activatedRoute.queryParams.subscribe(params => {
      this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;
    })

    this.getAll()


  }

  public setPage(page: number) {
    this.promotions = []
    this.loading = true
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { page: page },
      queryParamsHandling: 'merge', // preserve the existing query params in the route
      skipLocationChange: false  // do trigger navigation
    }).finally(() => {
      this.getAll()
      // this.viewScroller.setOffset([120, 120]);
      // this.viewScroller.scrollToAnchor('deals'); // Anchore Link
    });
  }

  getAll(){
    this.promotionService.getAll(this.pageNo,this.resultsPerPage).subscribe( 
     async result =>{
        if(result.status == "success"){
              // this.users = await result.data.data.filter( user  =>{
              //   return  user._id !== this.user._id
              // });
              this.promotionCount = result.data.promotionCount

              console.log("result.data: ",result.data)
              
              this.count = result.data.promotionCount
              this.pageNoTotal = Math.round(this.promotionCount / this.resultsPerPage) + 1 
              this.promotions = result.data.data
              this.loading = false
              this.loading = false
          }

      },
      respond_error => {
       this.toastrService.error(
         respond_error?.error.message
          , respond_error?.name);         
       }
    )
  }

  onRemove(item, i, activeStatus){

    this.promotionService.changeActiveStatus(item._id,activeStatus).subscribe( 
      result =>{
        if(result.status == "success"){
              let _name_ = result.data.linkUrl

              if(activeStatus === false ){
                this.toastrService.error(
                  _name_ +" has been deactivated" 
                   ,  "Successfully deactivated" );
               }else{
                this.toastrService.success(
                  _name_ +  "has been recovered" 
                   ,  "Successfully recovered"  );
               }
              this.promotions[i].active = activeStatus
        }

    },
    respond_error => {
      this.toastrService.error(
        respond_error?.error.message
         , respond_error?.name);         
      }
    )
  }

  onPause(item, i, pauseStatus){
    this.promotionService.changePauseStatus(item._id,pauseStatus).subscribe( 
      result =>{
        if(result.status == "success"){
              let _name_ = result.data.linkUrl
              if(pauseStatus === true ){
                this.toastrService.error(
                  _name_ +" has been stoped" 
                   ,  "Successfully paused" );
               }else{
                this.toastrService.success(
                  _name_ +  "has been started again" 
                   ,  "Successfully started"  );
               }
               this.promotions[i].pause = pauseStatus
        }

    },
    respond_error => {
      this.toastrService.error(
        respond_error?.error.message
         , respond_error?.name);         
      }
    )
  }
  

  ngOnDestroy(){
    this.loadJsService.removeScripts()
  }
  redirectTo(uri:string){
    console.log("uri: ",uri)
    this.router.navigateByUrl(this.lang+ uri)
      .then(() =>{
        this.loadJsService.removeScripts()
      });
  }


  resultsPerPageChanged(event){
    this.loading = true;
    this.count =  1
    this.pageNoTotal = 1
    this.promotions = []
    this.resultsPerPage = Number(event)
    this.setPage(1)
  }

  openUserModal(ListModalComponent) {
    this.modalService.open(ListModalComponent, { size: 'xl'});
  }
  openRemoveRecoverModal(RemoveRecoverContent) {
    this.modalService.open(RemoveRecoverContent, { centered: true });
  }
  openPlayPauseModal(PlayPauseContent) {
    this.modalService.open(PlayPauseContent, { centered: true });
  }

}
