.input-image{
  visibility:hidden;
}
.projects-create-gift {
  padding: 32px !important;
  display: grid;
  grid-template-columns: 1fr 2fr !important;
}

.input-image-create-gift {
  display: flex;
  flex-direction: column;
  width: 100%;
  justify-content: center;
  align-items: center;
  border-radius: 13px;
  border: 4px dotted var(--sidebar);
  gap: 5px;
  padding: 1em;
  height: 200px;
}

.input-create-gift {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 4px;
}

.input-create-gift input {
  width: 100%;
  height: 100%;
  min-height: 40px;
  border: none;
  background-color: var(--sidebar);
  border-radius: 15px;
  font-family: var(--body-font);
  font-size: 14px;
  font-weight: 500;
  padding: 0 16px 0 16px;
  box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
  text-align: center;
  color: #fff;
}

.create-gift-input {
  width: 100%;
  height: 100%;
  min-height: 40px;
  border: none;
  background-color: var(--sidebar);
  border-radius: 15px;
  font-family: var(--body-font);
  font-size: 14px;
  font-weight: 500;
  padding: 0 16px 0 16px;
  box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
  color: #fff;
}
.promotion-sent-image {
  width: 50%;
  border-radius: 15px;
  object-fit: contain;
}

.promotion-sent-content-image {
  height: 40px;
  width: 40px;
  border-radius: 10px;
}

.promotion-sent-image-preview {
  height: 100%;
  width: 100%;
  border-radius: 10px;
  position: absolute;
  object-fit: cover;
}

.promotion-sent-image-preview-home {
  height: 70%;
  width: 100%;
  border-radius: 10px 10px 0 0;
  position: absolute;
  object-fit: cover;
}

button {
  background-color: var(--action-color-50);
  border: 1px solid var(--action-color);
  color: white;
  border-radius: 10px;
  height: 30px;
  width: 80px;
}

.buttonReset {
  background-color: var(--action-color);
  color: white;
  border-radius: 10px;
  height: 30px;
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
}

.test {
  font-size: 30px;
}
.gifts {
  background: rgba(94, 152, 216, 0.65);
  border-radius: 16px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10.3px);
  -webkit-backdrop-filter: blur(10.3px);
  border: 3px solid rgba(94, 152, 216, 1);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* padding: 1em; */
  gap: 0.6em;
  position: relative;
  cursor: pointer;
  
  margin-bottom: 2em;
}

.mediumTier {
  background: rgba(136, 70, 255, 0.65);
  border-radius: 16px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10.3px);
  -webkit-backdrop-filter: blur(10.3px);
  border: 3px solid rgba(136, 70, 255, 1);
}
.premiumTier {
  /* From https://css.glass */
  background: rgba(240, 174, 54, 0.65);
  border-radius: 16px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10.3px);
  -webkit-backdrop-filter: blur(10.3px);
  border: 3px solid rgba(240, 174, 54, 0.88);
}

.lowTierImg {
  background-color: rgba(94, 152, 216, 1);
}
.mediumTierImg {
  background-color: rgba(136, 70, 255, 1);
}
.premiumTierImg {
  background-color: rgba(240, 174, 54, 1);
}

.giftDetails {
  display: grid;
}

.giftImage {
  width: 100%;
  border-radius: 10px;
  margin-top: -2.3em;
  scale: 1.1;
}
.giftDetails {
  display: grid;
}
.coinValue {
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: center;
}
.coinValue p:nth-child(2) {
  color: white;
  font-size: 14px;
  font-weight: 700;
  margin: 0;
  text-align: center;
}
.coinValue img {
  color: white;
  font-size: 12px;
  height: 16px;
  width: 16px;
  border-radius: 10px;
}
.sendGiftButton {
  background: var(--action-color);
  color: white;
  padding: 2px 2em;
  border-radius: 5px;
  font-weight: 600;
}
.lowTierImg {
  background-color: rgba(94, 152, 216, 1);
}
.mediumTierImg {
  background-color: rgba(136, 70, 255, 1);
}
.premiumTierImg {
  background-color: rgba(240, 174, 54, 1);
}
.giftsSklet {
  background: rgba(71, 71, 71, 0.281);
  border-radius: 16px;
  box-shadow: 0 4px 30px rgba(104, 104, 104, 0.068);
  backdrop-filter: blur(10.3px);
  -webkit-backdrop-filter: blur(10.3px);
  border: 3px solid rgba(94, 153, 216, 0.082);

  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1em;
  gap: 0.6em;
  position: relative;
  cursor: pointer;
  
  margin-bottom: 2em;
}
.giftImageSklet {
  width: 60%;
  border-radius: 10px;
  margin-top: -2.3em;
  scale: 1.1;
}
.giftImageSentSklet {
  width: 60%;
  border-radius: 10px;
  scale: 1.1;
}
.giftDetailsSklet {
  display: grid;
  width: 100%;
  gap: 1em;
}
.coinValueSklet {
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: center;
}
.coinValueSklet p:nth-child(2) {
  color: white;
  font-size: 16px;
  font-weight: 700;
  margin: 0;
  text-align: center;
}

.coinValueSklet img {
  color: white;
  font-size: 12px;
  height: 16px;
  width: 16px;
  border-radius: 10px;
}

.sendGiftButtonSklet {
  background: var(--action-color-30);
  color: white;
  padding: 2px 2em;
  border-radius: 5px;
  font-weight: 600;
}
.lowTierImgSklet {
  background-color: rgba(94, 153, 216, 0.267);
}
.topNavigation {
  height: 35px;
  background-color: #3e3e3e;
  border-radius: 10px;
  display: flex;
  align-items: center;
  margin: 0 5px;
  padding: 0 5px;
}
.topNavigation > div {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.selectedTab {
  background-color: #696969;
  height: 32px;
  /* width: 90px; */
  border-radius: 10px;
}
.tab {
  display: flex;
  justify-content: center;
  align-items: center;
}
.tab p {
  color: var(--fiberWhite);
  font-size: 11px;
  font-weight: 600;
}

.giftsSent {
  background-color: rgb(17 17 17);
  border-radius: 10px;
  display: flex;
  align-items: center;
  padding: 1em 1em;
  margin: 1em;
  width: 100%;
}

.giftDetails {
  display: grid;
}

.giftsSent img {
  width: 50px;
  border-radius: 10px;
}
.setBy {
  display: flex;
  gap: 5px;
}
.setBy p:nth-child(1) {
  color: white;
  font-size: 10px;
  border-radius: 10px;
  margin: 0;
  display: flex;
  align-items: center;
}
.setBy p:nth-child(2) {
  color: white;
  font-size: 14px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
}

.timeSent {
  color: #828282;
  font-size: 10px;
  display: flex;
  margin-left: auto;
  margin-bottom: 0;
}
.timeSent > * {
  width: 100%;
}
p,
span,
div {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
