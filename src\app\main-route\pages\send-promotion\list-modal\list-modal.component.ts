import { Component, OnInit } from '@angular/core';
import { Options, LabelType } from 'ngx-slider-v2';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SearchUserModalComponent } from '../search-user-modal/search-user-modal.component';
import { UserService } from 'src/app/shared/services/user.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from 'src/app/shared/services/auth.service';
@Component({
  selector: 'ngbd-modal-stacked',
  templateUrl: './list-modal.component.html',
  styleUrls: ['./list-modal.component.css'],
})
export class ListModalComponent implements OnInit {
  loading
  userFilteredData = []
  checkboxFalseUsersId = []
  checkboxFalseUsers = []
  allUsers = []

  selectedUsersId = []
  selectedUsers = []


  pageNumber = 0
  loadingData =  false
  resultLength = 0
  
  filters : any = {
    followersCountActive : false,
    followingCountActive : false,
    postCountActive : false, 
    earnCountActive : false 
  }

  public pageNo: number = 1;
  public pageNoTotal: number = 1;
  count: number = 0;
  resultsPerPage: number = 10;

  searchText = '';
  me: any;

  timeout: any = null;

  modalReference = null;     

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private toastrService: ToastrService,
    private userService: UserService, 
    private modalService: NgbModal,
    private authService: AuthService,
    public activeModal: NgbActiveModal
  ) {
    this.authService.user.pipe().subscribe((appUser) => {
      this.me = appUser;
    });
  }

  ngOnInit(): void {
    this.activatedRoute.queryParams.subscribe((params) => {
      this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;
    });
    this.userService.sentUserFilteredObservable.subscribe((data:any) => {
      console.log("data: ",data)
      if(data !== null){
        this.router
        .navigate([], {
          relativeTo: this.activatedRoute,
          queryParams: { page: 1 },
          queryParamsHandling: 'merge', // preserve the existing query params in the route
          skipLocationChange: false, // do trigger navigation
        })
        if(data.allUsers.length !== 0){
        this.allUsers = data.allUsers
        }
        if(data?.filters !== null){
          this.filters = data.filters
        }
        if(data?.filters !== null){
          this.getUserFilteredData("next")
        }
      }
    });
  }

  openUserSearchModal(searchUser) {
    this.modalReference = this.modalService.open(searchUser, { centered: true });
  }

  getUserFilteredData(reason){
    this.loading = true
    this.loadingData =  true
    if(reason.toString()  === "next"){
      // this.pageNoTotal= this.pageNumber+1
    }else{
      // this.pageNumber = this.pageNumber-1;
    }
      this.userService.getUserFilteredData(
        this.filters,
        this.pageNo,
        this.resultsPerPage).subscribe( respond  => {
        if( respond.status = "success" ){
          let totalUsersWithFilter = []
          if(this.pageNo == 1 && this.selectedUsers.length !== 0){
            respond.data.totalUsersWithFilter = [...this.selectedUsers,...respond.data.totalUsersWithFilter]
          }
          if(respond.data.totalUsersWithFilter.length){
            totalUsersWithFilter = respond.data.totalUsersWithFilter.map(  data => { 
              let dataCopy = data
              console.log("this.checkboxFalseUsersId: ",this.checkboxFalseUsersId)
              let result =  this.checkboxFalseUsersId.indexOf(data._id);
              dataCopy["checked"] = result === -1 ? true :  false
              return dataCopy });
          }
  
  
            this.userFilteredData = totalUsersWithFilter
  
            if(reason.toString()  === "next"){
              this.resultLength = this.resultLength + totalUsersWithFilter.length
            }else{
              this.resultLength = this.resultLength - totalUsersWithFilter.length
            }
  
            this.count = respond.data.totalUsersCount + this.selectedUsers.length;
            this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
  
            this.loadingData =  false
            this.loading = false
          // this.totalUsersWithFilter = respond.data.totalUsersWithFilter
            // this.loading = false
          }
        },
        respond_error => {
          this.loadingData =  false
          // this.disableSubmit = false
            let error_message = respond_error.error.message;
            this.toastrService.error(error_message);            
      })
  }

  changeCheckbox(user,event){
    if(event.currentTarget.checked){
      this.checkboxFalseUsersId.splice(
      this.checkboxFalseUsersId.indexOf(user._id), 1);
    }else{
      this.checkboxFalseUsersId = [...this.checkboxFalseUsersId ,user._id] 
    }
    // console.log("this.checkboxFalseUsersId: ",this.checkboxFalseUsersId)
  }

  resultsPerPageChanged(event){
    this.loading = true;
    this.count =  1
    this.pageNoTotal = 1
    this.userFilteredData = []
    this.resultsPerPage = Number(event)
    this.getUserFilteredData("next")
  }


  public setPage(page: number) {
    this.userFilteredData = [];
    this.loading = true;
    this.router
      .navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: page },
        queryParamsHandling: 'merge', // preserve the existing query params in the route
        skipLocationChange: false, // do trigger navigation
      })
      .finally(() => {
          if(this.searchText === ''){
            this.getUserFilteredData("next")
            // this.getAll();
          }else{
            this.getAllResultSearch()
          }
      });
  }


  getAllResultSearch() {
    if(this.searchText == '') return
    this.loading = true;
    this.count =  1
    this.pageNoTotal = 1
    this.userFilteredData = []
    this.userService.searchWithFilters(
      this.filters,
      this.searchText, 
      this.me._id,
      this.pageNo,
      this.resultsPerPage).subscribe(
      (result) => {
        if (result.status == 'success') {
          // console.log("getAllResultSearch: ",result)
          this.count = result.data.totalUsersCount;
          this.userFilteredData = result.data.totalUsersWithFilter;
          this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
          this.loading = false;
        }
      },
      (respond_error) => {
        this.loading = false;
        // this.toastrService.error(
        //   respond_error?.error.message,
        //   respond_error?.name
        // );
      }
    );
  }

  searchUser(searchText) {
    clearTimeout(this.timeout);
    var $this = this;
    this.timeout = setTimeout(function () {
      if (searchText.keyCode != 13) {
        $this.loading = true
        $this.searchSocket(searchText.target.value);
      }else{
        $this.loading = true
        $this.getUserFilteredData("next")
      }
    }, 1000);
  }

  public searchSocket(searchText) {
    this.searchText = searchText.toString();
    if(this.searchText == ''){
      this.loading = true
      this.setPage(1)
      return
    }
    this.userFilteredData = []
    this.setPage(1)
  }


  async selectUser(user){
    // console.log("user: ",user)
    // this.allUsers
    
    // console.log("userFilteredData: ",this.userFilteredData)

    // let newUser = {
    //       coins: user?.coins,
    //       commentsCount: user?.commentsCount,
    //       createdAt: user?.createdAt,
    //       earnCount: user?.earnCount,
    //       followersCount: user?.followersCount,
    //       followingCount: user?.followingCount ,
    //       isVerified: user?.isVerified,
    //       lastStoryCreated: user?.lastStoryCreated,
    //       likesCount: user?.likesCount, 
    //       photoColor: user?.photoColor,
    //       postCount: user?.postCount,
    //       untrusted: user?.untrusted,
    //       updatedAt: user?.updatedAt,
    //       _id: user?._id
    // }

    if(this.allUsers.some(e => e._id === user._id)){
      this.toastrService.error('This user is already selected');
    }else{

      user['checked'] = true
      this.selectedUsersId = [user._id,...this.selectedUsersId]
      this.selectedUsers = [user,...this.selectedUsers]
      this.userFilteredData = [user,...this.userFilteredData]
      this.allUsers = [user,...this.allUsers]
      // this.save(user)
    }
    this.modalReference.close();
  }


  save(){
    console.log("save")
    this.userService.changeFinalUserFiltered(
      {
        userFilteredData: this.userFilteredData,
        allUsers: this.allUsers,
        checkboxFalseUsersId: this.checkboxFalseUsersId
      })
      this.modalService.dismissAll()
  }


}
