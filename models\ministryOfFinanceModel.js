const mongoose = require('mongoose');
const { MongooseFindByReference } = require('mongoose-find-by-reference');

const ministryOfFinanceSchema = new mongoose.Schema({
  creditsAmount: {
    type: Number,
    default: 0
  },
  type: {
    type: String,
    enum: ['convert-credits-to-real-money'],
    required: [true, 'Not possible ministry Of Finance transaction without type of transaction selected!']
  },
  inflowCredits: {
    type: Boolean,
    default: false
  },
  outflowCredits: {
    type: Boolean,
    default: false
  },
  active: {
    type: Boolean,
    default: true
  },
  transaction:{
    type: mongoose.Schema.ObjectId,
    ref: 'Transaction',
    required: [true, 'must have transaction created before central bank transaction!']
  },
  createdBy:{
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: [true, 'must been created from a User!']
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});


ministryOfFinanceSchema.pre(/^find/, async function(next) {
    // this.populate({
    //     path: 'createdBy',
    // });
  next();
});

ministryOfFinanceSchema.plugin(MongooseFindByReference);

const MinistryOfFinance = mongoose.model('MinistryOfFinance', ministryOfFinanceSchema);

module.exports = MinistryOfFinance;