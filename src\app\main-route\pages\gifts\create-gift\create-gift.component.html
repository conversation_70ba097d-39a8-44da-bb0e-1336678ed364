<div style="overflow: auto" class="projects-section projects-create-gift">
  <div
    style="
      display: flex;
      flex-direction: column;
      overflow: auto;
      flex-grow: 1;
      height: 100%;
      border-radius: 15px;
      overflow-x: hidden;
      gap: 1em;
    "
  >
    <div style="flex-grow: 1; border-radius: 15px; overflow-y: auto">
      <div>
        <input
          class="input-image"
          #fileInputThumbnail
          accept="image/*"
          type="file"
          (change)="selectThumbnail($event)"
        />
        <input
          class="input-image"
          #fileInputAnimation
          accept="image/*"
          type="file"
          (change)="selectAnimation($event)"
        />
        <div style="display: flex; gap: 2em">
          <div
            *ngIf="!thumbnail"
            (click)="fileInputThumbnail.click()"
            class="input-image-create-gift"
          >
            <img
              style="height: 35px; width: 35px"
              src="assets/icons/importThumbnail.svg"
              alt=""
            />
            <span>Click to upload</span>
          </div>
          <div *ngIf="thumbnail" class="input-image-create-gift">
            <div
              style="
                aspect-ratio: 1/1;
                height: 100%;
                position: relative;
                display: flex;
                align-items: center;
              "
            >
              <img style="width: 100%" [src]="thumbnail" alt="" />
              <img
                (click)="thumbnail = null"
                style="
                  height: 20px;
                  width: 20px;
                  position: absolute;
                  top: 0;
                  right: 1em;
                "
                src="assets/icons/close.svg"
                alt=""
              />
            </div>
          </div>

          <div
            *ngIf="!animation"
            (click)="fileInputAnimation.click()"
            class="input-image-create-gift"
          >
            <img
              style="height: 40px; width: 40px"
              src="assets/icons/importAn.svg"
              alt=""
            />
            <span>Click to upload</span>
          </div>
          <div *ngIf="animation" class="input-image-create-gift">
            <div style="aspect-ratio: 9/16; height: 100%; position: relative">
              <img style="height: 100%; width: 100%" [src]="animation" alt="" />
              <img
                (click)="animation = null"
                style="
                  height: 20px;
                  width: 20px;
                  position: absolute;
                  top: 0;
                  right: 0;
                "
                src="assets/icons/close.svg"
                alt=""
              />
            </div>
          </div>
        </div>

        <div style="display: flex; width: 100%; gap: 1em">
          <div
            style="
              background: var(--sidebar);
              border-radius: 14px;
              margin-top: 1em;
              padding: 1rem 0.7rem;
              display: flex;
              flex-direction: column;
              gap: 1em;
              width: 100%;
            "
          >
            <div class="input-create-gift">
              <span>Gift Title</span>
              <input
                [(ngModel)]="title"
                autocomplete="false"
                placeholder="Write name of gift..."
              />
            </div>
          </div>
        </div>

        <div style="display: flex; width: 100%; gap: 1em">
          <div
            style="
              background: var(--sidebar);
              border-radius: 14px;
              margin-top: 1em;
              padding: 1rem 0.7rem;
              display: flex;
              flex-direction: column;
              gap: 1em;
              width: 100%;
            "
          >
            <div class="input-create-gift">
              <span>Gift Cost</span>
              <input
                [(ngModel)]="cost"
                autocomplete="false"
                type="number"
                placeholder="Write const of the gift..."
              />
            </div>
          </div>
        </div>
        <div style="display: flex; width: 100%; gap: 1em">
          <div
            style="
              background: var(--sidebar);
              border-radius: 14px;
              margin-top: 1em;
              padding: 1rem 0.7rem;
              display: flex;
              flex-direction: column;
              gap: 1em;
              width: 100%;
            "
          >
            <div class="input-create-gift">
              <span>Gift Duration</span>
              <input
                [(ngModel)]="duration"
                autocomplete="false"
                type="number"
                placeholder="Write const of the gift..."
              />
            </div>
          </div>
        </div>
        <div style="display: flex; align-items: center; gap: 1rem">
          <div style="display: flex; width: 100%; gap: 1em">
            <div
              style="
                background: var(--sidebar);
                border-radius: 14px;
                margin-top: 1em;
                padding: 1rem 0.7rem;
                display: flex;
                flex-direction: column;
                gap: 1em;
                width: 100%;
              "
            >
              <div class="input-create-gift">
                <span>Old ID</span>
                <input
                  [(ngModel)]="oldID"
                  autocomplete="false"
                  type="string"
                  placeholder="Old order number of the gift..."
                />
              </div>
            </div>
          </div>
          <div style="display: flex; width: 100%; gap: 1em">
            <div
              style="
                background: var(--sidebar);
                border-radius: 14px;
                margin-top: 1em;
                padding: 1rem 0.7rem;
                display: flex;
                flex-direction: column;
                gap: 1em;
                width: 100%;
              "
            >
              <div class="input-create-gift">
                <span>Order Index</span>
                <input
                  [(ngModel)]="orderIndex"
                  autocomplete="false"
                  type="number"
                  placeholder="Write order number of the gift..."
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div
    style="
      display: flex;
      border-radius: 20px;
      overflow: auto;
      aspect-ratio: 9/6;
    "
  >
    <!-- first screen mobile -->
    <div
      style="
        background-image: url(assets/iphone.svg);
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        aspect-ratio: 7/18;
      "
    >
      <div
        style="
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 60%;
        "
      >
        <div
          style="
            height: 92%;
            width: 100%;
            margin: 20px 0 0 0;
            border-radius: 0 0 35px 35px;
            display: flex;
            flex-direction: column;
            gap: 1em;
            align-items: center;
            max-height: 670px;
          "
        >
          <div
            style="
              display: flex;
              align-items: center;
              padding: 0 1.5em;
              gap: 10px;
              margin-top: 5px;
              margin-bottom: 10px;
              width: 100%;
              align-items: center;
            "
          >
            <img
              style="height: 40px; width: 40px; border-radius: 50%"
              src="/assets/user2.jpg"
              alt=""
            />
            <div style="display: grid">
              <span>{{ me?.name }} {{ me?.surname }}</span>
              <span style="font-size: 11px; opacity: 0.8">
                {{ me?.email }}
              </span>
            </div>
            <img
              style="height: 15px; width: 15px; margin-left: auto"
              src="/assets/icons/close.svg"
              alt=""
            />
          </div>
          <br />
          <div
            style="
              display: grid;
              overflow: auto;
              grid-template-columns: 1fr 1fr;
              justify-content: center;
              padding-top: 3em;
              width: 93%;
              gap: 3px;
            "
          >
            <div
              [ngClass]="{
                '': cost < 3,
                mediumTier: cost >= 3 && cost < 10,
                premiumTier: cost >= 10
              }"
              *ngIf="thumbnail"
              class="gifts premium"
            >
              <img class="giftImage" [src]="thumbnail" alt="" />
              <div class="giftDetails">
                <div class="coinValue">
                  <img src="/assets/goldcoinFiber.png" alt="" />
                  <p class="no-wrap-1-line">{{ cost }} credits</p>
                </div>
                <div class="coinValue">
                  <p style="margin: 0" class="giftTitle no-wrap-1-line">
                    {{ title }}
                  </p>
                </div>
              </div>
              <div>
                <span class="sendGiftButton">Send</span>
              </div>
            </div>
            <div *ngIf="!thumbnail" class="giftsSklet">
              <div
                style="height: 62px; width: 62px"
                class="lowTierImgSklet giftImageSklet"
              ></div>

              <div class="giftDetailsSklet">
                <div class="coinValueSklet">
                  <p
                    style="width: 100%; height: 14px; margin: 0"
                    class="giftTitle skeleton1"
                  ></p>
                </div>
                <div class="coinValueSklet">
                  <p
                    style="width: 100%; height: 14px; margin: 0"
                    class="giftTitle skeleton1"
                  ></p>
                </div>
              </div>
              <div>
                <span class="sendGiftButtonSklet skeleton1">
                  <span class="no-wrap-1-line" style="opacity: 0"> Send </span>
                </span>
              </div>
            </div>
            <div *ngFor="let i of [].constructor(3)" class="giftsSklet">
              <div
                style="height: 62px; width: 62px"
                class="lowTierImgSklet giftImageSklet"
              ></div>

              <div class="giftDetailsSklet">
                <div class="coinValueSklet">
                  <p
                    style="width: 100%; height: 14px; margin: 0"
                    class="giftTitle skeleton1"
                  ></p>
                </div>
                <div class="coinValueSklet">
                  <p
                    style="width: 100%; height: 14px; margin: 0"
                    class="giftTitle skeleton1"
                  ></p>
                </div>
              </div>
              <div>
                <span class="sendGiftButtonSklet skeleton1">
                  <span class="no-wrap-1-line" style="opacity: 0"> Send </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Second screen mobile -->
    <div
      style="
        background-image: url(assets/iphone.svg);
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        aspect-ratio: 7/18;
      "
    >
      <div
        style="
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 60%;
        "
      >
        <div
          style="
            height: 92%;
            width: 100%;
            margin: 20px 0 0 0;
            border-radius: 0 0 35px 35px;
            display: flex;
            flex-direction: column;
            gap: 1em;
            align-items: center;
            max-height: 670px;
            position: relative;
          "
        >
          <div
            style="
              display: flex;
              align-items: center;
              padding: 0 1.5em;
              gap: 5px;
              margin-top: 5px;
              width: 100%;
              align-items: center;
              z-index: 999;
              position: relative;
            "
          >
            <div style="display: grid">
              <span>{{ me?.name }} {{ me?.surname }}</span>
            </div>
            <span
              style="margin-left: auto; font-size: 10px; font-weight: 400"
              >{{ today | date : "EEEE, MMMM d" }}</span
            >
            <img
              style="height: 15px; width: 15px"
              src="/assets/icons/close.svg"
              alt=""
            />
          </div>
          <div
            style="
              display: flex;
              align-items: center;
              padding: 0 1.5em;
              gap: 5px;
              margin-top: 5px;
              width: 100%;
              align-items: center;
              z-index: 999;
              position: relative;
            "
          >
            <div style="width: 100%" class="topNavigation">
              <div class="tab likes">
                <p class="no-wrap-1-line" style="margin: 0">10 Likes</p>
              </div>
              <div class="tab comments">
                <p class="no-wrap-1-line" style="margin: 0">10 Comments</p>
              </div>
              <div class="tab giftsTab selectedTab">
                <p class="no-wrap-1-line" style="margin: 0">10 Gift</p>
              </div>
            </div>
            <img
              style="width: 24px"
              src="assets/icons/Button-like.svg"
              alt=""
            />
          </div>
          <div
            style="
              display: flex;
              flex-wrap: wrap;
              overflow: auto;
              justify-content: center;
              padding-top: 2em;
            "
          >
            <div *ngIf="thumbnail" class="giftsSent">
              <img [src]="thumbnail" alt="" />
              <div class="giftDetails">
                <div class="setBy">
                  <p></p>
                  <p>{{ me?.name }} {{ me?.surname }}</p>
                </div>
                <div class="setBy">
                  <p></p>
                  <p>{{ cost }} credits</p>
                </div>
              </div>
              <p class="timeSent">{{ today | date : "EEEE, MMMM d" }}</p>
            </div>
            <div *ngIf="!thumbnail" class="giftsSent">
              <div
                style="height: 32px; width: 32px; min-width: 32px"
                class="skeleton2 giftImageSentSklet"
              ></div>

              <div class="giftDetailsSklet">
                <div class="coinValueSklet">
                  <p
                    style="width: 100%; height: 14px; margin: 0"
                    class="giftTitle skeleton1"
                  ></p>
                </div>
                <div class="coinValueSklet">
                  <p
                    style="width: 100%; height: 14px; margin: 0"
                    class="giftTitle skeleton1"
                  ></p>
                </div>
              </div>
              <div style="margin-left: auto">
                <span class="sendGiftButtonSklet skeleton1">
                  <span style="opacity: 0"> Send </span>
                </span>
              </div>
            </div>
            <div *ngFor="let i of [].constructor(3)" class="giftsSent">
              <div
                style="height: 32px; width: 32px; min-width: 32px"
                class="skeleton2 giftImageSentSklet"
              ></div>

              <div class="giftDetailsSklet">
                <div class="coinValueSklet">
                  <p
                    style="width: 100%; height: 14px; margin: 0"
                    class="giftTitle skeleton1"
                  ></p>
                </div>
                <div class="coinValueSklet">
                  <p
                    style="width: 100%; height: 14px; margin: 0"
                    class="giftTitle skeleton1"
                  ></p>
                </div>
              </div>
              <div style="margin-left: auto">
                <span class="sendGiftButtonSklet skeleton1">
                  <span style="opacity: 0"> Send </span>
                </span>
              </div>
            </div>
          </div>
          <div style="height: 100%; width: 100%; position: absolute">
            <img
              *ngIf="animation"
              style="height: 100%; width: 100%; border-radius: 40px"
              [src]="animation"
              alt=""
            />
          </div>
          <div
            style="
              position: absolute;
              bottom: 1rem;
              right: 1rem;
              height: 40px;
              width: 40px;
              border-radius: 50%;
              color: white;
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: var(--action-color);
              font-size: 28px;
            "
          >
            <img
              style="height: 23px; width: 23px"
              src="assets/icons/Button-gift1.svg"
              alt=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div style="display: flex; gap: 1em">
    <button (click)="redirectTo('list-gifts')">Back</button>
    <span *ngIf="editMode" (click)="edit()" class="buttonReset"> Edit </span>
    <span *ngIf="!editMode" (click)="create()" class="buttonReset">
      Create
    </span>
  </div>
</div>
