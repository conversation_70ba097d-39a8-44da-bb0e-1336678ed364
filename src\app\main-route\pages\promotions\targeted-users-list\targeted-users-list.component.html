<ngx-loading
[(show)]="loading"
[config]="{
animationType: ngxLoadingAnimationTypes.circleSwish,
primaryColour: '#ffffff',
backdropBorderRadius: '3px'
}"
></ngx-loading>

<div class=".page-wrapper .page-body-wrapper .page-body">
  <div class="container-fluid">
    <div class="row">
            <div class="col-lg-6">
                <div class="page-header-left">
                    <p style="font-size: 26px; color:black ;font-weight: 800; margin: 1.5em 0;">{{promotionsAvailableCount}} - Targeted Users </p>
                </div>
            </div>
      <div class="col-sm-12">
        <div class="card">
          <div class="card-body">
            <div class="bg-inner cart-section order-details-table">
              <div class="row g-4">
                <div class="col-xl-12">
                
                  <div class="card-header">
                    <form class="form-inline search-form search-box">
                        <div class="form-group">
                            <input class="form-control-plaintext" type="search" placeholder="Search..">
                        </div>
                    </form>
                </div>
                  <div class="table-responsive table-details">
                    <table class="table cart-table table-borderless">
                      <tbody>
                        <tr 
                        *ngFor="let data of promotionsAvailable ; let i = index"
                        class="table-order">
                          <td>
                              <img
                              *ngIf="!data?.userAllowed?.photoProfile?.data"
                              class="list-image" src="assets/no_picture_fiber.svg" alt="">
                              <app-img
                                *ngIf="data?.userAllowed?.photoProfile?.data"
                                [(buffer)]="data.userAllowed.photoProfile.data"
                                [class]="'list-image'"
                              ></app-img>
                          </td>
                          <td>
                            <h5>{{data?.userAllowed?.name}} {{data?.userAllowed?.surname}}</h5>
                          </td>
                          <td>
                            <h5>{{data?.timeStart | date: 'h:mm a' }}</h5>
                          </td>
                          <td>
                            <h5>{{data?.timeEnd | date: 'h:mm a' }}</h5>
                          </td>
                          <td>
                            <p>Shared</p>
                            <div
                              *ngIf="data?.isShared"
                              class="order-success mt-2">
                                  <span>Successful</span>
                              </div>
                            <div 
                              *ngIf="!data?.isShared"
                              class="order-warning mt-2">
                                <span>NO</span>
                            </div>
                          </td>
                          <td>
                            <p>11th Notification</p>
                              <div
                              *ngIf="data?.nativeNotificationSendStatus"
                              class="order-success mt-2">
                                  <span>Successful</span>
                              </div>
                              <div 
                                *ngIf="!data?.nativeNotificationSendStatus"
                                class="order-warning mt-2">
                                  <span>NO</span>
                              </div>
                          </td>
                          <td>
                            <p>Native Missed Sent Notification</p>
                            <div
                            *ngIf="data?.nativeMissedNotificationSendStatus"
                            class="order-success mt-2">
                                <span>Successful</span>
                            </div>
                            <div 
                              *ngIf="!data?.nativeMissedNotificationSendStatus"
                              class="order-warning mt-2">
                                <span>NO</span>
                            </div>
                          </td>
                          <td>
                           <div
                           (click)="onRemove(data, i, !data.isActive)">
                            <div 
                            *ngIf="data.isActive"
                            class="button " >
                                <p style="color: white;" class="text-center p-2">Remove</p>
                            </div>
                           </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
            <!-- section end -->
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
