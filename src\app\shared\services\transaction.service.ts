import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { SocketService } from './socket.service';
// const BACKEND_URL = environment.apiUrl + "/transaction/";
const BACKEND_URL = environment.base_url  + "/transaction/"
@Injectable({
  providedIn: 'root'
})
export class TransactionService {

  socket
  constructor(
    private http: HttpClient,
    private socketService: SocketService
  ) {
    this.socket = this.socketService.setupSocketConnection();
  }

  createTransaction(
    userId,
    senderId,
    receiverId,
    total,
    reason,
    senderOperationLog,
    receiverOperationLog,
    fiberFee,
    isBuyingGiftTransaction,
    isSellingGiftTransaction,
    isPayPerViewTransaction,
    isPromotionSharingTransaction,
    isTransferTransaction
  ){
    this.socket.emit('createBalance', {
      userId: userId.toString(),
      senderId: senderId?.toString(),
      receiverId: receiverId?.toString(),
      total: total,
      reason: reason,
      senderOperationLog: senderOperationLog,
      receiverOperationLog: receiverOperationLog,
      fiberFee: fiberFee,
      isBuyingGiftTransaction: isBuyingGiftTransaction,
      isSellingGiftTransaction: isSellingGiftTransaction,
      isPayPerViewTransaction: isPayPerViewTransaction,
      isPromotionSharingTransaction: isPromotionSharingTransaction,
      isTransferTransaction: isTransferTransaction
    });
  }


  getAll(page,resultsPerPage){
    let data = {
      page : page,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL+'getAll',data)
  }


  searchTransaction(searchText,_id,pageNumber,resultsPerPage){
    let data = {
      userId: _id,
      searchText : searchText,
      page : pageNumber,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL + 'searchTransaction',data)
  }

}
