<div class="projects-section bloggers-section">
    <div
      style="
        /* display: none !important; */
        display: grid;
        grid-template-rows: 4em 2em 1fr 4em;
        background-color: var(--sidebar);
        padding: 5px 1em;
        border-radius: 20px;
        height: 76vh;
      "
    >
      <div style="gap: 1em" class="disp-flex a-i-center">
        <span class="no-wrap-1-line"> Banned Suggested Users </span>
        <div style="margin-left: auto; max-width: 200px;" class="search-bar">
          <input 
          (keyup)="searchUser($event)"
          type="text" placeholder="Search" />
            <!-- (keyup)="searchUser($event)" -->
            <!-- <input type="text" placeholder="Search for user..." /> -->
          </div>
        <span
          (click)="openUserSearchModal(searchContent)"
          style="
            height: 30px;
            width: 30px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 30px;
            background-color: var(--action-color);
            margin-bottom: 1px;
            cursor: pointer;
          "
          >+</span
        >
      </div>
      <div
        style="
          display: grid;
          grid-template-columns: 1fr 2fr 0.5fr;
          background-color: rgba(26, 37, 59, 0.5);
          align-items: center;
          margin-bottom: 5px;
          border-radius: 10px;
          gap: 1em;
        "
      >
        <span style="margin-left: 1em" class="no-wrap-1-line">User Details</span>
        <span class="no-wrap-1-line"></span>
        <span class="no-wrap-1-line">Action</span>
      </div>
      <div
      *ngIf="data.length === 0 && !loading"
      style="
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
      "
    >
      <img
        style="width: 70%; max-width: 299px"
        src="/assets/icons/animatedIconsTable/list_empty.svg"
        alt=""
      />
      <p style="font-size: 16px">List is empty</p>
      </div>
      <ul style="padding-inline-start: 0px; overflow: auto" class="">
        <ng-container
        *ngIf="loading">
          <li
            *ngFor="let i of [].constructor(15)"
            style="
              display: grid;
              grid-template-columns: 1fr 2fr 0.5fr;
              align-items: center;
              min-height: 4em;
              gap: 1em;
              border-bottom: 1px solid rgba(26, 37, 59, 0.5);
            "
          >
            <div style="gap: 5px; align-items: center" class="disp-flex">
              <span
                class="skeleton2"
                style="
                  font-size: 14px;
                  font-weight: 500;
                  opacity: 0.7;
                  width: 40px;
                  height: 40px;
                  border-radius: 50%;
                "
              ></span>
              <span
                class="skeleton2"
                style="
                  font-size: 14px;
                  font-weight: 500;
                  opacity: 0.7;
                  width: 111px;
                  height: 16px;
                "
              ></span>
            </div>
            <div style="gap: 8px" class="disp-flex">
            </div>
            <div style="justify-content: center" class="disp-grid">
              <span
                class="skeleton2"
                style="
                  font-size: 14px;
                  font-weight: 500;
                  opacity: 0.7;
                  width: 25px;
                  height: 13px;
                "
              ></span>
            </div>
          </li>
        </ng-container>
        <li
          *ngFor="let dat of data"
          style="
            display: grid;
            grid-template-columns: 1fr 2fr 0.5fr;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div class="disp-flex gap-05">
            <app-avatar-photo
              [buffer]="dat?.user?.photoProfile?.data"
              [userId]="dat?.user?._id"
              [circleColor]="dat?.user?.photoColor"
              [name]="dat?.user?.name"
              [surname]="dat?.user?.surname"
              [class]="'userImgBloggers'"
              [classAvatarInitials]="'initialsClass'"
            ></app-avatar-photo>
            <img
            *ngIf="dat?.user?.isVerified"
              style="
                height: 15px;
                width: 15px;
                position: absolute;
                bottom: 0;
                right: 0;"
              src="/assets/icons/fiberVerified.svg"
              alt=""
            />
            <img
            *ngIf="dat?.user?.untrusted"
              style="
                height: 15px;
                width: 15px;
                position: absolute;
                bottom: 0;
                right: 0;"
              src="/assets/icons/fiberUnverified.svg"
              alt=""
            />
            <div class="disp-grid a-i-center">
              <span style="font-size: 16px" class="no-wrap-1-line">
                {{ dat?.user?.name }} {{ dat?.user?.surname }}
              </span>
            </div>
          </div>
          <div style="gap: 0.5em" class="disp-flex">

          </div>
  
          <div style="gap: 1em" class="disp-flex j-c-center a-i-center">
            <img
              (click)="removeUser(dat?.user?._id)"
              style="
                position: relative;
                height: 15px;
                width: 15px;
                border-radius: 50%;
              "
              src="/assets/icons/close.svg"
              alt=""
            />
          </div>
        </li>
      </ul>
      <div class="list-number disp-flex" style="">
        <div
          class="showingInfoWrapper"
          style="
            margin: 0 0 0.75rem;
            display: flex !important;
            align-items: center;
            gap: 1em;
          "
        >
          <span class="showingInfo"
            >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
            {{ pageNo * resultsPerPage - (resultsPerPage - data.length) }} of
            {{ count }}</span>
          <div style="border: 1px solid gray; border-radius: 15px">
            <div>
              <input style="display: none" id="dropdownInput" type="checkbox" />
              <div
                style="
                  display: flex;
                  justify-content: center;
                  height: 45px;
                  width: 50px;
                "
                class="dropdown"
              >
                <select
                  style="
                    display: flex;
                    background: transparent;
                    border: none;
                    color: white;
                    font-size: 18px;
                    width: 90%;
                    font-weight: 600;
                  "
                  (ngModelChange)="resultsPerPageChanged($event)"
                  [(ngModel)]="resultsPerPage"
                >
                  <option class="optionStyle colorCancel">7</option>
                  <option class="optionStyle colorCancel">10</option>
                  <option class="optionStyle colorCancel">12</option>
                  <option class="optionStyle colorCancel">15</option>
                  <option class="optionStyle colorCancel">17</option>
                </select>
                <label for="dropdownInput" class="overlay"></label>
              </div>
            </div>
          </div>
        </div>
        <nav>
          <ul class="pager">
            <li class="pager__item pager__item--prev">
              <a
                *ngIf="pageNo !== 1"
                (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
                style="width: fit-content !important; padding: 0 10px"
                class="pager__link"
              >
                Previous
              </a>
            </li>
            <li *ngIf="pageNo !== 1" class="pager__item">
              <a (click)="setPage(1)" class="pager__link">...</a>
            </li>
            <li
              *ngFor="
                let item of [].constructor(pageNoTotal) | slice : 0 : 5;
                let i = index
              "
              [ngClass]="pageNo + i === pageNo ? 'active' : ''"
              class="pager__item"
            >
              <a
                *ngIf="pageNo + i <= pageNoTotal"
                (click)="setPage(pageNo + i)"
                class="pager__link"
                >{{ pageNo + i }}</a
              >
            </li>
            <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
              <a (click)="setPage(pageNoTotal)" class="pager__link">...</a>
            </li>
            <li
              *ngIf="pageNo !== pageNoTotal"
              class="pager__item pager__item--next"
            >
              <a
                (click)="pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''"
                style="width: fit-content !important; padding: 0 10px"
                class="pager__link"
              >
                Next
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
    
    <ng-template #searchContent let-modal>
      <app-search-content
        [(me)]="me"
        (closeModal)="modal.dismiss('Cross click')"
        (userSelected)="selectUser($event)"
      ></app-search-content>
    </ng-template>
  </div>
  