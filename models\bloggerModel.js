const mongoose = require('mongoose');
const { MongooseFindByReference } = require('mongoose-find-by-reference');

const bloggerSchema = new mongoose.Schema(
  {
      user: {
        type: mongoose.Schema.ObjectId,
        ref: 'User',
        required: [true, 'Blogger must been created from a User!'],
        unique: true
      },
      posts:[{
        type: mongoose.Schema.ObjectId,
        ref: 'Post',
        required: [true, 'Blogger must have post to show!']
      }],
      createdAt: {
        type: Date,
        default: Date.now
      }
  }

);

bloggerSchema.pre('save', async function( next) {
    next();
});
  
bloggerSchema.pre(/^find/, function(next) {
    next();
  })
bloggerSchema.plugin(MongooseFindByReference);

const Blogger = mongoose.model('Blogger', bloggerSchema);

module.exports = Blogger;
