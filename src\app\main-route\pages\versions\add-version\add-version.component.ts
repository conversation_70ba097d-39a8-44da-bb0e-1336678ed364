import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { AppVersionService } from 'src/app/shared/services/app-version.service';
import { AuthService } from 'src/app/shared/services/auth.service';
import { CompressImageService } from 'src/app/shared/services/compress-image.service';
import { ImageService } from 'src/app/shared/services/image.service';
import { environment } from 'src/environments/environment';
const BACKEND_Image_URL = environment.imageUrl + '/post/getImage/';

@Component({
  selector: 'app-add-version',
  templateUrl: './add-version.component.html',
  styleUrls: ['./add-version.component.css']
})
export class AddVersionComponent implements OnInit {

  thumbnailFile: any = []; 
  thumbnail = null;
  oldThumbnail = null;
  thumbnailImageId = null;

  appVersion = ''
  description = ''

  appStore = false
  googlePlay  = false
  mandatoryToUpdate  = false
  enabled = false

  startLoading = false
  loading = false
  user: User;

  id = ''
  editMode = false

  constructor(
    private imageService: ImageService,
    private compressImageService: CompressImageService,
    private toastrService: ToastrService,
    private appVersionService: AppVersionService,
    private authService: AuthService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) { 
    this.appVersion = ''
    this.description = ''
    this.appStore = false
    this.googlePlay  = false
    this.mandatoryToUpdate  = false
    this.thumbnailFile = []; 
    this.thumbnail = null;
    this.thumbnailImageId = null;            
    this.startLoading = false
    this.editMode = false
    this.id = null
    this.oldThumbnail = null
  }

  ngOnInit(): void {
    this.authService.user.pipe().subscribe( async appUser => {
      this.user = appUser
    })
    this.activatedRoute.params.subscribe( async paramMap => {
      this.appVersion = ''
      this.description = ''
      this.appStore = false
      this.googlePlay  = false
      this.mandatoryToUpdate  = false
      this.thumbnailFile = []; 
      this.thumbnail = null;
      this.thumbnailImageId = null;            
      this.startLoading = false
      this.editMode = false
      this.id = null
      this.oldThumbnail = null
      if(paramMap['id']){
          this.id = paramMap['id']
          this.getData(this.id)
      }
    })
  }

  getData(id){
    this.editMode = true
    this.appVersionService.getData(id).subscribe(
      async (result) => {
        if (result.status == 'success') {

          console.log("getData: ",result)
          this.appVersion = result?.data?.current
          this.description = result?.data?.description
         
          this.appStore = result?.data?.appStore
          this.googlePlay  = result?.data?.googlePlay
          this.mandatoryToUpdate  = result?.data?.mandatoryToUpdate
          this.enabled = result?.data?.enabled

          if(result?.data?.thumbnailImageId){
            this.thumbnailImageId = result?.data?.thumbnailImageId;
            this.thumbnail = `${BACKEND_Image_URL}${result?.data?.thumbnailImageId.toString()}`;
            this.oldThumbnail = this.thumbnail
          }      
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }

  submit(){
    // this.signUpForm.value.name
    console.log("appVersion: ",this.appVersion )
    if(this.startLoading === true){
      return
    }
    this.startLoading = true
    if(!this.thumbnailFile){
      this.toastrService.error('No File Selected');    
      return        
    }
    if(this.appVersion === ''){
      this.toastrService.error('App Version cannot be empty');    
      return        
    }
    if(this.description === ''){
      this.toastrService.error('Description cannot be empty');    
      return        
    }
    if(this.thumbnail === this.oldThumbnail){
      if(this.editMode === false){
        this.create()
      }
      if(this.editMode === true){
        this.edit()
      }
    }

    if(this.thumbnail !== this.oldThumbnail){
       this.imageService.uploadOriginalImage(
        this.thumbnailFile
      ).subscribe(  respond  => {
        console.log("uploadOriginalImage: ",respond)
              if( respond.status = "success"){
                    console.log("uploadOriginalImage: ",respond.data._id.toString())
                    this.thumbnail = `${BACKEND_Image_URL}${respond.data._id.toString()}`;
                    this.thumbnailImageId = respond.data._id
                    if(this.editMode === false){
                      this.create()
                    }
                    if(this.editMode === true){
                      this.edit()
                    }
              }
          },
          respond_error => {
            this.startLoading = false
              let error_message = respond_error.error.message;
              this.toastrService.error(error_message);            
          }
      );
    }
    


    

  }

  create(){
    let data = {
      current: this.appVersion, 
      description: this.description,
      thumbnailImageId: this.thumbnailImageId,
      enabled: this.enabled,
      mandatoryToUpdate: this.mandatoryToUpdate,
      googlePlay: this.googlePlay,
      appStore: this.appStore,
      msg: {
        title: 'App maintenance',
        msg: 'We are currently improving your app experince.Please try again later..',
        btn: 'Ok',
      },
      majorMsg: {
        title: 'Important App update',
        msg: 'Please update your app to the latest version to continue using it.',
        btn: 'Download',
      },
      minorMsg: {
        title: 'App update available',
        msg: 'There is a new version available, would you like to get it now?',
        btn: 'Download',
      },
      user: this.user._id
    }
    this.appVersionService.create(   
      data
    ).subscribe( respond  => {
            if( respond.status = "success"){
  
              this.toastrService.success( "Successfully created new" );
              this.appVersion = ''
              this.description = ''
              this.appStore = false
              this.googlePlay  = false
              this.mandatoryToUpdate  = false
              this.thumbnailFile = []; 
              this.thumbnail = null;
              this.thumbnailImageId = null;            
              this.startLoading = false
              this.editMode = false
              this.id = null
              this.oldThumbnail = null
              this.redirectTo('list-version')
            }
        },
        respond_error => {
          this.startLoading = false
          let error_message = respond_error.error.message;
          this.toastrService.error(error_message);
        }

      ); 
  }

  edit(){
    let data = {
      current: this.appVersion, 
      description: this.description,
      thumbnailImageId: this.thumbnailImageId,
      enabled: this.enabled,
      mandatoryToUpdate: this.mandatoryToUpdate,
      googlePlay: this.googlePlay,
      appStore: this.appStore,
      user: this.user._id,
      updatedAt: new Date()
    }
    this.appVersionService.edit(   
      {data:data},
      this.id
    ).subscribe( respond  => {
            if( respond.status = "success"){
  
              this.toastrService.success( "Successfully updated" );
              this.appVersion = ''
              this.description = ''
              this.appStore = false
              this.googlePlay  = false
              this.mandatoryToUpdate  = false
              this.thumbnailFile = []; 
              this.thumbnail = null;
              this.thumbnailImageId = null;            
              this.editMode = false
              this.id = null
              this.oldThumbnail = null
              this.startLoading = false
              this.redirectTo('list-version')
            }
        },
        respond_error => {
          this.startLoading = false
          let error_message = respond_error.error.message;
          this.toastrService.error(error_message);
        }

      ); 
  }

  selectThumbnail(event) {
    console.log("thumbnailFile")
    if(event.target.files[0]){
      this.compressImageService.compress(event.target.files[0]).subscribe( result =>{
        this.thumbnailFile = [...this.thumbnailFile,result] 
      })
      var reader = new FileReader();
      reader.readAsDataURL(event.target.files[0])
      reader.onload = (_event) => {
        this.compressImageService.compressImage(reader.result,800,800).then(compressed => {
          this.thumbnail = compressed;
        })
      }
  
    }

  }


  changeInput(event,type){
    if(type === 'mandatoryToUpdate'){
      this.mandatoryToUpdate = event
    }
    if(type === 'googlePlay'){
      this.googlePlay = event
    }
    if(type === 'appStore'){
      this.appStore = event
    }
    if(type === 'enabled'){
      this.enabled = event
    }
    console.log("changeInput: ",event)
  }

  redirectTo(uri:string){
    this.router.navigateByUrl('/en/'+ uri)
      .then(() =>{
      });
  }

}
