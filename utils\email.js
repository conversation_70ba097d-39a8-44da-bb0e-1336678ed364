const nodemailer = require('nodemailer');
const htmlToText = require('html-to-text');
const welcomeEmail = require('./../views/emails/welcome');
const passwordReset = require('./../views/emails/passwordReset');
const changeRole = require('./../views/emails/change-role');

const SENDGRID_USERNAME = 'apikey'
const SENDGRID_PASSWORD = '*********************************************************************'
const EMAIL_FROM =  '<EMAIL>'

module.exports = class Email {
  constructor(user, url) {
    this.user = user
    this.to = user.email;
    this.firstName = user.name.split(' ')[0];
    this.url = url;
    this.from = `Fiber <${EMAIL_FROM}>`;
  }

  newTransport() {
    console.log("SendGrid")
      return nodemailer.createTransport({
        service: 'SendGrid',
        auth: {
          user: SENDGRID_USERNAME,
          pass: SENDGRID_PASSWORD
        }
      });
  }

  // Send the actual email
  async send(template, subject) {
    // 1) Render HTML based on  template
    var html = '<p>Not Found</p>'

    if(template === 'welcome'){
      html = welcomeEmail(this.firstName,this.to);
    }else if(template === 'passwordReset'){
      html = passwordReset(this.firstName, this.url, this.to);
    }else if(template === 'changeRole'){
      // html = changeRole(this.user);
    }

    // 2) Define email options
    const mailOptions = {
      from: this.from,
      to: this.to,
      subject,
      html,
      text: htmlToText.fromString(html)
    };



    // 3) Create a transport and send email
    await this.newTransport().sendMail(mailOptions);

  }

  async sendWelcome() {
    await this.send('welcome', 'Welcome to the Fiber Family!');
  }

  async sendPasswordReset() {
    console.log("passwordReset")

    await this.send(
      'passwordReset',
      'Your password reset token (valid for only 10 minutes)'
    );
  }


  async sendChangeRole(){
    await this.send(
      'changeRole',
      'Your role has been change'
    );
  }
  
};
