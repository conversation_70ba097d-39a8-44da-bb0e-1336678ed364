<div class="projects-section exchange-section">
  <div
    style="
      grid-template-rows: 1fr 2fr 1fr;
      border-radius: 20px;
      overflow: hidden;
      gap: 1.5em;
      min-height: 530px;
    "
    class="disp-grid"
  >
    <div class="disp-flex j-c-center a-i-center">
      <img
        style="width: 100%; max-width: 180px"
        src="../assets/exchangeBIG_ANIM_TOP_INFINITE.svg"
        alt=""
      />
    </div>
    <div class="exchange-tiles-Wrapper">
      <div class="exchange-tile shadow1">
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
          "
        >
          <span style="font-size: var(--font-size-l)">Credits</span
          ><span style="opacity: 0.5"> 100 credits(Your balance)</span>
        </div>
        <div style="display: flex; align-items: center">
          <div class="creditsInputWrapper">
            <input class="creditsInput" type="number" placeholder="eg. 100" />
          </div>
        </div>
      </div>
      <div class="exchange-animation" style="">
        <img src="../assets/transactionsmallANIM-infinite.svg" alt="" />
      </div>
      <div class="exchange-tile">
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
          "
        >
          <span style="font-size: var(--font-size-l)">Euro</span
          ><span style="opacity: 0.5"> 0 credits(Your balance)</span>
        </div>
        <div style="display: flex; align-items: center">
          <div class="creditsInputWrapper">
            <input class="creditsInput" type="number" placeholder="eg. 100" />
          </div>
        </div>
      </div>
      <p class="credit-to-euro">1 credit = 0.9 euro</p>
    </div>
    <div
      style="
        border-top: 1px dotted var(--filter-reset);
        margin: 1em 0 0 0;
        padding: 5px 0 2em 0;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;
      "
    >
      <div
        style="
          display: flex;
          justify-content: space-between;
          width: 100%;
          padding: 0 2em;
        "
      >
        <span>Fees</span>
        <span>EUR 10</span>
      </div>
      <div class="exchange-button">Exchange</div>
    </div>
  </div>
  <div
    style="
      /* display: none !important; */
      display: grid;
      grid-template-rows: 4em 2em 1fr 4em;
      background-color: var(--sidebar);
      padding: 5px 1em;
      border-radius: 20px;
    "
  >
    <div class="disp-flex a-i-center">
      <span class="no-wrap-1-line">Recent Transactions</span>
      <div class="m-l-auto disp-flex">
        <div class="search-bar">
          <input type="text" placeholder="Search" />
        </div>
        <button class="add-btn" title="Add New Project">
          <svg
            class="btn-icon"
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="3"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="feather feather-plus"
          >
            <line x1="12" y1="5" x2="12" y2="19" />
            <line x1="5" y1="12" x2="19" y2="12" />
          </svg>
        </button>
      </div>
    </div>
    <div
      style="
        display: grid;
        grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.5fr;
        background-color: rgba(26, 37, 59, 0.5);
        align-items: center;
        margin-bottom: 5px;
        border-radius: 10px;
        gap: 1em;
      "
    >
      <span style="margin-left: 1em" class="no-wrap-1-line">Order Details</span>
      <span class="no-wrap-1-line">Order Source</span>
      <span class="no-wrap-1-line">Amount</span>
      <span class="no-wrap-1-line">Balance</span>
      <span class="no-wrap-1-line">Status</span>
      <span class="disp-flex j-c-center">Actions</span>
    </div>
    <ul style="padding-inline-start: 0px" class="">
      <li
        style="
          display: grid;
          grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.5fr;
          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
        "
      >
        <div class="disp-flex gap-05">
          <img
            style="min-width: 30px; max-width: 40px; opacity: 1"
            src="../assets/icons/arrowDown.svg"
            alt=""
          />
          <div class="disp-grid">
            <span class="no-wrap-1-line">Jane Doe</span>
            <span
              class="no-wrap-1-line"
              style="font-size: 14px; font-weight: 500; opacity: 0.7"
              >Time/Date</span
            >
          </div>
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">Bank Account</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >******123</span
          >
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">120 credits</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >120 euros</span
          >
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">120 credits</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >120 euros</span
          >
        </div>
        <span class="status-inProgress no-wrap-1-line">In Progress</span>
        <div class="disp-flex j-c-center">
          <span class="no-wrap-1-line">• • •</span>
        </div>
      </li>
      <li
        style="
          display: grid;
          grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.5fr;
          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
        "
      >
        <div class="disp-flex gap-05">
          <img
            style="min-width: 30px; max-width: 40px; opacity: 1"
            src="../assets/icons/arrowUp.svg"
            alt=""
          />
          <div class="disp-grid">
            <span class="no-wrap-1-line">Jane Doe</span>
            <span
              class="no-wrap-1-line"
              style="font-size: 14px; font-weight: 500; opacity: 0.7"
              >Time/Date</span
            >
          </div>
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">PayPal</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >a****@g******</span
          >
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">120 credits</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >120 euros</span
          >
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">120 credits</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >120 euros</span
          >
        </div>
        <span class="status-completed no-wrap-1-line">Completed</span>
        <div class="disp-flex j-c-center">
          <span class="no-wrap-1-line" class="no-wrap-1-line">• • •</span>
        </div>
      </li>
      <li
        style="
          display: grid;
          grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.5fr;
          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
        "
      >
        <div class="disp-flex gap-05">
          <img
            style="min-width: 30px; max-width: 40px; opacity: 1"
            src="../assets/icons/arrowDown.svg"
            alt=""
          />
          <div class="disp-grid">
            <span class="no-wrap-1-line">Jane Doe</span>
            <span
              class="no-wrap-1-line"
              style="font-size: 14px; font-weight: 500; opacity: 0.7"
              >Time/Date</span
            >
          </div>
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">Bank Account</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >******123</span
          >
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">120 credits</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >120 euros</span
          >
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">120 credits</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >120 euros</span
          >
        </div>
        <span class="status-completed no-wrap-1-line">Completed</span>
        <div class="disp-flex j-c-center">
          <span class="no-wrap-1-line">• • •</span>
        </div>
      </li>
      <li
        style="
          display: grid;
          grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.5fr;
          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
        "
      >
        <div class="disp-flex gap-05">
          <img
            style="min-width: 30px; max-width: 40px; opacity: 1"
            src="../assets/icons/arrowUp.svg"
            alt=""
          />
          <div class="disp-grid">
            <span class="no-wrap-1-line">Jane Doe</span>
            <span
              class="no-wrap-1-line"
              style="font-size: 14px; font-weight: 500; opacity: 0.7"
              >Time/Date</span
            >
          </div>
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">PayPal</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >a****@g******</span
          >
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">120 credits</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >120 euros</span
          >
        </div>
        <div class="disp-grid">
          <span class="no-wrap-1-line">120 credits</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
            >120 euros</span
          >
        </div>
        <span class="status-canceled no-wrap-1-line">Canceled</span>
        <div class="disp-flex j-c-center">
          <span class="no-wrap-1-line">• • •</span>
        </div>
      </li>
    </ul>
    <div class="list-number disp-flex" style="">
      <div class="showingInfoWrapper" style="margin: 0 0 0.75rem">
        <span class="showingInfo">Showing 26 to 30 of 271</span>
      </div>
      <nav>
        <ul class="pager">
          <li class="pager__item pager__item--prev">
            <a
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
              href="#"
            >
              Previous
            </a>
          </li>
          <li class="pager__item">
            <a class="pager__link" href="#">...</a>
          </li>
          <li class="pager__item">
            <a class="pager__link" href="#">1</a>
          </li>
          <li class="pager__item">
            <a class="pager__link" href="#">2</a>
          </li>
          <li class="pager__item">
            <a class="pager__link" href="#">3</a>
          </li>
          <li class="pager__item">
            <a class="pager__link" href="#">4</a>
          </li>
          <li class="pager__item active">
            <a class="pager__link" href="#">5</a>
          </li>
          <li class="pager__item">
            <a class="pager__link" href="#">6</a>
          </li>
          <li class="pager__item">
            <a class="pager__link" href="#">...</a>
          </li>
          <li class="pager__item pager__item--next">
            <a
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
              href="#"
            >
              Next
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</div>
