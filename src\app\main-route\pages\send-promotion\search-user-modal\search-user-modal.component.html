<div
  style="
    background-color: var(--app-bg);
    border-radius: 20px;
    scale: 1.1;
    height: 100%;
  "
>
  <div class="modal-body">
    <!-- header -->
    <div style="display: flex; justify-content: end; width: 100%">
      <img
        (click)="activeModal.close('Close click')"
        style="height: 17px; width: 17px; margin-left: auto"
        src="/assets/icons/close.svg"
        alt=""
      />
    </div>

    <div style="margin: 1em 0">
      <div
        style="
          display: flex;
          flex-direction: column;
          gap: 1em;
          align-items: center;
        "
      >
        <div
          style="
            width: 70%;
            position: relative;
            height: fit-content;
            margin-top: 0;
          "
          class="search-bar"
        >
          <!-- (keyup)="searchUser($event)" -->
          <input
            name="q"
            type="text"
            size="30"
            placeholder="Search..."
            style="height: 50px"
          />
        </div>

        <div
          *ngIf="!resultSearch?.length && !searchLoading"
          style="
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            margin: 0 0 15em;
          "
        >
          <img style="width: 60%" src="/assets/icons/usernotFound.svg" alt="" />
          <span>No user Found</span>
        </div>

        <!-- skeleton Start -->
        <!-- display: grid;  -->
        <div style="display: none; width: 100%; padding: 0 2em; gap: 1em"></div>
        <!-- skeleton end -->

        <!-- list -->
        <!-- display: flex; -->
        <div
          style="
            display: flex;
            overflow: scroll;
            width: 100%;
            padding: 0 2em;
            flex-direction: column;
            gap: 1em;
            margin-bottom: 1em;
            transform: translateX(5px);
            max-height: 54vh;
          "
        >
          <ng-container *ngIf="searchLoading">
            <div
              *ngFor="let i of [].constructor(10)"
              style="
                padding: 0.5em 1em !important;
                height: 64px;
                align-items: center;
                gap: 1em;
              "
              class="skeleton1 disp-flex"
            >
              <div
                style="height: 40px; width: 40px; border-radius: 14px"
                class="skeleton2"
              ></div>
              <div
                style="height: 18px; width: 118px; border-radius: 3px"
                class="skeleton2"
              ></div>
              <div
                style="
                  height: 25px;
                  width: 47px;
                  border-radius: 6px;
                  margin-left: auto;
                "
                class="skeleton2"
              ></div>
            </div>
          </ng-container>

          <ng-container *ngIf="resultSearch?.length && !searchLoading">
            <li *ngFor="let user of resultSearch" style="list-style: none">
              <div style="padding: 0.5em 1em !important" class="cardWrapper">
                <div style="display: flex; align-items: center" class="cardRec">
                  <div class="cardImage">
                    <!-- <app-avatar-photo
                      [buffer]="user?.photoProfile?.data"
                      [userId]="user?._id"
                      style="cursor: pointer"
                      [circleColor]="user?.photoColor"
                      [name]="user?.name"
                      [surname]="user?.surname"
                      [class]="'userImgBloggers'"
                      [classAvatarInitials]="'initialsClass'"
                    ></app-avatar-photo> -->
                  </div>
                  <div class="cardContent">
                    <p
                      class="no-wrap-1-line-200"
                      style="
                        color: white;
                        font-weight: 600;
                        margin-bottom: 0;
                        margin-top: 0;
                        display: flex;
                        align-items: center;
                        line-height: 1;
                        font-size: 17px;

                        max-width: 164px !important;
                      "
                    >
                      {{ user?.name }} {{ user?.surname }}
                    </p>
                  </div>
                  <div style="margin-left: auto">
                    <!-- (click)="selectUser(user)" -->
                    <p
                      style="
                        background: var(--action-color);
                        padding: 5px 1em;
                        border-radius: 8px;
                      "
                    >
                      Select
                    </p>
                  </div>
                </div>
              </div>
            </li>
          </ng-container>
        </div>
        <!-- list -->
      </div>
    </div>
  </div>
</div>
