import { DOCUMENT } from '@angular/common';
import { Component, Inject, Input, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { AuthService } from 'src/app/shared/services/auth.service';
import { LoadJsService } from 'src/app/shared/services/load-js.service';

@Component({
  selector: 'app-page-sidebar',
  templateUrl: './page-sidebar.component.html',
  styleUrls: ['./page-sidebar.component.css']
})
export class PageSidebarComponent implements OnInit {
  delay = ms => new Promise(res => setTimeout(res, ms));
  liSelected = ''
  subLiSelected = ''
  routePath = ''
  user: User;
  userIsAuthenticated = false
  @Input() lang = 'en'

  loading = false
  profileImage = null
  routeIsActiveUrl = '/en/home'
   
  constructor(
    private domSanitizer: DomSanitizer,
    @Inject(DOCUMENT) document: Document,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    public loadJsService: LoadJsService,
    private toastrService: ToastrService,
    private userService: AuthService
  ) { 
    this.loading = true
    this.userService.user.pipe().subscribe(appUser => {
      this.user = appUser
      if(this.user != null){
        // if(this.user?.photo?.data){
        //   this.profileImage = this.domSanitizer.bypassSecurityTrustResourceUrl('data:image/jpg;base64,'+ this.user.photo?.data)
        // }
        this.userIsAuthenticated = true
        this.routeIsActiveUrl =  this.router.url
      }
    })
  }

  ngOnInit(): void {
    this.loading = false
  }



  redirectTo(uri:string){
    this.routeIsActiveUrl =  '/'+ this.lang+uri
    this.router.navigateByUrl(this.lang+ uri)
      .then(() =>{
      });
  }

  routeIsActive(routePath: string) {
    return this.router.url === '/'+ this.lang+routePath;
  } 


  selectSub(element: string){
    this.subLiSelected = element !== this.subLiSelected ? element : ''
  }


  getElementById(element: string) {
    this.liSelected = element !== this.liSelected ? element : ''
  } 

}
