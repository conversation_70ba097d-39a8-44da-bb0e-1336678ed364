const catchAsync = require('./../utils/catchAsync');
const User = require('./../models/userModel');
const MinistryOfFinance = require('./../models/ministryOfFinanceModel');
const Post = require('./../models/postModel');

exports.getAll = catchAsync(async (req, res, next) => {

    let resultsPerPage =  req.body.resultsPerPage  
    let page = req.body.page 
    page = page - 1
    
    let data =
     await MinistryOfFinance.find()
     .populate({
      path: 'createdBy',
      select: ['id','_id','name','surname','isVerified','photoColor','untrusted','earnCount','likesCount','commentsCount','postCount','email']
    })
    .limit( resultsPerPage)
    .skip( resultsPerPage * page)
    .sort({ _id: -1 })
    
    let count = await MinistryOfFinance.countDocuments()

    res.status(200).json({
      status: 'success',
      data: {
        data: data,
        count: count
      }
    });
  
});

exports.search = catchAsync(async (req, res, next) => {
  if(req.body.searchText !== ''){
      let resultsPerPage =   req.body.resultsPerPage 
      let page = req.body.page
      page = page - 1
      req.body.searchText = req.body.searchText.trim()

      let name = req.body.searchText.split(" ")[0]; 
      let surname = req.body.searchText.split(" ")[1] === undefined ? name : req.body.searchText.split(" ")[1]; 

      let nameRegex = name.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
      let surnameRegex = surname.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
      
      nameRegex = new RegExp(nameRegex, 'i') 
      surnameRegex = new RegExp(surnameRegex, 'i') 

      let filter = {
        $or: [
          { 'createdBy.name':  { $regex: nameRegex } },
          { 'createdBy.surname':  { $regex: surnameRegex } },
        ]
      }

      MinistryOfFinance.find(filter)
      .populate({
          path: 'createdBy',
          select: ['id','_id','name','surname','isVerified','photoColor','untrusted']
      })
      .limit(resultsPerPage)
      .skip(resultsPerPage * page)
      .sort({ _id: -1 })
      .then(  async result => {
        // console.log("result: ",result)
        let countList = await MinistryOfFinance.find(filter,{user:0})
        let count = countList.length
        res.status(200).json({
          status: 'success',
          data: {
            data: result,
            count: count
          }
        });
      })

    }else{
      res.status(200).json({
        status: 'success',
        resultSearch: result 
      });
    }
});