const catchAsync = require("./../utils/catchAsync");
const User = require("./../models/userModel");
const Post = require("./../models/postModel");
const Like = require("./../models/likeModel");
const Comment = require("./../models/commentModel");
const PayPerView = require("./../models/payPerViewModel");
const PostGifts = require("./../models/postGiftsModel");
const Following = require("./../models/followingModel");
const PromotionAvailable = require("./../models/promotionAvailableModel");

// const sharp = require('sharp');
const { parser } = require("html-metadata-parser");
const mongoose = require("mongoose");

exports.resizePhoto = catchAsync(async (req, res, next) => {
  // console.log("req.files.file: ",req.files.file)
  // if (!req.files.file) return next();
  // req.body.images = []
  //  for(let file of  req.files.file){
  //   // .resize(        {
  //   //   width: 1080,
  //   //   height: 1080
  //   // })
  //         sharp(file.path)
  //         .rotate()
  //         .resize({
  //           width: 1080,
  //           height: 1080
  //         })
  //         .flatten(true)
  //         .flatten({ background: {r:0, b:0, g:0, alpha:0}})
  //         .jpeg({ mozjpeg: true })
  //         .toBuffer()
  //         .then( async data => {
  //             let buffer = await data
  //             req.body.images = [...req.body.images,await buffer]
  //             if(req.body.images.length === req.files.file.length ){
  //                 next();
  //             }
  //          }).catch( err => {});
  //  }
});

exports.resizePhotoBlur = catchAsync(async (req, res, next) => {
  // console.log("req.files.file: ",req.files.file)
  if (!req.files.file) return next();
  if (!JSON.parse(req.body.isPayPerView)) return next();

  req.body.blurImages = [];
  for (let file of req.files.file) {
    // {  r: 255, g: 255, b: 255}
    sharp(file.path)
      .rotate()
      .resize({
        width: 1080,
        height: 1080,
      })
      .blur(75) // 6 seems to work well
      .flatten(true)
      .flatten({ background: { r: 0, b: 0, g: 0, alpha: 0 } })
      .jpeg({ mozjpeg: true })
      .toBuffer()
      .then(async (data) => {
        let buffer = await data;
        req.body.blurImages = [...req.body.blurImages, await buffer];
        if (req.body.blurImages.length === req.files.file.length) {
          next();
        }
      })
      .catch((err) => {});
  }
});

exports.createPost = catchAsync(async (req, res, next) => {
  //   let fileDirName = req.body.fileDirName
  let data = {
    title: JSON.parse(req.body.title),
    comment: JSON.parse(req.body.comment),
    linkUrl: JSON.parse(req.body.linkUrl),
    user: JSON.parse(req.body.user),
    isImageContent: JSON.parse(req.body.isImageContent),
    isVideoContent: JSON.parse(req.body.isVideoContent),
    isPayPerView: JSON.parse(req.body.isPayPerView),
    isShareLink: JSON.parse(req.body.isShareLink),
    isPromotion: JSON.parse(req.body.isPromotion),
    images: [],
    blurImages: [],
  };

  if (JSON.parse(req.body.isPromotion)) {
    data["promotion"] = JSON.parse(req.body.promotion);
  }

  if (req.files.file) {
    for (let i = 0; i < req?.files.file.length; i++) {
      if (req.body.images[i]) {
        let item = await {
          size: await req.files.file[i].size,
          name: await req.files.file[i].name,
          data: await req.body.images[i],
          contentType: await req.files.file[i].type,
        };
        data.images = [...data.images, item];
      }
    }
    if (JSON.parse(req.body.isPayPerView)) {
      for (let i = 0; i < req?.files.file.length; i++) {
        if (req.body.blurImages[i]) {
          let item = await {
            size: await req.files.file[i].size,
            name: await req.files.file[i].name,
            data: await req.body.blurImages[i],
            contentType: await req.files.file[i].type,
          };
          data.blurImages = [...data.blurImages, item];
        }
      }
    }
  }

  if (JSON.parse(req.body.isVideoContent)) {
    data["videoId"] = JSON.parse(req.body.videoId);
  }

  let userId = null;
  let user = null;
  let userUpdatedData = {
    postCount: 0,
  };

  if (JSON.parse(req.body.user)) {
    userId = JSON.parse(req.body.user);
    user = await User.findOne({ _id: userId });
    if (user) {
      userUpdatedData.postCount = user.postCount + 1;
    }
  }

  const doc = await Post.create(data);
  let userUpdated = await User.findByIdAndUpdate(userId, userUpdatedData, {
    new: true,
  });
  if (JSON.parse(req.body.isPromotion)) {
    await PromotionAvailable.findOneAndUpdate(
      {
        promotion: JSON.parse(req.body.promotion),
        userAllowed: JSON.parse(req.body.user),
      },
      { isShared: true },
      { new: true }
    );

    // await Promotion.findOneAndUpdate({
    //   promotion: JSON.parse(req.body.promotion),
    //   userAllowed:  JSON.parse(req.body.user)
    // }, { isShared: true }, {new: true});
  }

  res.status(201).json({
    status: "success",
    data: {
      data: [],
    },
  });
});

exports.createEditedPost = catchAsync(async (req, res, next) => {
  //   let fileDirName = req.body.fileDirName

  let data = {
    title: JSON.parse(req.body.title),
    comment: JSON.parse(req.body.comment),
    linkUrl: JSON.parse(req.body.linkUrl),
    user: JSON.parse(req.body.user),
    isStatusContent: JSON.parse(req.body.isStatusContent),
    isImageContent: JSON.parse(req.body.isImageContent),
    isVideoContent: JSON.parse(req.body.isVideoContent),
    isPayPerView: JSON.parse(req.body.isPayPerView),
    isShareLink: JSON.parse(req.body.isShareLink),
    isPromotion: JSON.parse(req.body.isPromotion),
    isEditedCropImage: JSON.parse(req.body.isEditedCropImage),
    images: [],
    blurImages: [],
  };
  console.log("data['fontSize']: ", JSON.parse(req.body.fontSize));

  if (JSON.parse(req.body.fontSize)) {
    data["fontSize"] = JSON.parse(req.body.fontSize);
  }
  if (JSON.parse(req.body.fontColor)) {
    data["fontColor"] = JSON.parse(req.body.fontColor);
  }

  if (JSON.parse(req.body.isImageContent)) {
    data["isActive"] = false;
  }
  if (JSON.parse(req.body.isPromotion)) {
    data["promotion"] = JSON.parse(req.body.promotion);
  }

  if (JSON.parse(req.body.isVideoContent)) {
    data["videoId"] = JSON.parse(req.body.videoId);
  }

  let userId = null;
  let user = null;
  let userUpdatedData = {
    postCount: 0,
  };

  if (JSON.parse(req.body.user)) {
    userId = JSON.parse(req.body.user);
    user = await User.findOne({ _id: userId });
    if (user) {
      userUpdatedData.postCount = user.postCount + 1;
    }
  }

  const doc = await Post.create(data);
  let userUpdated = await User.findByIdAndUpdate(userId, userUpdatedData, {
    new: true,
  });
  if (JSON.parse(req.body.isPromotion)) {
    await PromotionAvailable.findOneAndUpdate(
      {
        promotion: JSON.parse(req.body.promotion),
        userAllowed: JSON.parse(req.body.user),
      },
      { isShared: true },
      { new: true }
    );

    // await Promotion.findOneAndUpdate({
    //   promotion: JSON.parse(req.body.promotion),
    //   userAllowed:  JSON.parse(req.body.user)
    // }, { isShared: true }, {new: true});
  }

  res.status(201).json({
    status: "success",
    postId: doc._id,
    data: {
      data: [],
    },
  });
});

exports.findPostByIdAndUpdate = catchAsync(async (req, res, next) => {
  let postUpdated = await Post.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
  });

  res.status(201).json({
    status: "success",
    postId: postUpdated._id,
    data: {
      data: [],
    },
  });
});

exports.getAll = catchAsync(async (req, res, next) => {
  let following = [];
  if (req.params.id !== "12354") {
    following = await Following.find({ follower: req.params.id })
      .distinct("user")
      .map((user) => user.id.toString());
  }

  res.status(200).json({
    status: "success",
    data: {
      followersNumbers: following.length,
    },
  });
});

exports.getAllBloggers = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 5 ? req.body.resultsPerPage : 5;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;
  let bloggers = await User.find({ active: { $ne: false } })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page);
  // bloggers = bloggers
  data = [];
  for (let item of bloggers) {
    let post = await Post.find({
      user: item?._doc?._id,
      isActive: { $ne: false },
      isPromotion: { $ne: true },
      isImageContent: { $ne: false },
    })
      .limit(3)
      .skip(0)
      .sort({ updatedAt: -1 });
    if (post.length) {
      data = await [
        ...data,
        {
          user: item,
          posts: post,
        },
      ];
    }
  }

  res.status(200).json({
    status: "success",
    data: {
      data: data,
    },
  });
});

exports.getPostData = catchAsync(async (req, res, next) => {
  let likesCount = 0;
  let commentsCount = 0;
  let giftsCount = 0;

  let post = await Post.findOne(
    { _id: req.params.id },
    { images: 0, blurImages: 0 }
  ).populate({
    path: "user",
    select: ["name", "surname", "isVerified", "photoColor", "untrusted"],
  });

  console.log("post: ", post);
  if (post) {
    if (post.user._id.toString() !== req.body.meId.toString()) {
      let payPerViewByMe = await PayPerView.find(
        { post: post._id, user: req.body.meId.toString() },
        {},
        {}
      );
      post["isPayPerViewByMe"] = payPerViewByMe.length ? true : false;
    } else {
      post["isPayPerViewByMe"] = true;
    }
  }

  let likes = await Like.find({ post: req.params.id })
    .populate({
      path: "user",
      select: ["name", "surname", "isVerified", "photoColor", "untrusted"],
    })
    .limit(10)
    .skip(0)
    .sort({ createdAt: -1 });
  let likesByMe = await Like.findOne({
    post: req.params.id.toString(),
    user: req.body.meId.toString(),
  });

  post["isLikedByMe"] = likesByMe ? true : false;
  post["myLikeId"] = likesByMe ? likesByMe._id : null;

  let comments = await Comment.find({ post: req.params.id })
    .populate({
      path: "user",
      select: ["name", "surname", "isVerified", "photoColor", "untrusted"],
    })
    .limit(10)
    .skip(0)
    .sort({ createdAt: -1 });

  let gifts = await PostGifts.find({ post: req.params.id })
    .populate({
      path: "user",
      select: ["name", "surname", "isVerified", "photoColor", "untrusted"],
    })
    .limit(10)
    .skip(0)
    .sort({ createdAt: -1 });

  const data = {
    post: post,
    likes: likes,
    likesCount: post.likesCount,
    comments: comments,
    commentsCount: post.commentsCount,
    gifts: gifts,
    giftsCount: post.giftsCount,
  };

  res.status(200).json({
    status: "success",
    data,
  });
});

exports.getAllPostForUserId = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 5 ? req.body.resultsPerPage : 5;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;

  let following = [];

  following = await Following.find({ follower: req.params.id })
    .distinct("user")
    .map((user) => {
      if (user.id) {
        return user.id.toString();
      }
    });
  following = [...following, req.params.id];

  let allPosts = await Post.find({
    user: { $in: following },
    isActive: { $ne: false },
  })
    .populate({
      path: "user",
    })
    .populate({
      path: "promotion",
    })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ updatedAt: -1 });
  let posts = [];

  for (let post of allPosts) {
    if (post._doc.user._id.toString() !== req.params.id.toString()) {
      let payPerViewByMe = await PayPerView.find({
        post: post._doc._id,
        user: req.params.id,
      });
      post._doc["isPayPerViewByMe"] = payPerViewByMe.length ? true : false;
    } else {
      post._doc["isPayPerViewByMe"] = true;
    }
    let likesByMe = await Like.find(
      { post: post._doc._id, user: req.params.id },
      {},
      {}
    );
    if (likesByMe.length) {
      post._doc["myLikeId"] = likesByMe[0]._id;
    }
    // console.log("post._doc._id: ",post._doc._id)
    post._doc["isLikedByMe"] = likesByMe.length ? true : false;

    let likes = await Like.find({ post: post._doc._id });
    likes = await likes.filter((element) => {
      if (element.user !== null) {
        return element;
      }
    });
    likes = await likes.filter(
      (tag, index, array) =>
        array.findIndex((t) => t.user._id == tag.user._id) == index
    );
    post._doc["likesCount"] = likes.length;

    post._doc["commentsCount"] = await Comment.countDocuments(
      { post: post._doc._id },
      function (err, count) {
        return count;
      }
    );
    post._doc["giftsCount"] = await PostGifts.countDocuments(
      { post: post._doc._id },
      function (err, count) {
        return count;
      }
    );
    if (post._doc.isShareLink) {
      if (post._doc.linkUrl) {
        try {
          await parser(post._doc.linkUrl.toString()).then(async (result) => {
            if (result) {
              post._doc["metadataParser"] = await result;
            }
          });
        } catch (error) {
          post._doc["metadataParser"] = null;
          // console.error(error);
          // post._doc['metadataParser'] = 0
        }
      }
    }

    posts = [...posts, post];
  }
  res.status(200).json({
    status: "success",
    data: {
      followingTotalNumber: following.length - 1,
      data: posts,
    },
  });
});

exports.getHomePost = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 20 ? 20 : req.body.resultsPerPage;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;

  let following = [];

  following = await Following.find({ follower: req.params.id })
    .distinct("user")
    .map((user) => {
      if (user.id) {
        return user.id.toString();
      }
    });
  following = [...following, req.params.id];

  let allPosts = await Post.find(
    { user: { $in: following }, isActive: { $ne: false } },
    { images: 0, blurImages: 0, backgroundImages: 0 }
  )
    .populate({
      path: "user",
      select: ["name", "surname", "isVerified", "photoColor", "untrusted"],
    })
    .populate({
      path: "promotion",
      select: ["linkUrl", "title"],
    })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ updatedAt: -1 });
  let posts = [];
  allPosts = await allPosts.filter((tag, index, array) => {
    if (tag.isPromotion) {
      if (tag.promotion !== null) {
        return tag;
      }
    } else {
      return tag;
    }
  });
  for (let post of allPosts) {
    post._doc["isLikedByMe"] = false;
    post._doc["isPayPerViewByMe"] = false;

    if (post._doc.user._id.toString() !== req.params.id.toString()) {
      let payPerViewByMe = await PayPerView.findOne({
        post: post._doc._id,
        user: req.params.id,
      }).select({ user: 0, post: 0, createdAt: 0 });
      if (payPerViewByMe) {
        if (payPerViewByMe._id) {
          post._doc["isPayPerViewByMe"] = payPerViewByMe._id ? true : false;
        }
      } else {
        post._doc["isPayPerViewByMe"] = false;
      }
    } else {
      post._doc["isPayPerViewByMe"] = true;
    }
    let likesByMe = await Like.findOne({
      post: post._doc._id,
      user: req.params.id,
    }).select({ user: 0, post: 0, createdAt: 0 });
    if (likesByMe) {
      if (likesByMe._id) {
        post._doc["myLikeId"] = likesByMe._id;
        post._doc["isLikedByMe"] = likesByMe._id ? true : false;
      }
    }
    if (post._doc.isShareLink) {
      if (post._doc.linkUrl) {
        try {
          // nonExistentFunction();
          await parser(post._doc.linkUrl.toString()).then(async (result) => {
            console.log("result: ", result);
            if (result) {
              post._doc["metadataParser"] = await result;
            } else {
              post._doc["metadataParser"] = null;
            }
          });
        } catch (error) {
          post._doc["metadataParser"] = null;
          // console.error(error);
          // post._doc['metadataParser'] = 0
        }
      }
    }
    posts = [...posts, post];
  }
  res.status(200).json({
    status: "success",
    data: {
      followingTotalNumber: following.length - 1,
      data: posts,
    },
  });
});

exports.getHomePostV2 = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 100 ? 100 : req.body.resultsPerPage;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;

  let following = [];

  following = await Following.find({ follower: req.params.id })
    .distinct("user")
    .map((user) => {
      if (user.id) {
        return user.id.toString();
      }
    });
  following = [...following, req.params.id];

  let allPosts = await Post.find(
    { user: { $in: following }, isActive: { $ne: false } },
    {
      images: 0,
      blurImages: 0,
      backgroundImages: 0,
      user: 0,
      title: 0,
      linkUrl: 0,
      likesCount: 0,
      isVideoContent: 0,
      isShareLink: 0,
      isPromotion: 0,
      isPayPerView: 0,
      isImageContent: 0,
      isEditedCropImage: 0,
      isActive: 0,
      id: 0,
      giftsCount: 0,
      fontSize: 0,
      createdAt: 0,
      commentsCount: 0,
      comment: 0,
      updatedAt: 0,
      videoId: 0,
      isStatusContent: 0,
      __v: 0,
      promotion: 0,
    }
  )
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ updatedAt: -1 });

  res.status(200).json({
    status: "success",
    data: {
      data: allPosts,
    },
  });
});

exports.getOnePost = catchAsync(async (req, res, next) => {
  let postData = await Post.find(
    { _id: req.params.id, isActive: { $ne: false } },
    { images: 0, blurImages: 0, backgroundImages: 0 }
  )
    .populate({
      path: "user",
      select: ["name", "surname", "isVerified", "photoColor", "untrusted"],
    })
    .populate({
      path: "promotion",
      select: ["linkUrl", "title"],
    });
  let onePost = [];
  postData = await postData.filter((tag, index, array) => {
    if (tag.isPromotion) {
      if (tag.promotion !== null) {
        return tag;
      }
    } else {
      return tag;
    }
  });

  for (let post of postData) {
    post._doc["isLikedByMe"] = false;
    post._doc["isPayPerViewByMe"] = false;

    if (post._doc.user._id.toString() !== req.body.meId.toString()) {
      let payPerViewByMe = await PayPerView.findOne({
        post: post._doc._id,
        user: req.body.meId.toString(),
      }).select({ user: 0, post: 0, createdAt: 0 });
      if (payPerViewByMe) {
        if (payPerViewByMe._id) {
          post._doc["isPayPerViewByMe"] = payPerViewByMe._id ? true : false;
        }
      } else {
        post._doc["isPayPerViewByMe"] = false;
      }
    } else {
      post._doc["isPayPerViewByMe"] = true;
    }

    let likesByMe = await Like.findOne({
      post: post._doc._id,
      user: req.body.meId.toString(),
    }).select({ user: 0, post: 0, createdAt: 0 });
    console.log("likesByMe: ", likesByMe);

    if (likesByMe) {
      if (likesByMe._id) {
        post._doc["myLikeId"] = likesByMe._id;
        post._doc["isLikedByMe"] = likesByMe._id ? true : false;
      }
    }
    if (post._doc.isShareLink) {
      if (post._doc.linkUrl) {
        try {
          // nonExistentFunction();
          await parser(post._doc.linkUrl.toString()).then(async (result) => {
            if (result) {
              post._doc["metadataParser"] = await result;
            } else {
              post._doc["metadataParser"] = null;
            }
          });
        } catch (error) {
          post._doc["metadataParser"] = null;
          // console.error(error);
          // post._doc['metadataParser'] = 0
        }
      }
    }
    onePost = [...onePost, post];
  }
  res.status(200).json({
    status: "success",
    data: onePost,
  });
});

exports.suggestedPosts = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 20 ? 20 : req.body.resultsPerPage;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;

  let following = [];

  following = await Following.find({ follower: req.params.id })
    .distinct("user")
    .map((user) => {
      if (user.id) {
        return user.id.toString();
      }
    });
  following = [...following, req.params.id];

  let allPosts = await Post.find(
    {
      user: { $nin: following },
      isActive: { $ne: false },
      isPayPerView: { $ne: true },
      isShareLink: { $ne: true },
      isPromotion: { $ne: true },
    },
    { images: 0, blurImages: 0 }
  )
    .populate({
      path: "user",
      select: ["name", "surname", "isVerified", "photoColor", "untrusted"],
    })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ updatedAt: -1 });
  let posts = [];

  for (let post of allPosts) {
    post._doc["isLikedByMe"] = false;
    post._doc["isPayPerViewByMe"] = false;

    if (post._doc.user._id.toString() !== req.params.id.toString()) {
      let payPerViewByMe = await PayPerView.findOne({
        post: post._doc._id,
        user: req.params.id,
      }).select({ user: 0, post: 0, createdAt: 0 });
      if (payPerViewByMe) {
        if (payPerViewByMe._id) {
          post._doc["isPayPerViewByMe"] = payPerViewByMe._id ? true : false;
        }
      } else {
        post._doc["isPayPerViewByMe"] = false;
      }
    } else {
      post._doc["isPayPerViewByMe"] = true;
    }
    let likesByMe = await Like.findOne({
      post: post._doc._id,
      user: req.params.id,
    }).select({ user: 0, post: 0, createdAt: 0 });
    if (likesByMe) {
      if (likesByMe._id) {
        post._doc["myLikeId"] = likesByMe._id;
        post._doc["isLikedByMe"] = likesByMe._id ? true : false;
      }
    }

    posts = [...posts, post];
  }
  res.status(200).json({
    status: "success",
    data: {
      followingTotalNumber: following.length - 1,
      data: posts,
    },
  });
});

exports.getLikesPost = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 10 ? req.body.resultsPerPage : 5;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;

  let likes = await Like.find({ post: req.params.id })
    .populate({
      path: "user",
      select: ["name", "surname", "isVerified", "photoColor", "untrusted"],
    })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ createdAt: -1 });

  res.status(200).json({
    status: "success",
    data: likes,
  });
});

exports.getCommentsPost = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 10 ? req.body.resultsPerPage : 5;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;

  let comments = await Comment.find({ post: req.params.id })
    .populate({
      path: "user",
      select: ["name", "surname", "isVerified", "photoColor", "untrusted"],
    })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ createdAt: -1 });

  res.status(200).json({
    status: "success",
    data: comments,
  });
});

exports.getGiftsPost = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 10 ? req.body.resultsPerPage : 5;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;

  let postGifts = await PostGifts.find({ post: req.params.id })
    .populate({
      path: "user",
      select: ["name", "surname", "isVerified", "photoColor", "untrusted"],
    })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ createdAt: -1 });

  res.status(200).json({
    status: "success",
    data: postGifts,
  });
});

exports.getAllCreatedPosts = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 5 ? req.body.resultsPerPage : 5;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;
  let allPosts = await Post.find({
    user: req.params.id,
    isPromotion: { $ne: true },
    isActive: { $ne: false },
  })
    .populate({
      path: "user",
      select: ["name", "surname", "isVerified", "photoColor", "untrusted"],
    })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ updatedAt: -1 });

  let posts = [];
  for (let post of allPosts) {
    if (post._doc.user._id.toString() !== req.body.meId.toString()) {
      let payPerViewByMe = await PayPerView.find({
        post: post._doc._id,
        user: req.body.meId.toString(),
      });
      post._doc["isPayPerViewByMe"] = payPerViewByMe.length ? true : false;
    } else {
      post._doc["isPayPerViewByMe"] = true;
    }
    let likesByMe = await Like.find({
      post: post._doc._id,
      user: req.body.meId.toString(),
    });
    post._doc["isLikedByMe"] = likesByMe.length ? true : false;

    if (likesByMe.length) {
      post._doc["myLikeId"] = likesByMe[0]._id;
    }

    let likes = await Like.find({ post: post._doc._id }).sort({
      createdAt: -1,
    });
    likes = await likes.filter((element) => {
      if (element.user !== null) {
        return element;
      }
    });
    likes = await likes.filter(
      (tag, index, array) =>
        array.findIndex((t) => t.user._id == tag.user._id) == index
    );
    post._doc["likesCount"] = likes.length;

    post._doc["commentsCount"] = await Comment.countDocuments(
      { post: post._doc._id },
      function (err, count) {
        return count;
      }
    );
    post._doc["giftsCount"] = await PostGifts.countDocuments(
      { post: post._doc._id },
      function (err, count) {
        return count;
      }
    );
    if (post._doc.isShareLink) {
      try {
        await parser(post._doc.linkUrl.toString()).then(async (result) => {
          if (result) {
            post._doc["metadataParser"] = await result;
          }
        });
      } catch (error) {
        post._doc["metadataParser"] = null;
        // console.error(error);
        // post._doc['metadataParser'] = 0
      }
    }

    posts = [...posts, post];
  }

  res.status(200).json({
    status: "success",
    data: {
      data: posts,
    },
  });
});

exports.getAllCreatedPostsV2 = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 2 ? req.body.resultsPerPage : 2;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;

  let allPosts = await Post.find(
    { user: req.params.id, isActive: { $ne: false } },
    { images: 0, blurImages: 0 }
  )
    .populate({
      path: "user",
      select: ["name", "surname", "isVerified", "photoColor", "untrusted"],
    })
    .populate({
      path: "promotion",
      select: ["linkUrl", "title"],
    })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ updatedAt: -1 });

  let posts = [];
  allPosts = await allPosts.filter((tag, index, array) => {
    if (tag.isPromotion) {
      if (tag.promotion !== null) {
        return tag;
      }
    } else {
      return tag;
    }
  });

  for (let post of allPosts) {
    post._doc["isLikedByMe"] = false;
    post._doc["isPayPerViewByMe"] = false;

    if (post._doc.user._id.toString() !== req.body.meId.toString()) {
      let payPerViewByMe = await PayPerView.findOne({
        post: post._doc._id,
        user: req.body.meId,
      }).select({ user: 0, post: 0, createdAt: 0 });
      if (payPerViewByMe) {
        if (payPerViewByMe._id) {
          post._doc["isPayPerViewByMe"] = payPerViewByMe._id ? true : false;
        }
      } else {
        post._doc["isPayPerViewByMe"] = false;
      }
    } else {
      post._doc["isPayPerViewByMe"] = true;
    }
    let likesByMe = await Like.findOne({
      post: post._doc._id,
      user: req.body.meId,
    }).select({ user: 0, post: 0, createdAt: 0 });
    if (likesByMe) {
      if (likesByMe._id) {
        post._doc["myLikeId"] = likesByMe._id;
        post._doc["isLikedByMe"] = likesByMe._id ? true : false;
      }
    }

    if (post._doc.isShareLink) {
      if (post._doc.linkUrl) {
        try {
          await parser(post._doc.linkUrl.toString()).then(async (result) => {
            if (result) {
              post._doc["metadataParser"] = await result;
            } else {
              post._doc["metadataParser"] = null;
            }
          });
        } catch (error) {
          post._doc["metadataParser"] = null;
          // console.error(error);
          // post._doc['metadataParser'] = 0
        }
      }
    }
    posts = [...posts, post];
  }

  res.status(200).json({
    status: "success",
    data: {
      data: posts,
    },
  });
});

exports.getAllPromotionPosts = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 5 ? req.body.resultsPerPage : 5;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;
  let allPosts = await Post.find({
    user: req.params.id,
    isPromotion: { $ne: false },
    promotion: { $exists: true, $ne: null },
    isActive: { $ne: false },
  })
    .populate({
      path: "user",
      select: ["name", "surname", "isVerified", "photoColor", "untrusted"],
    })
    .populate({
      path: "promotion",
      select: ["linkUrl", "title"],
    })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ updatedAt: -1 });

  let posts = [];

  allPosts = await allPosts.filter((tag, index, array) => {
    if (tag.isPromotion) {
      if (tag.promotion !== null) {
        return tag;
      }
    } else {
      return tag;
    }
  });

  for (let post of allPosts) {
    post._doc["isPayPerViewByMe"] = true;
    let likesByMe = await Like.find(
      { post: post._doc._id, user: req.body.meId.toString() },
      {},
      {}
    );
    post._doc["isLikedByMe"] = likesByMe.length ? true : false;
    if (likesByMe.length) {
      post._doc["myLikeId"] = likesByMe[0]._id;
    }
    let likes = await Like.find({ post: post._doc._id }).sort({
      createdAt: -1,
    });
    likes = await likes.filter((element) => {
      if (element.user !== null) {
        return element;
      }
    });
    likes = await likes.filter(
      (tag, index, array) =>
        array.findIndex((t) => t.user._id == tag.user._id) == index
    );
    post._doc["likesCount"] = likes.length;

    post._doc["commentsCount"] = await Comment.countDocuments(
      { post: post._doc._id },
      function (err, count) {
        return count;
      }
    );
    post._doc["giftsCount"] = await PostGifts.countDocuments(
      { post: post._doc._id },
      function (err, count) {
        return count;
      }
    );
    posts = [...posts, post];
  }

  res.status(200).json({
    status: "success",
    data: {
      data: posts,
    },
  });
});

exports.getAllVideosPosts = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 20 ? req.body.resultsPerPage : 20;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;

  //
  // .createIndex({ isVideoContent: -1 }, {'images':0, 'blurImages':0,'backgroundImages':0})
  // { $ne: false }
  // await mongoose.connection.db.posts.createIndex( {isVideoContent: 0 })
  // await Post.countDocuments({isVideoContent:  true}, function( err, count){
  //   console.log("count: ",count)
  //   return count
  // })
  const allPosts = await Post.find({ isVideoContent: true })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ updatedAt: -1 });

  res.status(200).json({
    status: "success",
    data: {
      data: allPosts,
    },
  });
});

exports.getAllDeletedPosts = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 2 ? req.body.resultsPerPage : 2;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;

  let allPosts = await Post.find(
    {
      user: req.params.id,
      isActive: { $in: false },
      isPromotion: { $in: false },
    },
    { images: 0, blurImages: 0 }
  )
    .populate({
      path: "user",
      select: ["name", "surname", "isVerified", "photoColor", "untrusted"],
    })
    .populate({
      path: "promotion",
      select: ["linkUrl", "title"],
    })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ updatedAt: -1 });

  let posts = [];

  for (let post of allPosts) {
    post._doc["isLikedByMe"] = false;
    post._doc["isPayPerViewByMe"] = false;

    if (post._doc.user._id.toString() !== req.body.meId.toString()) {
      let payPerViewByMe = await PayPerView.findOne({
        post: post._doc._id,
        user: req.body.meId,
      }).select({ user: 0, post: 0, createdAt: 0 });
      if (payPerViewByMe) {
        if (payPerViewByMe._id) {
          post._doc["isPayPerViewByMe"] = payPerViewByMe._id ? true : false;
        }
      } else {
        post._doc["isPayPerViewByMe"] = false;
      }
    } else {
      post._doc["isPayPerViewByMe"] = true;
    }
    let likesByMe = await Like.findOne({
      post: post._doc._id,
      user: req.body.meId,
    }).select({ user: 0, post: 0, createdAt: 0 });
    if (likesByMe) {
      if (likesByMe._id) {
        post._doc["myLikeId"] = likesByMe._id;
        post._doc["isLikedByMe"] = likesByMe._id ? true : false;
      }
    }

    if (post._doc.isShareLink) {
      if (post._doc.linkUrl) {
        try {
          await parser(post._doc.linkUrl.toString()).then(async (result) => {
            if (result) {
              post._doc["metadataParser"] = await result;
            } else {
              post._doc["metadataParser"] = null;
            }
          });
        } catch (error) {
          post._doc["metadataParser"] = null;
          // console.error(error);
          // post._doc['metadataParser'] = 0
        }
      }
    }
    posts = [...posts, post];
  }

  res.status(200).json({
    status: "success",
    data: {
      data: posts,
    },
  });
});
exports.deleteMyPost = catchAsync(async (req, res, next) => {
  let deletedPost = await Post.findByIdAndUpdate(req.params.id, {
    isActive: false,
    updatedAt: req.body.updatedAt,
  });

  res.status(200).json({
    status: "success",
    data: {
      data: deletedPost,
    },
  });
});
exports.recoverMyPost = catchAsync(async (req, res, next) => {
  let recoveredPost = await Post.findByIdAndUpdate(req.params.id, {
    isActive: true,
    updatedAt: req.body.updatedAt,
  });

  res.status(200).json({
    status: "success",
    data: {
      data: recoveredPost,
    },
  });
});

exports.uploadPostVideo = catchAsync(async (req, res, next) => {
  var conn = mongoose.connection;
  var Grid = require("gridfs-stream");
  Grid.mongo = mongoose.mongo;
  var gf = new Grid(conn.db);
  var GridFS = Grid(mongoose.connection.db, mongoose.mongo);
  let f = req.files.file[0];
  var writestream = GridFS.createWriteStream({
    filename: "test3",
  });
  writestream.on("close", function (file) {
    // callback(null, file);
    console.log("file: ", file);
    res.status(200).json({
      status: "success",
      data: {
        _id: file._id,
      },
    });
  });
  fs.createReadStream(f.path).pipe(writestream);
});

exports.getVideo = catchAsync(async (req, res, next) => {
  new GridStore(
    mongoose.connection.db,
    new ObjectID(req.params.id.toString()),
    null,
    "r"
  ).open(function (err, GridFile) {
    if (!GridFile) {
      res.send(404, "Not Found");
      return;
    } else {
      StreamGridFile(req, res, GridFile);
    }
    //StreamGridFile(req, res, GridFile)
  });
});
function StreamGridFile(req, res, GridFile) {
  const options = {};
  let start;
  let end;
  const range = req.headers.range;
  if (range) {
    const bytesPrefix = "bytes=";
    if (range.startsWith(bytesPrefix)) {
      const bytesRange = range.substring(bytesPrefix.length);
      const parts = bytesRange.split("-");
      if (parts.length === 2) {
        const rangeStart = parts[0] && parts[0].trim();
        if (rangeStart && rangeStart.length > 0) {
          options.start = start = parseInt(rangeStart);
        }
        const rangeEnd = parts[1] && parts[1].trim();
        if (rangeEnd && rangeEnd.length > 0) {
          options.end = end = parseInt(rangeEnd);
        }
      }
    }
  }

  if (req.headers["range"]) {
    let contentLength = GridFile.length;

    const fileSize = GridFile.length;
    var parts = req.headers["range"].replace(/bytes=/, "").split("-");
    var maxIdx = GridFile.length - 1;
    //var start = parseInt(parts[0], 10);
    //var end = parseInt(parts[1], 10);
    if (isInvalidRange(start, end, maxIdx)) {
      return res
        .writeHead(416, {
          "Accept-Ranges": "bytes",
          "Content-Type": GridFile.contentType,
          "Content-Range": "bytes */" + GridFile.length,
        })
        .end();
    }
    // if (end > maxIdx || end !== end) {
    //   end = maxIdx
    // }
    // var bytesToSend = (end - start) + 1

    // res.writeHead(206, {
    //   'Content-Type': GridFile.contentType,
    //   'Content-Range': 'bytes ' + start + '-' + end + '/' + GridFile.length,
    //   'Accept-Ranges': 'bytes',
    //   'Content-Length': bytesToSend
    // });
    if (req.method === "HEAD") {
      res.statusCode = 200;
      res.setHeader("accept-ranges", "bytes");
      res.setHeader("content-length", contentLength);
      res.end();
    } else {
      let retrievedLength;
      if (start !== undefined && end !== undefined) {
        retrievedLength = end + 1 - start;
      } else if (start !== undefined) {
        retrievedLength = contentLength - start;
      } else if (end !== undefined) {
        retrievedLength = end + 1;
      } else {
        retrievedLength = contentLength;
      }
      var statusCode = start !== undefined || end !== undefined ? 206 : 200;
      res.writeHead(statusCode, {
        "Content-Type": "video/mp4",
        "Content-Range": `bytes ${start || 0}-${
          end || contentLength - 1
        }/${contentLength}`,
        "Accept-Ranges": "bytes",
        "Content-Length": retrievedLength,
      });
      GridFile.seek(start, function () {
        // get GridFile stream
        var stream = GridFile.stream(true);
        // write to response
        stream.on("data", function (buff) {
          if (buff.length >= retrievedLength) {
            //      buff = buff.slice(0, retrievedLength)
          }

          //		console.log("Buf lenght: f", buff.length);
          res.write(buff);
          //console.log("etrive lenght: ",retrievedLength);
          retrievedLength -= buff.length;
          console.log("Retrive lenght: ", retrievedLength);
          console.log("End: ", end);
          if (end !== 1) {
            if (retrievedLength == 0) {
              // if(req.method == "HEAD"){
              GridFile.close();
              res.end();
            }
          }
        });
      });
    }
  } else {
    // stream back whole file
    //res.header('Accept-Range', 'bytes');
    //res.header('Content-Type', GridFile.contentType);
    //var stream = GridFile.stream(true);
    //stream.pipe(res);
    //stream.on('end', function(){
    //  GridFile.close();
    //});
  }
}
function isInvalidRange(start, end, maxIdx) {
  return (
    start !== start || // NaN
    start < 0 ||
    end < start ||
    start > maxIdx
  );
}

exports.getImage = catchAsync(async (req, res, next) => {
  new GridStore(
    mongoose.connection.db,
    new ObjectID(req.params.id.toString()),
    null,
    "r"
  ).open(function (err, GridFile) {
    if (!GridFile) {
      res.send(404, "Not Found");
      return;
    } else {
      GridFile.read(GridFile.length, function (err, data) {
        res.setHeader("Content-Type", GridFile.contentType);
        res.setHeader("Content-Length", GridFile.length);
        // gs don't have a property filename, filename need to come from your req.
        res.setHeader(
          "Content-Disposition",
          'inline; filename="' + "img1.png" + '"'
        );

        res.end(data);
      });
      // StreamGridFile(req, res, GridFile);
    }
  });
});

exports.getPostImages = catchAsync(async (req, res, next) => {
  let status = "failed";
  let reason = "Following dosnt exist";
  let images = [];
  let backgroundImages = [];
  if (req.body.userId && req.body.postUserId && req.body.postUserId) {
    // let following = await Following.findOne({ follower: req.body.userId , user: req.body.postUserId}).select({  follower: 0, user: 0,createdAt:0,id:0});
    // if(following){
    //   if(following._id){

    //   }
    // }

    let post = await Post.findOne(
      { user: req.body.postUserId.toString(), _id: req.body.postId.toString() },
      "images backgroundImages"
    );
    if (post) {
      if (post.images.length) {
        status = "success";
        reason = "This Post have images";
        images = post.images;
        if (post.backgroundImages) {
          if (post.backgroundImages.length) {
            backgroundImages = post.backgroundImages;
          }
        }
      } else {
        reason = "This Post does not have images";
      }
    } else {
      reason = "Post doesnt exist";
    }
  }

  res.status(200).json({
    status: status,
    reason: reason,
    data: {
      images: images,
      backgroundImages: backgroundImages,
    },
  });
});

exports.getPostBlurImages = catchAsync(async (req, res, next) => {
  let status = "failed";
  let reason = "Following dosnt exist";
  let blurImages = [];
  if (req.body.userId && req.body.postUserId && req.body.postUserId) {
    // let following = await Following.findOne({ follower: req.body.userId , user: req.body.postUserId}).select({  follower: 0, user: 0,createdAt:0,id:0});
    // if(following){
    //   if(following._id){

    //   }
    // }

    let post = await Post.findOne(
      { user: req.body.postUserId.toString(), _id: req.body.postId.toString() },
      "blurImages"
    );
    if (post) {
      if (post.blurImages.length) {
        status = "success";
        reason = "This Post have blur images";
        blurImages = post.blurImages;
      } else {
        reason = "This Post does not have blur images";
      }
    } else {
      reason = "Post doesnt exist";
    }
  }

  res.status(200).json({
    status: status,
    reason: reason,
    data: {
      blurImages: blurImages,
    },
  });
  // let allPosts = await Post.find({ user: req.params.id , isPromotion: { $ne: true }, isActive: { $ne: false } })
  // .limit( resultsPerPage)
  // .skip( resultsPerPage * page)
  // .sort({updatedAt:-1})
});

exports.getPromotionImage = catchAsync(async (req, res, next) => {
  let status = "failed";
  let reason = "Following dosnt exist";
  let images = [];
  if (req.body.userId && req.body.postUserId && req.body.postUserId) {
    // let following = await Following.findOne({ follower: req.body.userId , user: req.body.postUserId}).select({  follower: 0, user: 0,createdAt:0,id:0});
    // if(following){
    //   if(following._id){

    //   }
    // }

    let post = await Post.findOne({
      user: req.body.postUserId.toString(),
      _id: req.body.postId.toString(),
    }).populate({
      path: "promotion",
      select: ["images"],
    });

    if (post) {
      if (post.isPromotion) {
        if (post.promotion.images.length) {
          status = "success";
          reason = "This Post have promotion images";
          images = post.promotion.images;
        } else {
          reason = "This Post  does not have promotion images";
        }
      } else {
        reason = "This Post is not promotion";
      }
    } else {
      reason = "Post doesnt exist";
    }
  }

  res.status(200).json({
    status: status,
    reason: reason,
    data: {
      images: images,
    },
  });
  // let allPosts = await Post.find({ user: req.params.id , isPromotion: { $ne: true }, isActive: { $ne: false } })
  // .limit( resultsPerPage)
  // .skip( resultsPerPage * page)
  // .sort({updatedAt:-1})
});

exports.getVideoFirstImage = catchAsync(async (req, res, next) => {
  let status = "failed";
  let reason = "Following dosnt exist";
  let images = [];
  if (req.body.userId && req.body.postUserId && req.body.postUserId) {
    // let following = await Following.findOne({ follower: req.body.userId , user: req.body.postUserId}).select({  follower: 0, user: 0,createdAt:0,id:0});
    // if(following){
    //   if(following._id){  }
    // }

    let post = await Post.findOne({
      user: req.body.postUserId.toString(),
      _id: req.body.postId.toString(),
    });

    if (post) {
      if (post.isVideoContent) {
        if (post.images.length) {
          status = "success";
          reason = "This Post have video first image";
          images = post.images;
        } else {
          reason = "This Post does not have video first image";
        }
      } else {
        reason = "This Post is not video content";
      }
    } else {
      reason = "Post doesnt exist";
    }
  }

  res.status(200).json({
    status: status,
    reason: reason,
    data: {
      images: images,
    },
  });
  // let allPosts = await Post.find({ user: req.params.id , isPromotion: { $ne: true }, isActive: { $ne: false } })
  // .limit( resultsPerPage)
  // .skip( resultsPerPage * page)
  // .sort({updatedAt:-1})
});

exports.getVideoFirstBlurImage = catchAsync(async (req, res, next) => {
  let status = "failed";
  let reason = "Following dosnt exist";
  let images = [];
  if (req.body.userId && req.body.postUserId && req.body.postUserId) {
    // let following = await Following.findOne({ follower: req.body.userId , user: req.body.postUserId}).select({  follower: 0, user: 0,createdAt:0,id:0});
    // if(following){
    //   if(following._id){

    //   }
    // }

    let post = await Post.findOne({
      user: req.body.postUserId.toString(),
      _id: req.body.postId.toString(),
    });

    if (post) {
      if (post.isVideoContent) {
        if (post.blurImages.length) {
          status = "success";
          reason = "This Post have video first blur image";
          images = post.blurImages;
        } else {
          reason = "This Post does not have video first blur image ";
        }
      } else {
        reason = "This Post is not video content";
      }
    } else {
      reason = "Post doesnt exist";
    }
  }

  res.status(200).json({
    status: status,
    reason: reason,
    data: {
      images: images,
    },
  });
  // let allPosts = await Post.find({ user: req.params.id , isPromotion: { $ne: true }, isActive: { $ne: false } })
  // .limit( resultsPerPage)
  // .skip( resultsPerPage * page)
  // .sort({updatedAt:-1})
});

exports.getProfileImage = catchAsync(async (req, res, next) => {
  let status = "failed";
  let reason = "User doesnt exist";
  let image = null;
  console.log("getProfileImage");
  if (req.body.meId && req.body.userId) {
    let user = await User.findOne({ _id: req.body.userId.toString() });
    if (user) {
      if (user.photoProfile) {
        status = "success";
        reason = "This User have photoProfile";
        image = user.photoProfile;
      } else {
        reason = "This User  does not have photoProfile";
      }
    }
  }
  res.status(200).json({
    status: status,
    reason: reason,
    data: {
      image: image,
    },
  });
});

exports.getPhotoCoverImage = catchAsync(async (req, res, next) => {
  console.log("getPhotoCoverImage");
  let status = "failed";
  let reason = "User doesnt exist";
  let image = null;
  if (req.body.meId && req.body.userId) {
    let user = await User.findOne({ _id: req.body.userId.toString() });
    if (user) {
      if (user.photoProfile) {
        status = "success";
        reason = "This User have photoCover";
        image = user.photoCover;
      } else {
        reason = "This User  does not have photoCover";
      }
    }
  }
  res.status(200).json({
    status: status,
    reason: reason,
    data: {
      image: image,
    },
  });
});
