import { Component, OnInit, TemplateRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ngxLoadingAnimationTypes } from 'ngx-loading';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { AppVersionService } from 'src/app/shared/services/app-version.service';
import { AuthService } from 'src/app/shared/services/auth.service';

@Component({
  selector: 'app-list-of-app-versions',
  templateUrl: './list-of-app-versions.component.html',
  styleUrls: ['./list-of-app-versions.component.css']
})
export class ListOfAppVersionsComponent implements OnInit {


  public ngxLoadingAnimationTypes = ngxLoadingAnimationTypes;
  public loadingTemplate: TemplateRef<any>;
  loading = false
  lang = 'en'
  user: User;
  appVersions = []
  constructor(
    private activatedRoute: ActivatedRoute,
    private authService: AuthService,
    private toastrService: ToastrService,
    private appVersionService: AppVersionService
  ) { }

  ngOnInit(): void {
    this.loading = true

    this.activatedRoute.params.subscribe( async paramMap => {
      this.authService.user.pipe().subscribe( async appUser => {
        this.user = appUser
      })
      if(paramMap['lang']){
        this.lang = paramMap['lang'];
      }
      this.getAll()
    })
  }
  
  getAll(){
    this.appVersionService.getAll().subscribe( 
     async result =>{
        if(result.status == "success"){
              this.appVersions = result.data.data
              this.loading = false
          }

      },
      respond_error => {
       this.toastrService.error(
         respond_error?.error.message
          , respond_error?.name);         
       }
    )
  }


}
