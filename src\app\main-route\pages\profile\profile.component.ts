import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { switchMap } from 'rxjs/operators';
import { User } from 'src/app/shared/models/user';
import { AuthService } from 'src/app/shared/services/auth.service';
import { UserService } from 'src/app/shared/services/user.service';
import { ImageCroppedEvent, base64ToFile } from 'ngx-image-cropper';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { LoadJsService } from 'src/app/shared/services/load-js.service';
import { TranslatePipe } from 'src/app/shared/pipes/translate.pipe';
import { DomSanitizer } from '@angular/platform-browser';
import { CompressImageService } from 'src/app/shared/services/compress-image.service';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css']
})
export class ProfileComponent implements OnInit {

  imageChangedEvent: any = '';
  croppedImage: any = '';

  user: User;
  userIsAuthenticated = false

  selectedImage;
  showPreview = false;
  infoImage: string;
  @ViewChild('fileInput', { static: false }) fileInput: ElementRef;
  primaryInfoForm: UntypedFormGroup;
  userImageProfile;


  loading = false
  lang = 'en'
  languages : any[] = []

  ime : any[] = []
  profileImage = null
  constructor(
    private domSanitizer: DomSanitizer,
    private compressImageService: CompressImageService,
    private authService: AuthService,
    private userService: UserService,
    private activatedRoute: ActivatedRoute,
    public toastrService: ToastrService,
    private router: Router,
    public loadJsService: LoadJsService) {
      this.loading = true


      this.authService.user.pipe().subscribe(appUser => {
        this.user = appUser
        if(this.user != null){
          this.userIsAuthenticated = true
        }
      })
  }

  ngOnInit(): void {
    this.activatedRoute.params.subscribe( async paramMap => {
      if(paramMap['lang']){
        this.lang = paramMap['lang'];
      }
    })
    this.authService.user.pipe().subscribe(
      appUser =>{

        this.user = appUser
        let name = ' '
        let surname = ' '
        this.selectedImage = ' '

        if(this.user.name){
            name = this.user.name
        }

        if(this.user.surname){
            surname  = this.user.surname
        }

        this.primaryInfoForm = new UntypedFormGroup({
          name: new UntypedFormControl( name ),
          surname: new UntypedFormControl(surname)
        })

        }
      );

      this.loading = false
      this.loadJsService.loadScripts()
    
  }


  public fileUploadClick(){
    this.fileInput.nativeElement.click()
  }
  updatePrimaryInfo() {
          // this.primaryInfoForm.get('profileImagePath')?.value,
    // this.userService.updateMe(
    //   this.user._id,
    //   this.primaryInfoForm.value.name,      
    //   this.primaryInfoForm.value.surname
    // ).subscribe( respond  => {
    //         if( respond.status = "success"){
    //           this.profileImage = null
    //           this.authService.setUserData(
    //             this.user._id,
    //             respond.data.data.name,
    //             respond.data.data.surname,
    //             this.user.bio,
    //             this.user.email,
    //             this.user.role,
    //             this.user.photoProfile,
    //             this.user.photoCover,
    //             this.user.photoColor,
    //             this.user.coins,
    //             this.user.token,
    //             this.user.tokenExpirationDate
    //           )
    //           this.imageChangedEvent = null
    //           this.toastrService.success( "Successfully updated");
    //         ;
    //         }
    //     },
    //     respond_error => {

    //         let error_message = respond_error.error.message;
    //         this.toastrService.error(error_message);            
    //     }
    // ); 
  }

  fileChangeEvent(event: any): void {
    this.imageChangedEvent = event ;
  }
  async imageCropped(event: ImageCroppedEvent) {
      this.croppedImage = event.base64;
      let File : any = await base64ToFile(this.croppedImage);
      this.compressImageService.compress(File).subscribe( result =>{
        this.primaryInfoForm.patchValue({ profileImagePath: result });
      })
  }
  imageLoaded(event) {
    // show cropper
  }
  cropperReady(event) {
    // cropper ready
  }
  loadImageFailed() {
    // show message
  }

  ngOnDestroy(){
    this.loadJsService.removeScripts()
  }

}
