var express = require("express");
// var favicon = require('serve-favicon');
var logger = require("morgan");
var bodyParser = require("body-parser");
var busboyBodyParser = require("busboy-body-parser");
const AppError = require("./utils/appError");
const formData = require("express-form-data");
const compression = require("compression");
const cors = require("cors");
const path = require("path");

// Load environment variables
const dotenv = require('dotenv');
dotenv.config({ path: './config.env' });

const globalErrorHandler = require("./controllers/errorController");
const userRouter = require("./routes/userRoutes");
const fiberBankTransactionRoutes = require("./routes/fiberBankTransactionRoutes");
const promotionRoutes = require("./routes/promotionRoutes");
const updateRoutes = require("./routes/updateRoutes");
const promotionAvailableRoutes = require("./routes/promotionAvailableRoutes");
const mainDataRoutes = require("./routes/mainDataRoutes");
const transactionRoutes = require("./routes/transactionRoutes");
const centralBankRoutes = require("./routes/centralBankRoutes");
const atmRoutes = require("./routes/atmRoutes");
const onlinePaymentRoutes = require("./routes/onlinePaymentRoutes");
const bloggerRoutes = require("./routes/bloggerRoutes");
const postRoutes = require("./routes/postRoutes");
const searchListShownRoutes = require("./routes/searchListShownRoutes");
const ministryOfFinanceRoutes = require("./routes/ministryOfFinanceRoutes");
const bannedSuggestedUserRoutes = require("./routes/bannedSuggestedUserRoutes");
const giftRoutes = require("./routes/giftRoutes");
const notificationNativeRoutes = require("./routes/notificationNativeRoutes");
const permissionCategoryRoutes = require("./routes/permissionCategoryRoutes");
const permissionRoutes = require("./routes/permissionRoutes");
const roleTitleRoutes = require("./routes/roleTitleRoutes");

var app = express();
app.use(formData.parse());
app.use(busboyBodyParser());
app.enable("trust proxy");
app.use(cors());
app.options("*", cors());

// app.set("view engine", "ejs");
const mongoose = require("mongoose");

// mongoose.Promise = require('bluebird');
// mongoose.connect('************************************************************************************', { promiseLibrary: require('bluebird') })
//   .then(() =>  console.log('connection succesful'))
//   .catch((err) => console.error("err: ",err));

// Use environment variable for database connection
const DB = process.env.DATABASE.replace('<password>', process.env.DATABASE_PASSWORD);

mongoose
  .connect(DB)
  .then(() => console.log("Database connection successful"))
  .catch((err) => console.error("Database connection error: ", err));

app.use(logger("dev"));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: "true" }));

app.use(express.json({ limit: "10kb" }));
app.use(express.urlencoded({ extended: true, limit: "10kb" }));

app.use(compression());

app.use(express.static(path.join(__dirname, "/dist/fiber-al")));
app.use("", express.static(path.join(__dirname, "/dist/fiber-al")));

app.use("/post", postRoutes);
app.use("/users", userRouter);
app.use("/fiberBankTransaction", fiberBankTransactionRoutes);
app.use("/promotion", promotionRoutes);
app.use("/promotionAvailable", promotionAvailableRoutes);
app.use("/update", upRoutes);
app.use("/mainData", mainDataRoutes);
app.use("/transaction", transactionRoutes);
app.use("/centralBank", centralBankRoutes);
app.use("/atm", atmRoutes);
app.use("/onlinePayment", onlinePaymentRoutes);
app.use("/blogger", bloggerRoutes);
app.use("/searchListShown", searchListShownRoutes);
app.use("/ministryOfFinance", ministryOfFinanceRoutes);
app.use("/bannedSuggestedUser", bannedSuggestedUserRoutes);
app.use("/gift", giftRoutes);
app.use("/notificationNative", notificationNativeRoutes);
app.use("/permissionCategory", permissionCategoryRoutes);
app.use("/permission", permissionRoutes);
app.use("/roleTitle", roleTitleRoutes);

app.all("*", (req, res, next) => {
  // next(new AppError(`Can't find ${req.originalUrl} on this server!`, 404));
  next();
});

app.use(globalErrorHandler);

module.exports = app;
