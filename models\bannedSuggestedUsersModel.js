const mongoose = require('mongoose');
const { MongooseFindByReference } = require('mongoose-find-by-reference');

const bannedSuggestedUserSchema = new mongoose.Schema(
  {
      user: {
        type: mongoose.Schema.ObjectId,
        ref: 'User',
        required: [true, 'Banned Suggested User Schema must have a User!'],
        unique: true
      },
      createdBy:{
        type: mongoose.Schema.ObjectId,
        ref: 'User',
        required: [true, 'must been created from a User!']
      },
      createdAt: {
        type: Date,
        default: Date.now
      }
  }

);

bannedSuggestedUserSchema.plugin(MongooseFindByReference);

const BannedSuggestedUsers = mongoose.model('BannedSuggestedUser', bannedSuggestedUserSchema);

module.exports = BannedSuggestedUsers;
