<div style="
position: relative;
overflow: auto;" class="projects-section">
  <mat-vertical-stepper #stepper>
    <mat-step st label="Promotion Details">
      <div style="gap: 1em" class="projects-send-promotion">
        <div
          style="
            display: flex;
            flex-direction: column;
            overflow: auto;
            flex-grow: 1;
            height: 100%;
            border-radius: 15px;
            overflow-x: hidden;
            gap: 1em;
          ">
          <div style="flex-grow: 1; border-radius: 15px; overflow-y: auto">
            <div>
              <div 
              (click)="fileInputThumbnail.click()"
              class="input-image-send-promotion">
                <img
                *ngIf="!img"
                (click)="fileInputThumbnail.click()"
                  style="height: 30px; width: 30px"
                  src="assets/icons/photo-camera.svg"
                  alt=""
                />
                <span 
                *ngIf="!img"
                (click)="fileInputThumbnail.click()">Drag image here or click to upload</span>
                <div
                *ngIf="img"
                  style="
                    display: flex;
                    justify-content: center;
                    position: relative;
                  "
                >
                  <img
                    [src]="img"
                    class="promotion-sent-image"
                    alt=""
                  />
                  <img
                  (click)="
                  selectedFile=null;
                  img=null"
                    style="
                      height: 20px;
                      width: 20px;
                      position: absolute;
                      top: 0;
                      right: 1em;
                    "
                    src="assets/icons/close.svg"
                    alt=""
                  />
                </div>
              </div>

              <div style="display: flex; width: 100%; gap: 1em">
                <div
                  style="
                    background: var(--sidebar);
                    border-radius: 14px;
                    margin-top: 1em;
                    padding: 1rem 0.7rem;
                    display: flex;
                    flex-direction: column;
                    gap: 1em;
                    width: 100%;
                  "
                >
                  <div class="input-send-promotion">
                    <span>Cost for share</span>
                    <input
                      [(ngModel)]="cost" 
                      autocomplete="false"
                      type="number"
                      placeholder="Cost for share..."
                    />
                  </div>
                </div>
                <div
                  style="
                    background: var(--sidebar);
                    border-radius: 14px;
                    margin-top: 1em;
                    padding: 1rem 0.7rem;
                    display: flex;
                    flex-direction: column;
                    gap: 1em;
                    width: 100%;
                  "
                >
                  <div class="input-send-promotion">
                    <span>Allowed to share</span>
                    <input
                      [(ngModel)]="allowedToShare" 
                      autocomplete="false"
                      type="number"
                      placeholder="Allowed to share..."
                    />
                  </div>
                </div>
              </div>
              <div style="display: flex; width: 100%; gap: 1em">
                <div
                  style="
                    background: var(--sidebar);
                    border-radius: 14px;
                    margin-top: 1em;
                    padding: 1rem 0.7rem;
                    display: flex;
                    flex-direction: column;
                    gap: 1em;
                    width: 100%;
                  "
                >
                  <div class="input-send-promotion">
                    <span>Total cost</span>
                    <input
                      [(ngModel)]="cost*allowedToShare" 
                      disabled="true"
                      autocomplete="false"
                      type="number"
                      placeholder="Total cost..."
                    />
                  </div>
                </div>
                <div
                  style="
                    background: var(--sidebar);
                    border-radius: 14px;
                    margin-top: 1em;
                    padding: 1rem 0.7rem;
                    display: flex;
                    flex-direction: column;
                    gap: 1em;
                    width: 100%;
                  "
                >
                  <div class="input-send-promotion">
                    <span>Link Url</span>
                    <input
                      [(ngModel)]="linkUrl" 
                      autocomplete="false"
                      placeholder="Link Url..."
                    />
                  </div>
                </div>
              </div>
              <div style="display: flex; width: 100%; gap: 1em">
                <div
                  style="
                    background: var(--sidebar);
                    border-radius: 14px;
                    margin-top: 1em;
                    padding: 1rem 0.7rem;
                    display: flex;
                    flex-direction: column;
                    gap: 1em;
                    width: 100%;
                  "
                >
                  <div class="input-send-promotion">
                    <span>Time allowed to share</span>
                    <input
                      [(ngModel)]="timeAllowedToShareMinutes" 
                      autocomplete="false"
                      type="number"
                      placeholder="Time allowed to share..."
                    />
                  </div>
                </div>
                <div
                  style="
                    background: var(--sidebar);
                    border-radius: 14px;
                    margin-top: 1em;
                    padding: 1rem 0.7rem;
                    display: flex;
                    flex-direction: column;
                    gap: 1em;
                    width: 100%;
                  "
                >
                  <div class="input-send-promotion">
                    <span
                      >Minutes difference between users allowed to share</span
                    >
                    <input
                      [(ngModel)]="minutesDiffernce" 
                      autocomplete="false"
                      type="number"
                      placeholder="Minutes difference.."
                    />
                  </div>
                </div>
              </div>
              <div style="display: flex; width: 100%; gap: 1em">
                <div
                  style="
                    background: var(--sidebar);
                    border-radius: 14px;
                    margin-top: 1em;
                    padding: 1rem 0.7rem;
                    display: flex;
                    flex-direction: column;
                    gap: 1em;
                    width: 100%;
                  "
                >
                  <div class="input-send-promotion">
                    <span>Start to promote</span>
                    <input 
                    class="form-control"
                    [(ngModel)]="startPromoteDateTime" 
                    [owlDateTime]="dt1" [owlDateTimeTrigger]="dt1" placeholder="Date Time">
                    <owl-date-time #dt1></owl-date-time>    
                  </div>
                </div>
              </div>
            </div>
          </div>
          <input 
          class="input-image"
          #fileInputThumbnail accept="image/*" type="file" (change)="selectThumbnail($event)" />
        </div>
        <div
          style="
            display: flex;
            border-radius: 20px;
            overflow: auto;
            aspect-ratio: 7/6;
          "
        >
          <!-- first screen mobile -->
          <div
            style="
              background-image: url(assets/iphone.svg);
              background-repeat: no-repeat;
              background-position: center;
              background-size: contain;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 100%;
              aspect-ratio: 7/18;
            "
          >
            <div
              style="
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 80%;
              "
            >
              <div
                style="
                  height: 92%;
                  width: 100%;
                  margin: 20px 0 0 0;
                  border-radius: 0 0 35px 35px;
                  display: flex;
                  flex-direction: column;
                  gap: 1em;
                  align-items: center;
                  max-height: 670px;
                "
              >
                <div
                  style="
                    display: flex;
                    align-items: center;
                    padding: 0 1.5em;
                    gap: 5px;
                    margin-top: 5px;
                    width: 100%;
                  "
                >
                  <img
                    style="height: 30px; width: 30px"
                    src="/assets/fiberLogo.svg"
                    alt=""
                  />
                </div>
                <div
                  style="
                    width: 100%;
                    padding: 0 6%;
                    display: flex;
                    flex-direction: column;
                    overflow: auto;
                    height: 100%;
                  "
                >
                  <div 
                  *ngIf="img"
                  style="position: relative; height: 230px; width: 100%">
                    <div
                      style="
                        z-index: 1;
                        position: absolute;
                        top: 1em;
                        right: 1em;
                        width: 69px;
                        background: var(--action-color-50);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 5px;
                        border-radius: 11px;
                      "
                    >
                      <span style="line-height: 2">0</span>
                      <img
                        style="height: 12px; width: 12px"
                        src="assets/icons/share.svg"
                        alt=""
                      />
                    </div>
                    <img
                      [src]="thumbnail"
                      class="promotion-sent-image-preview"
                      alt=""
                    />
                    <div
                      style="
                        z-index: 1;
                        position: absolute;
                        bottom: 1em;
                        padding: 0 1em;
                        display: flex;
                        align-items: center;
                        gap: 5px;
                      "
                    >
                      <img
                        style="height: 12px; width: 12px"
                        src="assets/icons/coinSvg.svg"
                        alt=""
                      />
                      <span class="no-wrap-1-line" style="font-size: 11px">
                        {{cost}} credits (Share and win)
                      </span>
                    </div>
                  </div>
                <!-- skeletion -->
                 <div 
                 *ngIf="!img"
                class="skeleton2"
                  style="position: relative; height: 230px; width: 100%;opacity: .6;">
                    <div
                    
                      style="
                        z-index: 1;
                        position: absolute;
                        top: 1em;
                        right: 1em;
                        width: 69px;
                        background: var(--action-color-50);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 5px;
                        border-radius: 11px;
                      "
                    >
                      <span style="line-height: 2"><span style="opacity: 0;">0</span></span>
                      <img
                        style="height: 12px; width: 12px;opacity: 0;"
                        src="assets/icons/share.svg"
                        alt=""
                      />
                    </div>
                    <img
                      style="opacity: 0;"
                      class="promotion-sent-image-preview "
                      alt=""
                      [src]="img"
                    />
                    <div
                      style="
                        z-index: 1;
                        position: absolute;
                        bottom: 1em;
                        padding: 0 1em;
                        display: flex;
                        align-items: center;
                        gap: 5px;
                      "
                    >
                      <div
                        class="skeleton1"
                        style="height: 12px; width: 12px;"
                      >
                      </div>
                      <span class="no-wrap-1-line skeleton1" style="font-size: 11px">
                       <span style="opacity: 0;">{{cost}} credits (Share and win)</span> 
                      </span>
                    </div>
                  </div>
                  <div
                    style="
                      margin-top: auto;
                      display: flex;
                      padding: 16px 1em;
                      justify-content: space-between;
                    "
                  >
                    <img
                      style="width: 9%"
                      src="assets/icons/bottomTabIcons/HomeIc.svg"
                      alt=""
                    />
                    <img
                      style="width: 9%; scale: 1.2"
                      src="assets/icons/bottomTabIcons/PromotionIconSelected.svg"
                      alt=""
                    />
                    <div
                      style="
                        width: 9%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                      "
                    >
                      <span
                        style="
                          background: var(--action-color);
                          padding: 4px;
                          height: 18px;
                          width: 18px;
                          display: flex;
                          justify-content: center;
                          align-items: center;
                          border-radius: 50%;
                          transform: translateY(-4px);
                          scale: 1.7;
                          font-size: 18px;
                        "
                        >+</span
                      >
                    </div>
                    <img
                      style="width: 9%"
                      src="assets/icons/bottomTabIcons/ChatIcon.svg"
                      alt=""
                    />
                    <img
                      style="width: 9%; scale: 1.1"
                      src="assets/icons/bottomTabIcons/ProfileIcon.svg"
                      alt=""
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Second screen mobile -->
          <div
            style="
              background-image: url(assets/iphone.svg);
              background-repeat: no-repeat;
              background-position: center;
              background-size: contain;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 100%;
              aspect-ratio: 7/18;
            "
          >
            <div
              style="
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 80%;
              "
            >
              <div
                style="
                  height: 92%;
                  width: 100%;
                  margin: 20px 0 0 0;
                  border-radius: 0 0 35px 35px;
                  display: flex;
                  flex-direction: column;
                  gap: 1em;
                  align-items: center;
                  max-height: 670px;
                "
              >
                <div
                  style="
                    display: flex;
                    align-items: center;
                    padding: 0 1.5em;
                    gap: 5px;
                    margin-top: 5px;
                    width: 100%;
                  "
                >
                  <img
                    style="height: 30px; width: 30px"
                    src="/assets/fiberLogo.svg"
                    alt=""
                  />
                  <div
                    style="
                      background-color: black;
                      flex: 1;
                      height: 28px;
                      border-radius: 10px;
                      box-shadow: 0px 0px 4px 0px rgba(172, 172, 172, 0.5);
                      display: flex;
                      justify-content: space-between;
                      align-items: center;
                      padding: 0 6px;
                    "
                  >
                    <span
                      style="
                        font-size: 9px;
                        color: rgb(210, 210, 210);
                        line-height: 1;
                      "
                      class="no-wrap-1-line"
                      >Search on FiberAl</span
                    ><svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="12"
                      fill="none"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      class="feather feather-search"
                      viewBox="0 0 24 24"
                    >
                      <defs></defs>
                      <circle cx="11" cy="11" r="8"></circle>
                      <path d="M21 21l-4.35-4.35"></path>
                    </svg>
                  </div>
                  <img
                    style="height: 20px; width: 20px"
                    src="/assets/icons/notification.svg"
                    alt=""
                  />
                  <img
                    style="height: 20px; width: 20px"
                    src="/assets/icons/realTime.svg"
                    alt=""
                  />
                </div>
                <div
                  style="
                    width: 100%;
                    padding: 0 6%;
                    display: flex;
                    flex-direction: column;
                    overflow: auto;
                    height: 100%;
                  "
                >
                  <div
                  *ngIf="img"
                    style="
                      position: relative;
                      height: 86%;
                      width: 100%;
                      background-color: rgb(10, 10, 10);
                      border-radius: 10px;
                    "
                  >
                    <div
                      style="
                        z-index: 1;
                        position: absolute;
                        left: 1em;
                        top: 72%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 5px;
                        border-radius: 11px;
                      "
                    >
                      <span
                        class="no-wrap-1-line"
                        style="line-height: 2; font-size: 10px"
                        >{{linkUrl}}</span
                      >
                    </div>
                    <img
                      [src]="img"
                      class="promotion-sent-image-preview-home"
                      alt=""
                    />
                    <div
                      style="
                        z-index: 1;
                        position: absolute;
                        bottom: 1em;
                        padding: 0 1em;
                        display: flex;
                        align-items: center;
                        gap: 5px;
                        width: 100%;
                      "
                    >
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          gap: 4px;
                          background: gray;
                          padding: 5px 1em;
                          border-radius: 20px;
                        "
                      >
                        <img
                          style="width: 22px; height: 22px; border-radius: 50%"
                          src="https://images.unsplash.com/photo-1497445462247-4330a224fdb1?w=500&h=500"
                          alt=""
                        />
                        <div style="display: grid">
                          <span
                            class="no-wrap-1-line"
                            style="font-size: 9px; line-height: 1"
                            >shared by</span
                          >
                          <span
                            class="no-wrap-1-line"
                            style="font-size: 11px; line-height: 1"
                            >{{me?.name}} {{me?.surname}}</span
                          >
                        </div>
                      </div>
                      <div
                        style="
                          border: 1px solid var(--action-color);
                          margin-left: auto;
                          padding: 1px 6px;
                          border-radius: 5px;
                        "
                      >
                        <span class="no-wrap-1-line" style="font-size: 10px"
                          >Visit Now</span
                        >
                      </div>
                    </div>
                  </div>
                  <div
                  *ngIf="!thumbnail"
                  class="skeleton2"
                    style="
                      position: relative;
                      height: 86%;
                      width: 100%;
                      background-color: rgb(10, 10, 10);
                      border-radius: 10px;
                    "
                  >
                    <div
                      style="
                        z-index: 1;
                        position: absolute;
                        left: 1em;
                        top: 72%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 5px;
                        border-radius: 11px;
                      "
                    >
                      <span
                        class="no-wrap-1-line skeleton1"
                        style="line-height: 1; font-size: 10px"
                        ><span style="opacity: 0;">https://speedtaxi.al</span></span
                      >
                    </div>
                    <div
                      class="promotion-sent-image-preview-home skeleton1"
                      alt=""
                    ></div>
                    <div
                      style="
                        z-index: 1;
                        position: absolute;
                        bottom: 1em;
                        padding: 0 1em;
                        display: flex;
                        align-items: center;
                        gap: 5px;
                        width: 100%;
                      "
                    >
                      <div
                        class="skeleton1"
                        style="
                          display: flex;
                          align-items: center;
                          gap: 4px;
                          padding: 5px 1em;
                          border-radius: 20px;
                        "
                      >

                        <div
                        class="skeleton2"
                          style="width: 22px; height: 22px; border-radius: 50%"
                          alt=""
                        >
                        </div>
                        <div style="display: grid;gap: 4px;">
                          <span
                            class="no-wrap-1-line skeleton2"
                            style="font-size: 9px; line-height: .5"
                            ><span style="opacity: 0;">shared by </span></span
                          >
                          <span
                            class="no-wrap-1-line skeleton2"
                            style="font-size: 9px; line-height: .5"
                            ><span style="opacity: 0;">shared by </span></span
                          >
                        </div>
                      </div>
                      <div
                      class="skeleton1"
                        style="
                          margin-left: auto;
                          padding: 1px 6px;
                          border-radius: 5px;
                        "
                      >
                      <span
                      class="no-wrap-1-line "
                      style="font-size: 9px; line-height: .5"
                      ><span style="opacity: 0;">shared by </span></span
                    >
                      </div>
                    </div>
                  </div>

                  <div
                    style="
                      margin-top: auto;
                      display: flex;
                      padding: 16px 1em;
                      justify-content: space-between;
                    "
                  >
                    <img
                      style="width: 9%"
                      src="assets/icons/bottomTabIcons/homeAn.svg"
                      alt=""
                    />
                    <img
                      style="width: 9%"
                      src="assets/icons/bottomTabIcons/PromotionIcon.svg"
                      alt=""
                    />
                    <div
                      style="
                        width: 9%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                      "
                    >
                      <span
                        style="
                          background: var(--action-color);
                          padding: 4px;
                          height: 18px;
                          width: 18px;
                          display: flex;
                          justify-content: center;
                          align-items: center;
                          border-radius: 50%;
                          transform: translateY(-4px);
                          scale: 1.7;
                          font-size: 18px;
                        "
                        >+</span
                      >
                    </div>
                    <img
                      style="width: 9%"
                      src="assets/icons/bottomTabIcons/ChatIcon.svg"
                      alt=""
                    />
                    <img
                      style="width: 9%; scale: 1.1"
                      src="assets/icons/bottomTabIcons/ProfileIcon.svg"
                      alt=""
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </mat-step>
    <mat-step label="Filters">
      <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 1em">
        <div
          style="
            display: flex;
            flex-direction: column;
            overflow: auto;
            flex-grow: 1;
            height: 100%;
            border-radius: 15px;
            overflow-x: hidden;
            gap: 1em;
          "
        >
          <div style="flex-grow: 1; border-radius: 15px; overflow-y: auto">
            <div style="display: flex; gap: 1em">
              <div>
                <div style="display: flex; width: 100%; gap: 1em">
                  <div
                    style="
                      background: var(--sidebar);
                      border-radius: 14px;
                      margin-top: 1em;
                      padding: 1rem 0.7rem;
                      display: flex;
                      flex-direction: column;
                      gap: 1em;
                      width: 100%;
                      height: fit-content;
                    "
                  >
                    <div style="padding: 1em" class="input-send-promotion">
                      <div style="display: flex; align-items: center">
                        <span>Followers Count</span>
                        <p style="margin: 0 0 0 auto">
                          <mat-checkbox 
                          (change)="followersCountChange($event)"
                          color="dark">
                            <span 
                            *ngIf="!followersCountActive"
                            style="opacity: 0.7">
                              Enable</span>
                            
                            <span 
                            *ngIf="followersCountActive"
                            style="opacity: 0.7">
                              Disable</span>
                          </mat-checkbox>
                        </p>
                      </div>
                      <div 
                      *ngIf="followersCountActive"
                      style="display: flex; gap: 1em; align-items: center">
                        <input
                          class="send-promotion-input"
                          autocomplete="false"
                          type="number"
                          placeholder="From"
                          (ngModelChange)="search({
                            searchText: $event,
                            inputForm: 'followersCountMin'},'followers')"
                          [(ngModel)]="followersCountMin"
                        />
                        <!-- (keyup)="search($event,'followersCountMin')" -->
                        <span style="font-size: 20px; font-weight: 900">-</span>
                        <input
                          class="send-promotion-input"
                          autocomplete="false"
                          type="number"
                          placeholder="To"
                          (ngModelChange)="search({
                            searchText: $event,
                            inputForm: 'followersCountMax'},'followers')"
                          [(ngModel)]="followersCountMax"
                        />
                      </div>
                      <!-- <div 
                      *ngIf="followersCountActive"
                      class="custom-slider">
                        <ngx-slider
                          [(value)]="followersCountMin"
                          [(highValue)]="followersCountMax"
                          [options]="options"
                          (userChange)="search($event,'followers')"
                        ></ngx-slider>
                      </div> -->
                    </div>
                  </div>
                  <div
                    style="
                      background: var(--sidebar);
                      border-radius: 14px;
                      margin-top: 1em;
                      padding: 1rem 0.7rem;
                      display: flex;
                      flex-direction: column;
                      gap: 1em;
                      width: 100%;
                      height: fit-content;
                    "
                  >
                    <div style="padding: 1em" class="input-send-promotion">
                      <div style="display: flex; align-items: center">
                        <span>Following Count</span>
                        <p style="margin: 0 0 0 auto">
                          <mat-checkbox 
                          (change)="followingCountChange($event)"
                          color="dark">
                            <span 
                            *ngIf="!followingCountActive"
                            style="opacity: 0.7">
                              Enable</span>
                            
                            <span 
                            *ngIf="followingCountActive"
                            style="opacity: 0.7">
                              Disable</span>
                          </mat-checkbox>
                        </p>
                      </div>
                      <div 
                      *ngIf="followingCountActive"
                      style="display: flex; gap: 1em; align-items: center">
                        <input
                          class="send-promotion-input"
                          autocomplete="false"
                          type="number"
                          placeholder="From"
                          [(ngModel)]="followingCountMin"
                          (ngModelChange)="search({
                            searchText: $event,
                            inputForm: 'followingCountMin'},'following')"
                        />
                        <span style="font-size: 20px; font-weight: 900">-</span>
                        <input
                          class="send-promotion-input"
                          autocomplete="false"
                          type="number"
                          placeholder="To"
                          [(ngModel)]="followingCountMax"
                          (ngModelChange)="search({
                            searchText: $event,
                            inputForm: 'followingCountMax'},'following')"
                        />
                      </div>
                      <!-- <div 
                      *ngIf="followingCountActive"
                      class="custom-slider">
                        <ngx-slider
                          [(value)]="followingCountMin"
                          [(highValue)]="followingCountMax"
                          [options]="options"
                          (userChange)="search($event,'following')"
                        ></ngx-slider>
                      </div> -->
                    </div>
                  </div>
                </div>
                <div style="display: flex; width: 100%; gap: 1em">
                  <div
                    style="
                      background: var(--sidebar);
                      border-radius: 14px;
                      margin-top: 1em;
                      padding: 1rem 0.7rem;
                      display: flex;
                      flex-direction: column;
                      gap: 1em;
                      width: 100%;
                      height: fit-content;
                    "
                  >
                    <div style="padding: 1em" class="input-send-promotion">
                      <div style="display: flex; align-items: center">
                        <span>Post Count</span>
                        <p style="margin: 0 0 0 auto">
                          <mat-checkbox 
                          (change)="postCountChange($event)"
                          color="dark">
                            <span 
                            *ngIf="!postCountActive"
                            style="opacity: 0.7">
                              Enable</span>
                            
                            <span 
                            *ngIf="postCountActive"
                            style="opacity: 0.7">
                              Disable</span>
                          </mat-checkbox>
                        </p>
                      </div>
                      <div 
                      *ngIf="postCountActive"
                      style="display: flex; gap: 1em; align-items: center">
                        <input
                          class="send-promotion-input"
                          autocomplete="false"
                          type="number"
                          placeholder="From"
                          [(ngModel)]="postCountMin"
                          (ngModelChange)="search({
                            searchText: $event,
                            inputForm: 'postCountMin'},'post')"
                        />
                        <span style="font-size: 20px; font-weight: 900">-</span>
                        <input
                          class="send-promotion-input"
                          autocomplete="false"
                          type="number"
                          placeholder="To"
                          [(ngModel)]="postCountMax"
                          (ngModelChange)="search({
                            searchText: $event,
                            inputForm: 'postCountMax'},'post')"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    style="
                      background: var(--sidebar);
                      border-radius: 14px;
                      margin-top: 1em;
                      padding: 1rem 0.7rem;
                      display: flex;
                      flex-direction: column;
                      gap: 1em;
                      width: 100%;
                    "
                  >
                    <div style="padding: 1em" class="input-send-promotion">
                      <div style="display: flex; align-items: center">
                        <span>Earn Count</span>
                        <p style="margin: 0 0 0 auto">
                          <mat-checkbox 
                          (change)="earnCountChange($event)"
                          color="dark">
                            <span 
                            *ngIf="!earnCountActive"
                            style="opacity: 0.7">
                              Enable</span>
                            
                            <span 
                            *ngIf="earnCountActive"
                            style="opacity: 0.7">
                              Disable</span>
                          </mat-checkbox>
                        </p>
                      </div>
                      <div 
                      *ngIf="earnCountActive"
                      style="display: flex; gap: 1em; align-items: center">
                        <input
                          class="send-promotion-input"
                          autocomplete="false"
                          type="number"
                          placeholder="From"
                          [(ngModel)]="earnCountMin"
                          (ngModelChange)="search({
                            searchText: $event,
                            inputForm: 'earnCountMin'},'earn')"
                        />
                        <span style="font-size: 20px; font-weight: 900">-</span>
                        <input
                          class="send-promotion-input"
                          autocomplete="false"
                          type="number"
                          placeholder="To"
                          [(ngModel)]="earnCountMax"
                          (ngModelChange)="search({
                            searchText: $event,
                            inputForm: 'earnCountMax'},'earn')"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
        style="
          display: flex;
          flex-direction: column;
          overflow: auto;
          flex-grow: 1;
          height: 97%;
          border-radius: 14px;
          overflow-x: hidden;
          gap: 1em;
          background: var(--sidebar);
          margin-top: 1em;
          padding: 1em 0;">

          <div
          *ngIf="startLoading"
          style="
            position: absolute;
            background-color: rgba(0, 0, 0, 0.226);
            background-image: url(assets/blur.svg);
            background-size: cover;
            background-repeat: repeat;
            height: 90%;
            width: 32%;
            margin-top: -12px;
            border-radius: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
          ">
          <img style="width: 30%;" src="assets/animatedIcons/fiber_al_loader.svg" alt="">
          </div>

          <div
            style="
              flex-grow: 1;
              border-radius: 15px;
              overflow-y: auto;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-direction: column;
              gap: 1em;
            "
          >
            <div style="display: flex; gap: 1em">
              <div style="display: flex; flex-direction: column">
                <div
                  style="
                    border-radius: 14px;
                    margin-top: 1em;
                    padding: 1rem 0.7rem;
                    display: flex;
                    flex-direction: column;
                    gap: 1em;
                    width: 100%;
                    border: 1px solid var(--app-bg);
                  "
                >
                  <div class="input-send-promotion">
                    <span style="text-align: center"
                      >Total users with filter selected</span
                    >
                    <input
                      disabled="true"
                      value="{{
                        totalUsersWithFilter - checkboxFalseUsersId.length
                      }}"
                      autocomplete="false"
                      placeholder="0"
                      type="number"
                    />
                  </div>
                </div>
                <div
                  style="
                    border-radius: 14px;
                    margin-top: 1em;
                    padding: 1rem 0.7rem;
                    display: flex;
                    flex-direction: column;
                    gap: 1em;
                    width: 100%;
                    border: 1px solid var(--app-bg);
                  "
                >
                  <div class="input-send-promotion">
                    <span style="text-align: center"
                      >Total users with filter</span
                    >
                    <input
                      [(ngModel)]="totalUsersWithFilter"
                      disabled="true"
                      autocomplete="false"
                      type="number"
                      placeholder="0"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div style="display: flex; width: 100%; justify-content: center">
              <span
                (click)="openUserModal()"
                style="
                  background: var(--action-color);
                  padding: 9px 2em;
                  border-radius: 10px;
                  line-height: 2;
                  width: 53%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                "
                >SHOW MORE</span
              >
            </div>
          </div>

        </div>
      </div>
    </mat-step>
    <mat-step>
      <ng-template matStepLabel>Done</ng-template>
      <p>You are now done.</p>
      <div style="display: flex; gap: 1em">
        <!-- <button >Back</button> -->
        <span 
        *ngIf="!startSubmitLoading"
        class="buttonReset" (click)="submit()">
          Save
        </span>
      </div>
    </mat-step>
  </mat-vertical-stepper>
  <div
  *ngIf="startSubmitLoading"
  style="
    position: absolute;
    background-color: rgba(0, 0, 0, 0.226);
    background-image: url(/assets/blur.svg);
    background-size: cover;
    background-repeat: repeat;
    height: 100%;
    width: 100%;
    margin-left: -32px;
    margin-top: -32px;
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 20px;
  ">
      <img
        style="width: 80%; max-width: 361px"
        src="/assets/animatedIcons/phone_final.svg"
        alt=""
      />
  </div>
</div>
