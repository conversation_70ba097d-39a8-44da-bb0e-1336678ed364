@import "~ng-pick-datetime/assets/style/picker.min.css";

:root {
  --app-bg: #212d3b;
  --sidebar: rgba(21, 30, 47, 1);
  --sidebar-main-color: #fff;
  --table-border: #1a2131;
  --table-header: #1a2131;
  --app-content-main-color: #fff;
  --sidebar-link: #fff;
  --sidebar-active-link: #1d283c;
  --sidebar-hover-link: #1a2539;
  --action-color: #32dbc6;
  --action-color-50: #32dbc688;
  --action-color-30: #32dbc634;
  --action-color-hover: #37fbe4;
  --app-content-secondary-color: #1f1d2b;
  --filter-reset: #2c394f;
  --filter-shadow: rgba(16, 24, 39, 0.8) 0px 6px 12px -2px,
    rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;
  --text-light: rgb(18, 18, 18);
  --text-dark: #fff;
  --button-color-primary: #32dbc6;
  --button-color-primary-50: #32dbc688;
  --button-color-accent: #b3eee7;
  --green: #09aa29;
  --blue: #2b6fff;
  --red: #f83015;
  --gray: #a5a5a5;

  --green-trans: #09aa29aa;
  --blue-trans: #2b6fffaa;
  --red-trans: #f83015aa;

  --font-size-xxxl: 40px;
  --font-size-xxl: 35px;
  --font-size-xl: 30px;
  --font-size-l: 25px;
  --font-size-m: 20px;
  --font-size-s: 15px;
  --font-size-xs: 10px;

  --font-size-big: 21px;
  --font-size-primary: 19px;
  --font-size-title: 18px;
  --font-size-small: 12px;

  --product-image-grid-height: 150px;

  --product-view-grid-padding: 8em 0.5em 0.5em 0.5em;

  --app-container: #1f1d2b;
  --app-container: #111827;
  --main-color: #fff;
  --secondary-color: rgba(255, 255, 255, 0.8);
  --projects-section: #1f2937;
  --link-color: rgba(255, 255, 255, 0.8);
  --link-color-hover: rgba(195, 207, 244, 0.1);
  --link-color-active-bg: rgba(195, 207, 244, 0.2);
  --button-bg: #1f2937;
  --search-area-bg: #1f2937;
  --message-box-hover: #243244;
  --message-box-border: rgba(255, 255, 255, 0.1);
  --star: #ffd92c;
  --light-font: rgba(255, 255, 255, 0.8);
  --more-list-bg: #2f3142;
  --more-list-bg-hover: rgba(195, 207, 244, 0.1);
  --more-list-shadow: rgba(195, 207, 244, 0.1);
  --message-btn: rgba(195, 207, 244, 0.1);

  --c-gray-900: #000000;
  --c-gray-800: #1f1f1f;
  --c-gray-700: #2e2e2e;
  --c-gray-600: #313131;
  --c-gray-500: #969593;
  --c-gray-400: #a6a6a6;
  --c-gray-300: #bdbbb7;
  --c-gray-200: #f1f1f1;
  --c-gray-100: #ffffff;
  --c-green-500: #45ffbc;
  --c-olive-500: #e3ffa8;

  --c-white: var(--c-gray-100);
  --c-text-primary: var(--c-gray-100);
  --c-text-secondary: var(--c-gray-200);
  --c-text-tertiary: var(--c-gray-500);
}

* {
  box-sizing: border-box;
}

/* :root {
    --app-container: #f3f6fd;
    --main-color: #1f1c2e;
    --secondary-color: #4a4a4a;
    --link-color: #1f1c2e;
    --link-color-hover: #c3cff4;
    --link-color-active: #fff;
    --link-color-active-bg: #1f1c2e;
    --projects-section: #fff;
    --message-box-hover: #fafcff;
    --message-box-border: #e9ebf0;
    --more-list-bg: #fff;
    --more-list-bg-hover: #f6fbff;
    --more-list-shadow: rgba(209, 209, 209, 0.4);
    --button-bg: #1f1c24;
    --search-area-bg: #fff;
    --star: #1ff1c2e;
    --message-btn: #fff;
  } */

html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
}

body {
  font-family: monospace;
  overflow: hidden;
  display: flex;
  justify-content: center;
  background-color: var(--app-container);
}

::-webkit-scrollbar {
  width: 10px !important;
}

.left-sidebar::-webkit-scrollbar {
  width: 5px !important;
}

/* Track */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgb(33, 33, 33) !important;
  border-radius: 10px !important;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #1b1b1b82 !important;
  border-radius: 10px !important;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #0f0f0f !important;
}

/* 
button,
a {
  cursor: pointer;
}

p,
span,
div {
  color: white;
  font-size: 16px;
  font-weight: 600;
}

p,
span,
div {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
 */
.disp-grid {
  display: grid !important;
}

.disp-flex {
  display: flex !important;
}
.m-l-auto {
  margin-left: auto;
}
.j-c-center {
  justify-content: center;
}
.a-i-center {
  align-items: center;
}
.j-c-start {
  justify-content: start;
}
.a-i-start {
  align-items: flex-start;
}
.j-c-end {
  justify-content: end;
}
.a-i-end {
  align-items: flex-end;
}
.gap-1 {
  gap: 1em;
}
.gap-05 {
  gap: 0.5em;
}
.no-wrap-1-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%; /* Change to desired width */
}
.no-wrap-1-line-200 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px; /* Change to desired width */
}
.shadow1 {
  box-shadow: 0 2px 6px 0 rgba(8, 8, 22, 0.2),
    0 24px 20px -24px rgba(71, 82, 107, 0.1);
}
@media screen and (max-height: 980px) {
  .dashRightContent {
    height: 80% !important;
    gap: 0.5em;
    padding: 1em;
  }
  .dashLeftContent {
    /* padding: 0em 1em; */
    /* margin: 3em 0em 0; */
  }
  .projects-section {
    padding: 1em 2em;
  }

  .finRightNum {
    font-size: var(--font-size-l);
    font-weight: 500;
    color: var(--c-text-primary);
    margin: 0;
    text-align: end;
    width: 90%;
  }

  .finRightStats {
    font-size: var(--font-size-m);
    font-weight: 500;
    color: var(--c-text-secondary);
    margin: 0;
    text-align: end;
    width: 90%;
  }

  .finRightTarget {
    font-size: 10px;
    font-weight: 600;
    color: var(--c-text-tertiary);
    margin: 0;
    text-align: end;
    width: 90%;
  }

  .statisticsWrapper {
    margin-bottom: -20px;
  }
}

/* @media screen and (max-height: 980px) {

  tr > * {
    padding: 0px 0px 5px 5px;
  }
  td div.value {
    background-color: var(--action-color-50);
    height: 0.3em;
    animation: animation reverse 1s linear;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    border-radius: 0 10px 10px 0;
  }
  th {
    border-right: solid 1px rgba(0, 0, 0, 0);
    width: 25%;
    text-align: left;
    font-size: 8px;
  }
 
} */
.creditsInputWrapper {
  height: 45px !important;
}
@media screen and (max-height: 890px) {
  .creditsInputWrapper {
    height: 45px !important;
  }
  .exchange-tile {
    padding: 1em !important;
  }
}

@media screen and (max-height: 980px) {
  .KWrapper p {
    margin: 0;
    font-size: 11px;
  }
  .mWrapper {
    font-size: 10px;
  }
  .mText {
    position: absolute;
    font-size: 10px;
    font-weight: 500;
    text-align: center;
  }
  .lWrapper {
    font-size: 14px;
  }
  .nucleus :nth-child(1) {
    height: 19px !important;
  }
  .nucleus :nth-child(2) {
    font-size: 30px !important;
  }
  .nucleus :nth-child(3) {
    font-size: 8px !important;
  }
  .quantityOfItemWrapper {
    margin-top: -3em;
  }

  .finRightStats {
    font-size: var(--font-size-m) !important;
  }
  .creditsInputWrapper {
    height: 45px !important;
  }
  .exchange-tile {
    padding: 1em !important;
  }
}
.app-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--app-container);
  transition: 0.2s;
  max-width: 1800px;
}
.app-container button,
.app-container input,
.app-container optgroup,
.app-container select,
.app-container textarea {
  font-family: monospace;
}
.app-content {
  display: flex;
  height: 100%;
  overflow: hidden;
  padding: 16px 24px 24px 0;
}
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 16px 24px;
  position: relative;
}
.app-header-left,
.app-header-right {
  display: flex;
  align-items: center;
}
.app-header-left {
  flex-grow: 1;
}
.app-header-right button {
  margin-left: 10px;
}
.app-icon {
  width: 26px;
  height: 2px;
  border-radius: 4px;
  background-color: var(--main-color);
  position: relative;
}
.app-icon:before,
.app-icon:after {
  content: "";
  position: absolute;
  width: 12px;
  height: 2px;
  border-radius: 4px;
  background-color: var(--main-color);
  left: 50%;
  transform: translatex(-50%);
}
.app-icon:before {
  top: -6px;
}
.app-icon:after {
  bottom: -6px;
}
.app-name {
  color: var(--main-color);
  margin: 0;
  font-size: 20px;
  line-height: 24px;
  font-weight: 700;
  margin: 0 32px;
}

.mode-switch {
  background-color: transparent;
  border: none;
  padding: 0;
  color: var(--main-color);
  display: flex;
  justify-content: center;
  align-items: center;
}

.search-wrapper {
  border-radius: 20px;
  background-color: var(--search-area-bg);
  padding-right: 12px;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 480px;
  color: var(--light-font);
  box-shadow: 0 2px 6px 0 rgba(136, 148, 171, 0.2),
    0 24px 20px -24px rgba(71, 82, 107, 0.1);
  overflow: hidden;
}
.dark .search-wrapper {
  box-shadow: none;
}

.search-input {
  border: none;
  flex: 1;
  outline: none;
  height: 100%;
  padding: 0 20px;
  font-size: 16px;
  background-color: var(--search-area-bg);
  color: var(--main-color);
}
.search-input:placeholder {
  color: var(--main-color);
  opacity: 0.6;
}

.add-btn {
  color: #fff;
  background-color: var(--button-bg);
  padding: 0;
  border: 0;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 1em;
  font-weight: 700;
  font-size: 20px;
}

.notification-btn {
  color: var(--main-color);
  padding: 0;
  border: 0;
  background-color: transparent;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.profile-btn {
  padding: 0;
  border: 0;
  background-color: transparent;
  display: flex;
  align-items: center;
  padding-left: 12px;
  border-left: 2px solid #ddd;
}
.profile-btn img {
  width: 32px;
  height: 32px;
  -o-object-fit: cover;
  object-fit: cover;
  border-radius: 50%;
  margin-right: 4px;
}
.profile-btn span {
  color: var(--main-color);
  font-size: 16px;
  line-height: 24px;
  font-weight: 700;
}

.page-content {
  flex: 1;
  width: 100%;
}

.app-sidebar {
  padding-top: 5px !important;
  padding: 40px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.app-sidebar-link {
  color: var(--main-color);
  color: var(--link-color);
  margin: 12px 0;
  transition: 0.2s;
  border-radius: 50%;
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  scale: 1.2;
}
.app-sidebar-link:hover {
  background-color: var(--link-color-hover);
  color: var(--link-color-active);
}
.app-sidebar-link.active {
  background-color: var(--link-color-active-bg);
  color: var(--link-color-active);
  scale: 1.3;
}

.projects-section {
  flex: 2;
  background-color: var(--projects-section);
  border-radius: 32px;
  padding: 32px 32px 0 32px;
  overflow: hidden;
  height: 100%;
  max-height: 900px;
  display: flex;
  flex-direction: column;
  gap: 1em;
}
.projects-section-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 32px;
}
.projects-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  color: var(--main-color);
}
.projects-section-header p {
  font-size: 24px;
  line-height: 32px;
  font-weight: 700;
  opacity: 0.9;
  margin: 0;
  color: var(--main-color);
}
.projects-section-header .time {
  font-size: 20px;
}

.projects-status {
  display: flex;
}

.item-status {
  display: flex;
  flex-direction: column;
  margin-right: 16px;
}
.item-status:not(:last-child) .status-type:after {
  content: "";
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translatey(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  border: 1px solid var(--secondary-color);
}

.status-number {
  font-size: 24px;
  line-height: 32px;
  font-weight: 700;
  color: var(--main-color);
}

.status-type {
  position: relative;
  padding-right: 24px;
  color: var(--secondary-color);
}

.search-bar {
  height: 34px;
  display: flex;
  width: 100%;
  max-width: 450px;
}
.search-bar input {
  width: 100%;
  height: 100%;
  border: none;
  background-color: var(--button-bg);
  border-radius: 8px;
  font-family: var(--body-font);
  font-size: 14px;
  font-weight: 500;
  padding: 0 40px 0 16px;
  box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 56.966 56.966' fill='%23717790c7'%3e%3cpath d='M55.146 51.887L41.588 37.786A22.926 22.926 0 0046.984 23c0-12.682-10.318-23-23-23s-23 10.318-23 23 10.318 23 23 23c4.761 0 9.298-1.436 13.177-4.162l13.661 14.208c.571.593 1.339.92 2.162.92.779 0 1.518-.297 2.079-.837a3.004 3.004 0 00.083-4.242zM23.984 6c9.374 0 17 7.626 17 17s-7.626 17-17 17-17-7.626-17-17 7.626-17 17-17z'/%3e%3c/svg%3e");
  background-size: 14px;
  background-repeat: no-repeat;
  background-position: 96%;
  color: #fff;
}
/* ------------------------------ PAGINTION BEGIN ------------------------------ */
.list-number {
  justify-content: space-between;
  align-items: center;
  gap: 1em;
}
.list-number > span {
  font-size: 18px;
  height: 26px;
  width: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.list-number > span.active {
  background-color: #32dbc6;
  border-radius: 5px;
  box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
}
.pager {
  margin: 0 0 0.75rem;
  font-size: 0;
  text-align: center;
  padding-inline-start: 0;
}
.showingInfo {
  position: relative;
  border-radius: 4px;
  display: block;
  text-align: center;
  width: fit-content;
  height: 2.625rem;
  line-height: 2.625rem;
  margin-left: -1px;
  color: #63676c;
  text-decoration: none;
  transition: 0.3s;
}
.pager__item {
  display: inline-block;
  vertical-align: top;
  font-size: 1.025rem;
  font-weight: bold;
  margin: 0 2px;
}
.pager__item.active .pager__link {
  background-color: #32dbc6;
  border-color: #32dbc6;
  color: #fff;
  text-decoration: none;
  box-shadow: 0 0 0 2px rgba(66, 66, 66, 0.37);
}
.pager__item--prev svg,
.pager__item--next svg {
  width: 8px;
  height: 12px;
}
.pager__item--next .pager__link svg {
  transform: rotate(180deg);
  transform-origin: center center;
}
.pager__link {
  position: relative;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 1.9rem !important;
  height: 1.9rem !important;
  line-height: 2.625rem;
  margin-left: -1px;
  color: #63676c;
  text-decoration: none;
  transition: 0.3s;
}
.pager__link:hover,
.pager__link:focus,
.pager__link:active {
  background-color: #32dbc782;
  border-color: #32dbc782;
  color: #fff;
  text-decoration: none;
}
.pager__link:hover svg path,
.pager__link:focus svg path,
.pager__link:active svg path {
  fill: #fff;
}
.pager .pager__item.active + .pager__item .pager__link,
.pager .pager__item:hover + .pager__item .pager__link {
  border-left-color: #32dbc7d5;
}
@media screen and (max-width: 795px) {
  .showingInfoWrapper {
    display: none;
  }
  .list-number {
    justify-content: center !important;
  }
}
@media screen and (max-width: 795px) {
  .pager__item {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }
  .pager__item.active,
  .pager__item:first-of-type,
  .pager__item:last-of-type,
  .pager__item:nth-of-type(2),
  .pager__item:nth-last-of-type(2) {
    position: initial;
    top: initial;
    left: initial;
  }
  .pager__item.active + li {
    position: initial;
    top: initial;
    left: initial;
  }
  .pager__link {
    width: 1.9rem !important;
    height: 1.9rem !important;
  }
}

.chart-svg {
  position: relative;
  max-width: 60px;
  min-width: 40px;
  flex: 1;
}

.circle-bg {
  fill: none;
  stroke: #eee;
  stroke-width: 2.5;
}

.circle {
  fill: none;
  stroke-width: 3;
  stroke-linecap: round;
  -webkit-animation: progress 1s ease-out forwards;
  animation: progress 1s ease-out forwards;
}

.circular-chart.orange .circle {
  stroke: #32dbc6;
}
.circular-chart.orange .circle-bg {
  stroke: #32dbc63c;
}
.circular-chart.green .circle-bg {
  stroke: var(--green-trans);
}
.circular-chart.blue .circle {
  stroke: #32dbc6;
}
.circular-chart.blue .circle-bg {
  stroke: #557b88;
}
.circular-chart.pink .circle {
  stroke: #ff7dcb;
}
.circular-chart.pink .circle-bg {
  stroke: #6f5684;
}

.percentage {
  fill: #fff;
  font-size: 0.5em;
  text-anchor: middle;
  font-weight: 400;
}
.downAnimation {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  box-shadow: 0 0 10px 4px #df1d1d51, 0 0 10px 4px #df1d1d29;
}
.upAnimation {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  box-shadow: 0 0 10px 4px #32dbc751, 0 0 10px 4px #32dbc729;
}
@-webkit-keyframes progress {
  0% {
    stroke-dasharray: 0 100;
  }
}

@keyframes progress {
  0% {
    stroke-dasharray: 0 100;
  }
}

p {
  cursor: pointer;
}

span {
  cursor: pointer;
}

/* ============================================== */
/* Skeletons */

.skeletonBG {
  background-color: #d7d7d7;
  background-image: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0.5),
    rgba(255, 255, 255, 0)
  );
  background-size: 260px 100%;
  background-repeat: no-repeat;
  background-position: left -70px top 0;
  -webkit-animation: shine 1s ease-in-out infinite;
  animation: shine 1s ease-in-out infinite;
}
.skeleton1 {
  border-radius: 1em;
  background-color: #191b29;
  background-image: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0),
    rgba(32, 36, 44, 0.5),
    rgba(255, 255, 255, 0)
  );
  background-size: 70px 100%;
  background-repeat: no-repeat;
  background-position: left -70px top 0;
  -webkit-animation: shine 1s ease-in-out infinite;
  animation: shine 1s ease-in-out infinite;
}

.skeleton2 {
  border-radius: 1em;
  background-color: #242c3f;
  background-image: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0),
    rgba(32, 36, 44, 0.5),
    rgba(255, 255, 255, 0)
  );
  background-size: 70px 100%;
  background-repeat: no-repeat;
  background-position: left -70px top 0;
  -webkit-animation: shine 1s ease-in-out infinite;
  animation: shine 1s ease-in-out infinite;
}
.postAvatar {
  cursor: pointer;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.postAvatar img {
  border-radius: 50%;
  width: 60px;
  height: 60px;
}

.postAvatar .initials {
  color: #ffffff;
  font-size: 20px;
  line-height: 19px;
  letter-spacing: 0.2625px;
}

.userImgBloggers {
}

.userImgBloggers {
  cursor: pointer;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.userImgBloggers img {
  border-radius: 50%;
  min-width: 44px;
  max-width: 44px;
  max-height: 44px;
  min-height: 44px;
}

.initialsClass {
  min-width: 44px;
  max-width: 44px;
  max-height: 44px;
  min-height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  font-size: 16px;
}
.userImgModalPromotionList {
  cursor: pointer;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.userImgModalPromotionList img {
  border-radius: 50%;
  min-width: 44px;
  max-width: 44px;
  max-height: 44px;
  min-height: 44px;
}

.initialsClassModalPromotionList {
  min-width: 44px;
  max-width: 44px;
  max-height: 44px;
  min-height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  font-size: 16px;
}

@-webkit-keyframes shine {
  to {
    background-position: right -40px top 0;
  }
}

@keyframes shine {
  to {
    background-position: right -40px top 0;
  }
}

.saveButton {
  background: var(--action-color);
  font-size: 17px;
  padding: 8px 2em;
  border-radius: 10px;
  cursor: pointer;
}
.saveButton:hover {
  background: var(--action-color-30);
  border: 1px solid var(--action-color);
}

html,
body {
  height: 100%;
}
body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
