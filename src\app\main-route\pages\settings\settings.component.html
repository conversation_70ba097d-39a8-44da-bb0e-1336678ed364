<div class="projects-section">
  <div
    style="
      overflow: auto;
      /* max-width: 100%; */
      display: grid;
      justify-content: center;
    "
  >
    <div style="width: 100%">
      <p
        style="font-size: 28px; font-weight: 800; line-height: 36px; margin: 0"
      >
        Fiber Settings
      </p>
      <p
        style="
          font-size: 15px;
          font-weight: 400;
          line-height: 21px;
          margin: 5px 0 1em 0;
        "
      >
        Menage all application settings in one place
      </p>
    </div>
    <div>
      <div class="settingWrapper" style="">
        <div style="display: flex; flex-direction: column; gap: 2em">
          <div
            style="
              display: grid;
              border: 1.2px solid var(--link-color-active-bg);
              border-radius: 10px;
            "
          >
            <div style="padding: 14px 22px 0px 22px">
              <span style="font-size: 20px">Social platform setup</span>
            </div>
            <div style="">
              <div
                (click)="redirectTo('/bloggers')"
                style="border-bottom: 1px solid var(--link-color-hover)"
              >
                <div
                  style="
                    cursor: pointer;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    padding: 14px 22px 14px 22px;
                    justify-content: space-between;
                  "
                >
                  <div style="width: 81%">
                    <p>Bloggers details</p>
                    <span
                      style="opacity: 0.5; font-weight: 500; font-size: 12px"
                      >Users suggested to follow, when dont have any
                      following</span
                    >
                  </div>
                  <img
                    style="margin: 1.5em 0 0 0; width: 12px; height: 12px"
                    src="assets/icons/next.svg"
                    alt=""
                  />
                </div>
              </div>

              <div
                (click)="redirectTo('/search-list-shown')"
                style="border-bottom: 1px solid var(--link-color-hover)"
              >
                <div
                  style="
                    cursor: pointer;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    padding: 14px 22px 14px 22px;
                    justify-content: space-between;
                  "
                >
                  <div style="width: 81%">
                    <p>Search users details</p>
                    <span
                      style="opacity: 0.5; font-weight: 500; font-size: 12px"
                    >
                      List of users on search page</span
                    >
                  </div>
                  <img
                    style="margin: 1.5em 0 0 0; width: 12px; height: 12px"
                    src="assets/icons/next.svg"
                    alt=""
                  />
                </div>
              </div>
              <div 
              (click)="redirectTo('/list-of-banned-suggested-users')"
              style="border-bottom: 1px solid var(--link-color-hover)">
                <div
                  style="
                    cursor: pointer;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    padding: 14px 22px 14px 22px;
                    justify-content: space-between;
                  "
                  href=""
                >
                  <div style="width: 81%">
                    <p>Banned Suggested Users</p>
                    <span
                      style="opacity: 0.5; font-weight: 500; font-size: 12px"
                      >Tracks users who are banned from being suggested </span
                    >
                  </div>
                  <img
                    style="margin: 1.5em 0 0 0; width: 12px; height: 12px"
                    src="assets/icons/next.svg"
                    alt=""
                  />
                </div>
              </div>
            </div>
          </div>
          <div
            style="
              display: grid;
              border: 1.2px solid var(--link-color-active-bg);
              border-radius: 10px;
            ">
            <div style="padding: 14px 22px 0px 22px">
              <span style="font-size: 20px">General Information</span>
            </div>
            <div>
              <div 
              (click)="redirectTo('/list-version')"
              style="border-bottom: 1px solid var(--link-color-hover)">
                <div
                  style="
                    cursor: pointer;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    padding: 14px 22px 14px 22px;
                    justify-content: space-between;
                  "
                >
                  <div style="width: 81%">
                    <p>App Versions</p>
                    <span
                      style="opacity: 0.5; font-weight: 500; font-size: 12px">
                      Refers to a specific release or iteration of software. 
                    </span>
                  </div>
                  <img
                    style="margin: 1.5em 0 0 0; width: 12px; height: 12px"
                    src="assets/icons/next.svg"
                    alt=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div style="display: flex; flex-direction: column; gap: 2em">
          <div
            style="
              display: grid;
              border: 1.2px solid var(--link-color-active-bg);
              border-radius: 10px;
            "
          >
            <div style="padding: 14px 22px 0px 22px">
              <span style="font-size: 20px">Financial setup</span>
            </div>
            <div style="">
              <div
                (click)="redirectTo('/fees')"
                style="border-bottom: 1px solid var(--link-color-hover)"
              >
                <div
                  style="
                    cursor: pointer;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    padding: 14px 22px 14px 22px;
                    justify-content: space-between;
                  "
                >
                  <div style="width: 81%">
                    <p>Fees</p>
                    <span
                      style="opacity: 0.5; font-weight: 500; font-size: 12px"
                      >Applied in a variety of ways such as costs, charges,
                      commissions.</span
                    >
                  </div>
                  <img
                    style="margin: 1.5em 0 0 0; width: 12px; height: 12px"
                    src="assets/icons/next.svg"
                    alt=""
                  />
                </div>
              </div>
              <div
              (click)="redirectTo('/list-main-data')"
              style="border-bottom: 1px solid var(--link-color-hover)"
            >
              <div
                style="
                  cursor: pointer;
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  padding: 14px 22px 14px 22px;
                  justify-content: space-between;
                "
              >
                <div style="width: 81%">
                  <p>Main Data</p>
                  <span
                    style="opacity: 0.5; font-weight: 500; font-size: 12px">
                    Main numbers that are important for statistic and analyse of our app.</span
                  >
                </div>
                <img
                  style="margin: 1.5em 0 0 0; width: 12px; height: 12px"
                  src="assets/icons/next.svg"
                  alt=""
                />
              </div>
            </div>
              
            
            </div>
          </div>
          <div
            style="
              display: grid;
              border: 1.2px solid var(--link-color-active-bg);
              border-radius: 10px;
            "
          >
            <div style="padding: 14px 22px 0px 22px">
              <span style="font-size: 20px">Authorization setup</span>
            </div>
            <div style="">
              <div 
              (click)="redirectTo('/permissions')"
              style="border-bottom: 1px solid var(--link-color-hover)">
                <div
                  style="
                    cursor: pointer;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    padding: 14px 22px 14px 22px;
                    justify-content: space-between;
                  "
                  href=""
                >
                  <div style="width: 81%">
                    <p>Permissions</p>
                    <span
                      style="opacity: 0.5; font-weight: 500; font-size: 12px">
                      To control access, prevent unauthorized access, and maintain security
                    </span>
                  </div>
                  <img
                    style="margin: 1.5em 0 0 0; width: 12px; height: 12px"
                    src="assets/icons/next.svg"
                    alt=""
                  />
                </div>
              </div>
              <div 
              (click)="redirectTo('/list-roles')"
              style="border-bottom: 1px solid var(--link-color-hover)">
                <div
                  style="
                    cursor: pointer;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    padding: 14px 22px 14px 22px;
                    justify-content: space-between;
                  "
                  href=""
                >
                  <div style="width: 81%">
                    <p>Roles</p>
                    <span
                      style="opacity: 0.5; font-weight: 500; font-size: 12px">
                      Functions that define specific responsibilities and authority within a particular context
                    </span>
                  </div>
                  <img
                    style="margin: 1.5em 0 0 0; width: 12px; height: 12px"
                    src="assets/icons/next.svg"
                    alt=""
                  />
                </div>
              </div>
            </div>
          </div>
        
        </div>
      </div>
    </div>
    <!-- <div
      style="
        display: grid;
        grid-gap: 30px;
        grid-template-columns: repeat(2, minmax(300px, 1fr));
        grid-template-rows: repeat(2, minmax(300px, 1fr));

        grid-auto-rows: 150px;
        grid-auto-flow: row dense;
        justify-content: center;
      "
    >
      <div
        style="
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          box-sizing: border-box;
          background: #0c9a9a;
          color: #fff;
          grid-column-start: auto;
          grid-row-start: auto;
          color: #fff;
          background-size: cover;
          background-position: center;
          box-shadow: -2px 2px 10px 0px rgba(68, 68, 68, 0.4);
          transition: transform 0.3s ease-in-out;
          cursor: pointer;
          counter-increment: item-counter;
        "
      >
        Account setup
      </div>
      <div
        style="
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          box-sizing: border-box;
          background: #0c9a9a;
          color: #fff;
          grid-column-start: auto;
          grid-row-start: auto;
          color: #fff;
          background-size: cover;
          background-position: center;
          box-shadow: -2px 2px 10px 0px rgba(68, 68, 68, 0.4);
          transition: transform 0.3s ease-in-out;
          cursor: pointer;
          counter-increment: item-counter;
          grid-row-end: span 2;
        "
      >
        Client Side
      </div>
      <div
        style="
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          box-sizing: border-box;
          background: #0c9a9a;
          color: #fff;
          grid-column-start: auto;
          grid-row-start: auto;
          color: #fff;
          background-size: cover;
          background-position: center;
          box-shadow: -2px 2px 10px 0px rgba(68, 68, 68, 0.4);
          transition: transform 0.3s ease-in-out;
          cursor: pointer;
          counter-increment: item-counter;
        "
      >
        Services
      </div>
    </div> -->
  </div>
</div>
