import { Injectable } from '@angular/core'
import { Location } from '@angular/common'
import { Router, NavigationEnd } from '@angular/router'
import { filter } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NavigationService {

  private _previousUrl: string;
  private _currentUrl: string;
  private _routeHistory: string[];

  private history: string[] = []

  constructor(private router: Router, private location: Location) { }

  public startSaveHistory():void{

    // this._routeHistory = [];
    // this.router.events
    // .pipe(filter(event => event instanceof NavigationEnd))
    // .subscribe((event: NavigationEnd) => {
    //   this._setURLs(event);
    // });
    this.router.events.subscribe((event) => {
        if (event instanceof NavigationEnd) {
            this.history.push(event.urlAfterRedirects)
        }
      })
  }

  public getHistory(): string[] {
    return this.history;
  }

  private _setURLs(event: NavigationEnd): void {
    const tempUrl = this._currentUrl;
    this._previousUrl = tempUrl;
    this._currentUrl = event.urlAfterRedirects;
    this._routeHistory.push(event.urlAfterRedirects);
  }

  public goBack(): void {
    this.history.pop();
    this.location.back()

    // if (this.history.length > 0) {
    //   this.location.back()
    // } else {
    //   this.router.navigateByUrl("/")
    // }
  }

  public goForward(): void {
       this.location.forward()
  }

  public getPreviousUrl(): string {
    if (this.history.length > 0) {
        return this.history[this.history.length - 2];
    }

    return '';
  }


  get previousUrl(): string {
    return this._previousUrl;
  }

  get currentUrl(): string {
    return this._currentUrl;
  }

  get routeHistory(): string[] {
    return this._routeHistory;
  }

}
