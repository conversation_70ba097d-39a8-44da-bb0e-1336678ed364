                <!-- <div class="container-fluid" *ngIf="!loading">
                    <div class="page-header">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="page-header-left">
                                    <h3> My profile  
                                        <small> Fiber Admin Panel </small>
                                    </h3>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <ol class="breadcrumb pull-right">
                                    <li class="breadcrumb-item">
                                        <a >
                                            <i data-feather="home"></i>
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item"> Edit profile  </li>
                                    <li class="breadcrumb-item active"> My profile  </li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="container-fluid" *ngIf="!loading">
                    <div class="row">
                        <div class="col-xl-4">
                            <div class="card">
                                <div class="card-body">
                                    <div class="profile-details text-center">
                                        <img *ngIf="!user?.photoProfile?.data" src="assets/user2.jpg" alt=""
                                            class="img-fluid img-90 rounded-circle blur-up lazyloaded">
                                            <app-img
                                            *ngIf="user?.photoProfile?.data"
                                                [(buffer)]="user.photoProfile.data"
                                                [class]="'img-fluid img-90 rounded-circle blur-up lazyloaded'"
                                            ></app-img>
                                        <h5 class="f-w-600 mb-0">
                                            {{user?.name}} {{user?.surname}}
                                        </h5>
                                        <span>
                                            {{user?.email}}
                                        </span>
                                        <div class="social">
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="project-status">
                                        <h5 class="f-w-600">
                                             Employee 
                                        </h5>
                                        <div class="media">
                                            <div class="media-body">
                                                <h6> Role  <span class="pull-right">
                                                    {{user?.role}}
                                                </span></h6>
                                            </div>
                                        </div>
                                        <div class="media">
                                            <div class="media-body">
                                                <h6> Token   <span class="pull-right">
                                                    {{user?.token | slice :0:20}}...
                                                </span></h6> 
                                            </div>
                                        </div>
                                        <div class="media">
                                            <div class="media-body">
                                                <h6>  Token Expiration Date  <span class="pull-right">
                                                    {{user?.tokenExpirationDate.toUTCString()}}
                                                </span></h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-8">
                            <div class="card tab2-card">
                                <div class="card-body">
                                    <ul class="nav nav-tabs nav-material" id="top-tab" role="tablist">
                                        <li class="nav-item"><a class="nav-link active" id="top-profile-tab"
                                                data-bs-toggle="tab" href="#top-profile" role="tab"
                                                aria-controls="top-profile" aria-selected="true"><i data-feather="user"
                                                    class="me-2"></i> Profile </a>
                                        </li>
                                        <li class="nav-item"><a class="nav-link" id="contact-top-tab"
                                                data-bs-toggle="tab" href="#top-contact" role="tab"
                                                aria-controls="top-contact" aria-selected="false"><i
                                                    data-feather="settings" class="me-2"></i> Edit profile </a>
                                        </li>
                                    </ul>
                                    <div class="tab-content" id="top-tabContent">
                                        <div class="tab-pane fade show active" id="top-profile" role="tabpanel"
                                            aria-labelledby="top-profile-tab">
                                            <div class="table-responsive profile-table">
                                                <table class="table table-borderless">
                                                    <tbody>
                                                        <tr>
                                                            <td>  First Name :</td>
                                                            <td>{{user?.name}}</td>
                                                        </tr>
                                                        <tr>
                                                            <td>  Last Name :</td>
                                                            <td>{{user?.surname}}</td>
                                                        </tr>
                                                        <tr>
                                                            <td>  Email :</td>
                                                            <td>{{user?.email}}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="top-contact" role="tabpanel" aria-labelledby="contact-top-tab">
                                            <div class="account-setting">
                                                <div class="row">

                                                      <form [formGroup]="primaryInfoForm" >
                                                        <h5><small><strong> Account Details  </strong></small></h5>
                                                            <br>
                                                            <br>
                                                          <div class="form-group row">
                                                              <label for="validationCustom0" class="col-xl-3 col-md-4"><span>*</span>
                                                                 First Name </label>
                                                              <input class="form-control col-xl-8 col-md-7" type="text" formControlName="name"  [value]="user.name" >
                                                          </div>
                                                          <div class="form-group row">
                                                              <label for="validationCustom1" class="col-xl-3 col-md-4"><span>*</span>
                                                                 Last Name </label>
                                                              <input class="form-control col-xl-8 col-md-7" type="text" formControlName="surname" [value]="user.surname">
                                                          </div>
                                                          <div class="pull-right">
                                                            <button type="button" class="btn btn-primary" (click)="updatePrimaryInfo()"> Save </button>
                                                          </div>
                                                    </form>

                                                </div>
                                            </div>
    
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
