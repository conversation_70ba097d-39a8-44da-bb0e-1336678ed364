import { NgxSliderModule } from 'ngx-slider-v2';
import { MatStepperModule } from '@angular/material/stepper';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MainRouteRoutingModule } from './main-route-routing.module';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AutocompleteLibModule } from 'angular-ng-autocomplete';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { AngularMyDatePickerModule } from 'angular-mydatepicker';

import { HomeComponent } from './pages/home/<USER>';
import { PageMainHeaderComponent } from './components/page-main-header/page-main-header.component';
import { PageSidebarComponent } from './components/page-sidebar/page-sidebar.component';
import { RightSidebarComponent } from './components/right-sidebar/right-sidebar.component';
import { MainRouteComponent } from './main-route.component';
import { SharedModule } from '../shared/shared.module';
import { SearchItemComponent } from './components/search-item/search-item.component';
import { OnlineStatusModule } from 'ngx-online-status';
import { ProfileComponent } from './pages/profile/profile.component';
import { ImageCropperModule } from 'ngx-image-cropper';
import { ImgComponent } from './components/app-img/app-img.component';
import { NgxLoadingModule } from 'ngx-loading';
import { ListOfPromotionsComponent } from './pages/promotions/list-of-promotions/list-of-promotions.component';
import { CreatePromotionComponent } from './pages/promotions/create-promotion/create-promotion.component';
import { EditPromotionComponent } from './pages/promotions/edit-promotion/edit-promotion.component';
import { NewAppVersionComponent } from './pages/app-version/new-app-version/new-app-version.component';
import { ListOfAppVersionsComponent } from './pages/app-version/list-of-app-versions/list-of-app-versions.component';
import { PormotionDetailsComponent } from './pages/promotions/pormotion-details/pormotion-details.component';
import { OwlDateTimeModule, OwlNativeDateTimeModule } from 'ng-pick-datetime';
import { TargetedUsersListComponent } from './pages/promotions/targeted-users-list/targeted-users-list.component';
import { NewMainDataComponent } from './pages/main-data/new-main-data/new-main-data.component';
import { ListOfMainDataComponent } from './pages/main-data/list-of-main-data/list-of-main-data.component';
import { ListOfTransactionsComponent } from './pages/transaction/list-of-transactions/list-of-transactions.component';
import { ListOfCentralBankTransactionsComponent } from './pages/central-bank/list-of-central-bank-transactions/list-of-central-bank-transactions.component';
import { AddNewBankTransactionComponent } from './pages/central-bank/add-new-bank-transaction/add-new-bank-transaction.component';
import { ListOfExchangeComponent } from './pages/exchange/list-of-exchange/list-of-exchange.component';
import { ListOfOnlinePaymentComponent } from './pages/online-payment/list-of-online-payment/list-of-online-payment.component';
import { ListOfTranscationsOfMinistryOfFinanceComponent } from './pages/ministry-of-finance/list-of-transcations-of-ministry-of-finance/list-of-transcations-of-ministry-of-finance.component';
import { ListOfTranscationsOfAtmComponent } from './pages/atm/list-of-transcations-of-atm/list-of-transcations-of-atm.component';
import { SettingsComponent } from './pages/settings/settings.component';
import { FeesComponent } from './pages/fees/fees.component';
import { AvatarPhotoComponent } from './components/avatar-photo/avatar-photo.component';
import { NgbdDatepickerRangePopup } from './components/date-from-to/datepicker-range-popup';
import { BloggersComponent } from './pages/bloggers/bloggers.component';
import { SearchListShownComponent } from './pages/search-list-shown/search-list-shown.component';
import { ChatComponent } from './pages/chat/chat.component';
import { UsersComponent } from './pages/users/users.component';
import { SearchContentComponent } from './components/search-content/search-content.component';
import { SelectPostImgComponent } from './components/select-post-img/select-post-img.component';
import { SendNotificationComponent } from './pages/notifications/send-notification/send-notification.component';
import { SearchSideComponent } from './pages/chat/search-side/search-side.component';
import { UserChatComponent } from './pages/chat/user-chat/user-chat.component';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { SendPromotionComponent } from './pages/send-promotion/send-promotion.component';
import { PromotionsComponent } from './pages/promotions/promotions.component';
import { ListPromotionsComponent } from './pages/list-promotions/list-promotions.component';
import { ListNotificationComponent } from './pages/notifications/list-notification/list-notification.component';
import { ListGiftsComponent } from './pages/gifts/list-gifts/list-gifts.component';
import { CreateGiftComponent } from './pages/gifts/create-gift/create-gift.component';
import { ListModalComponent } from './pages/send-promotion/list-modal/list-modal.component';
import { ListOfBannedSuggestedUsersComponent } from './pages/bannedSuggestedUsers/list-of-banned-suggested-users/list-of-banned-suggested-users.component';
import { PermissionsComponent } from './pages/permissions/permissions.component';
import { ListRolesComponent } from './pages/list-roles/list-roles.component';
import { ListVersionComponent } from './pages/versions/list-version/list-version.component';
import { AddVersionComponent } from './pages/versions/add-version/add-version.component';
import { SuggestedComponent } from './pages/suggested/suggested.component';

@NgModule({
  declarations: [
    MainRouteComponent,
    HomeComponent,
    PageMainHeaderComponent,
    PageSidebarComponent,
    RightSidebarComponent,
    SearchItemComponent,
    ProfileComponent,
    ImgComponent,
    ListOfPromotionsComponent,
    CreatePromotionComponent,
    EditPromotionComponent,
    NewAppVersionComponent,
    ListOfAppVersionsComponent,
    PormotionDetailsComponent,
    TargetedUsersListComponent,
    NewMainDataComponent,
    ListOfMainDataComponent,
    ListOfTransactionsComponent,
    ListOfCentralBankTransactionsComponent,
    AddNewBankTransactionComponent,
    ListOfExchangeComponent,
    ListOfOnlinePaymentComponent,
    ListOfTranscationsOfMinistryOfFinanceComponent,
    ListOfTranscationsOfAtmComponent,
    SettingsComponent,
    FeesComponent,
    AvatarPhotoComponent,
    BloggersComponent,
    SearchListShownComponent,
    ChatComponent,
    UsersComponent,
    SearchContentComponent,
    SelectPostImgComponent,
    SendNotificationComponent,
    SearchSideComponent,
    UserChatComponent,
    SendPromotionComponent,
    PromotionsComponent,
    ListPromotionsComponent,
    ListNotificationComponent,
    ListGiftsComponent,
    CreateGiftComponent,
    ListModalComponent,
    ListOfBannedSuggestedUsersComponent,
    PermissionsComponent,
    ListRolesComponent,
    ListVersionComponent,
    AddVersionComponent,
    SuggestedComponent,
  ],
  imports: [
    InfiniteScrollModule,
    CommonModule,
    OnlineStatusModule,
    OwlDateTimeModule,
    OwlNativeDateTimeModule,
    SharedModule,
    MainRouteRoutingModule,
    NgxLoadingModule.forRoot({}),
    NgMultiSelectDropDownModule.forRoot(),
    AutocompleteLibModule,
    ReactiveFormsModule,
    FormsModule,
    AutocompleteLibModule,
    ImageCropperModule,
    AngularMyDatePickerModule,
    NgbdDatepickerRangePopup,
    MatStepperModule,
    NgxSliderModule,
    MatCheckboxModule,
  ],
})
export class MainRouteModule {}
