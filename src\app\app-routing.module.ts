import { NgModule } from '@angular/core';
import { LoadingComponent } from './main-route/pages/loading/loading.component';
import { AuthComponent } from './main-route/pages/login/auth/auth.component';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'en/home',
    pathMatch: 'full',
  },
  {
    path: ':lang/auth',
    component: AuthComponent,
  },
  {
    path: ':lang/loading',
    component: LoadingComponent,
  },
  {
    path: '',
    loadChildren: () =>
      import('./main-route/main-route.module').then((m) => m.MainRouteModule),
  },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
