<ngx-loading
[(show)]="loading"
[config]="{
animationType: ngxLoadingAnimationTypes.circleSwish,
primaryColour: '#ffffff',
backdropBorderRadius: '3px'
}"
></ngx-loading>

<div class="card-body">
  <div class="bg-inner cart-section order-details-table">
    <div class="row g-4">
      <div class="card-details-title ">
        <div style="display: flex; align-items: center; gap: 2em">
          <img
            *ngIf="!promotion?.user?.photoProfile?.data"
           style="height: 65px;width: 65px; border-radius: 20px;" src="assets/no_picture_fiber.svg" alt="">
          <app-img
            *ngIf="promotion?.user?.photoProfile?.data"
            [(buffer)]="promotion.user.photoProfile.data"
            [class]="'profile-radius'"
          ></app-img>
          <td>
            <p
              style="
                margin-bottom: 0;
                font-weight: 700;
                color: rgb(92, 92, 92);
                font-size: 22px;
              ">
              {{promotion?.user?.name}} {{promotion?.user?.surname}}
            </p>
            <p style="margin-bottom: 0">{{promotion?.createdAt | date: 'MMM d, y, h:mm:ss a' }}</p>
          </td>
        </div>
      </div>
      <div
          style="display: grid; grid-template-columns: 1fr 1fr"
          class="table-responsive table-details"
        >
          <div class="col-xl-11 col-sm-11 col-md-11 xl-10">
            <div class="card o-hidden widget-cards">
              <div class="warning-box card-body">
                <div class="media static-top-widget align-items-center">
                  <div class="icons-widgets">
                    <div class="align-self-center text-center shadow">
                      <img
                        style="height: 26px; width: 26px"
                        src="assets/svg/icons/icona_cost.svg"
                        alt=""
                      />
                    </div>
                  </div>
                  <div class="media-body media-doller">
                    <span class="m-0">Cost</span>
                    <h3 class="mb-0">
                      <span class="counter">{{promotion?.cost}}</span><small> Credits</small>
                    </h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-11 xl-10">
            <div class="card o-hidden widget-cards">
              <div class="warning-box card-body">
                <div class="media static-top-widget align-items-center">
                  <div class="icons-widgets">
                    <div class="align-self-center text-center shadow">
                      <img
                        style="height: 26px; width: 26px"
                        src="assets/svg/icons/icona_allowed_to_share.svg"
                        alt=""
                      />
                    </div>
                  </div>
                  <div class="media-body media-doller">
                    <span class="m-0">Allowed To Share</span>
                    <h3 class="mb-0">
                      <span class="counter">{{promotion?.allowedToShare}}</span><small> Users</small>
                    </h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-11 xl-10">
            <div class="card o-hidden widget-cards">
              <div class="warning-box card-body">
                <div class="media static-top-widget align-items-center">
                  <div class="icons-widgets">
                    <div class="align-self-center text-center shadow">
                      <img
                        style="height: 24px; width: 24px"
                        src="assets/svg/icons/icona_share.svg"
                        alt=""
                      />
                    </div>
                  </div>
                  <div class="media-body media-doller">
                    <span class="m-0">Are Shared </span>
                    <h3 class="mb-0">
                      <span class="counter">{{promotion?.sharedCount}}</span><small> Users</small>
                    </h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-11 xl-10">
            <div class="card o-hidden widget-cards">
              <div class="warning-box card-body">
                <div class="media static-top-widget align-items-center">
                  <div class="icons-widgets">
                    <div class="align-self-center text-center shadow">
                      <img
                        style="height: 30px; width: 30px"
                        src="assets/svg/icons/native_notification_miss.svg"
                        alt=""
                      />
                    </div>
                  </div>
                  <div class="media-body media-doller">
                    <span class="m-0">Native Missed Sent Notification </span>
                    <h3 class="mb-0">
                      <span class="counter">{{promotion?.nativeMissedNotificationSendCount}}</span><small> Users</small>
                    </h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
      </div>
      <div class="col-xl-8">
        <div class="card-details-title ">
          <div style="display: flex; align-items: center; gap: 2em">
            <td>
              <p style="margin-bottom: 0">Status</p>
              <p
                
                *ngIf="promotion?.active"
                style="
                  margin-bottom: 0;
                  font-weight: 700;
                  color: rgb(92, 92, 92);
                  font-size: 22px;
                "
              >
                Activate
              </p>
                <p
                *ngIf="!promotion?.active"
                style="
                  margin-bottom: 0;
                  font-weight: 700;
                  color: rgb(250, 76, 45);
                  font-size: 22px;
                "
              >
                Deactive
              </p>
            </td>
            <div 
            (click)="onRemove(promotion, 1, !promotion?.active)"
            style="margin-left: auto; width: 30%; height: 59px">
              <div 
              *ngIf="!promotion?.active"
              class="button shadow">
                <p class="paragraphButtonStyle">Activate</p>
              </div>
              <div
                *ngIf="promotion?.active"
                class="button tomatoBackround">
                  <p class="paragraphButtonStyle">Deativate</p>
              </div>
            </div>
          </div>
        </div>
        <div 
        (click)="goToLink(promotion?.linkUrl.toString())"
        style="display: flex; flex-direction: column; justify-content: center;">
          <img
            style="object-fit: cover !important;
            height: 40%!important;
            width: 40% !important;
            border-radius: 30px !important;
            display: flex !important;
            margin-left: 50% !important;
            transform: translateX(-50%) !important;"
            *ngIf="!promotion?.images[0]?.data"
             src="assets/no_picture_fiber.svg" alt="">

          <app-img
            *ngIf="promotion?.images[0]?.data"
            [(buffer)]="promotion.images[0].data"
            [class]="'promotion-detail-image'"
          ></app-img>
            
          <p
            style="
              cursor: pointer;
              height: 50px;
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 20px;
            "
          >
            {{promotion?.linkUrl}}
          </p>
        </div>
      </div>
      <div class="col-xl-4 col-md-12">
        <div class="row g-4">
          <div class="col-11">
            <div class="order-success">
              <div class="d-flex">
                <h4 style="color: rgb(92, 92, 92)">Targeted Users </h4>
                <h4 style="color: rgb(92, 92, 92)" class="ms-auto">{{promotion?.specificUsersCount}}</h4>
              </div>
              <ul class="order-details">
                <li *ngFor="let promotion of specificUsers">{{promotion?.userAllowed?.name}}  {{promotion?.userAllowed?.surname}}</li>
              </ul>
              <p
              *ngIf="specificUsers.length"
              (click)="redirectTo('/targeted-users-list/'+ promotion?._id)"
                style="
                  position: absolute;
                  right: 0;
                  padding-right: 2em;
                  cursor: pointer;
                  color:rgb(22, 22, 22)
                "
              >
                See More...
              </p>
            </div>
          </div>

          <div class="col-11">
            <div class="order-success">
              <div class="d-flex">
                <h4 style="color: rgb(92, 92, 92)">
                  Socket Notification
                </h4>
                <h4 style="color: rgb(92, 92, 92)" class="ms-auto">            
                  {{promotion?.iPromotionNotificationSendCount}}
                </h4>
              </div>
              <ul class="order-details">
                <li *ngFor="let promotion of promotionAvailableShared">{{promotion?.userAllowed?.name}}  {{promotion?.userAllowed?.surname}} </li>
              </ul>
              <p
              *ngIf="promotionAvailableShared.length"
              (click)="redirectTo('/targeted-users-list/'+ promotion?._id)"
                style="
                  position: absolute;
                  right: 0;
                  padding-right: 2em;
                  cursor: pointer;
                  color:rgb(22, 22, 22)

                "
              >
                See More...
              </p>
            </div>
          </div>

          <div class="col-11">
            <div class="order-success">
              <div class="payment-mode">
                <div class="d-flex">
                  <h4 style="color: rgb(92, 92, 92)">
                    Native Sent Notification
                  </h4>
                  <h4 style="color: rgb(92, 92, 92)" class="ms-auto">{{promotion?.nativeNotificationSendCount}}  </h4>
                </div>
                <ul class="order-details">
                  <li *ngFor="let promotion of nativeNotificationUsers" >{{promotion?.userAllowed?.name}}  {{promotion?.userAllowed?.surname}} </li>
                </ul>
                <p
                  *ngIf="nativeNotificationUsers.length"
                  (click)="redirectTo('/targeted-users-list/'+ promotion?._id)"
                  style="
                    position: absolute;
                    right: 0;
                    padding-right: 2em;
                    cursor: pointer;
                  color:rgb(22, 22, 22)

                  "
                >
                  See More...
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card-details-title mt-4">
      <div style="display: flex; align-items: center; gap: 2em">
        <td>
          <p style="margin-bottom: 0">Starts at</p>
          <p
            style="
              margin-bottom: 0;
              font-weight: 600;
              color: rgb(126, 126, 126);
              font-size: 19px;
            "
          >
            {{promotion?.startPromoteDateTime | date: 	'M/d/yy, h:mm a' }} 🕒
          </p>
        </td>
        <!-- <td>
          <p style="margin-bottom: 0">Ends at</p>
          <p
            style="
              margin-bottom: 0;
              font-weight: 600;
              color: rgb(126, 126, 126);
              font-size: 19px;
            "
          >
          {{promotion?.startPromoteDateTime | date: 	'M/d/yy, h:mm a' }} 🕒
          </p>
        </td> -->
        <div 
        (click)="onPause(promotion, 1, !promotion?.pause)"
        style="margin-left: auto; width: 30%; height: 59px">
          <div 
          *ngIf="promotion?.pause"
          class="button shadow">
            <p class="paragraphButtonStyle">Play</p>
          </div>
          <div 
          *ngIf="!promotion?.pause"
          class="button tomatoBackround">
            <p class="paragraphButtonStyle">Stop</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
