import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AtmService } from 'src/app/shared/services/atm.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/shared/services/user.service';
import { AuthService } from 'src/app/shared/services/auth.service';
import { User } from 'src/app/shared/models/user';
import { SocketService } from 'src/app/shared/services/socket.service';

@Component({
  selector: 'app-list-of-transcations-of-atm',
  templateUrl: './list-of-transcations-of-atm.component.html',
  styleUrls: ['./list-of-transcations-of-atm.component.css'],
})
export class ListOfTranscationsOfAtmComponent implements OnInit {
  closeResult: string;
  lang = 'en';
  searchForm;

  loading = false;

  data: any[] = [];

  startLoading = false;
  public pageNo: number = 1;
  public pageNoTotal: number = 1;
  count: number = 0;
  resultsPerPage: number = 7;

  public sortables = ['score', 'date', 'reliability'];
  public sortableElement: string = 'score';
  
  userSelected : any = null

  resultSearchPageNumber = 1;
  timeout: any = null;
  resultSearch: any = null;
  searchText = '';
  searchLoading = false

  me: User;
  atmTransactionSelected = null

  statusTypes = [
    'waiting',
    'processing',
    'succes',
    'canceled'
  ]

  currencyTypes = [
    'EUR',
    'GBP',
    'ALL'
  ]
  
  form = new UntypedFormGroup({});
  settingsForm = new UntypedFormGroup({});
  exchangeForm = new UntypedFormGroup({});

  statusSelected = 'All'

  socket

  disableWithdrawButton = false

  constructor(
    private socketService: SocketService,
    private atmService: AtmService,
    private authService: AuthService,
    private userService: UserService,
    private formBuilder: UntypedFormBuilder,
    private toastrService: ToastrService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private modalService: NgbModal
  ) {
    this.socket = this.socketService.setupSocketConnection();
    this.searchForm = this.formBuilder.group({
      search: '',
    });
    this.authService.user.pipe().subscribe((appUser) => {
      this.me = appUser;
    });
  }

  ngOnInit(): void {
    this.loading = true;

    this.activatedRoute.params.subscribe(async (paramMap) => {
      if (paramMap['lang']) {
        this.lang = paramMap['lang'];
      }
    });

    this.activatedRoute.queryParams.subscribe((params) => {
      this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;
    });
    this.prepareExchangeForm(0,0,0)
    this.prepareSettingsForm('EUR',1)
    this.getAll();
  }
  
  setSortableElement($event) {
    this.sortableElement = $event;
  }

  public setPage(page: number) {
    this.data = [];
    this.loading = true;
    this.router
      .navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: page },
        queryParamsHandling: 'merge', // preserve the existing query params in the route
        skipLocationChange: false, // do trigger navigation
      })
      .finally(() => {
        if(this.searchText === ''){
          this.getAll();
        }else{
          this.getAllResultSearch()
        }
        // this.viewScroller.setOffset([120, 120]);
        // this.viewScroller.scrollToAnchor('deals'); // Anchore Link
      });
  }

  getAll() {
    this.atmService.getAll(this.pageNo, this.resultsPerPage,this.statusSelected).subscribe(
      async (result) => {
        if (result.status == 'success') {
          this.count = result.data.count;
          this.data = result.data.data;
          console.log("this.data: ",this.data)
          this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
          this.loading = false;
          this.startLoading = false;
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }

  ngOnDestroy() {}

  redirectTo(uri: string) {
    this.router.navigateByUrl(this.lang + uri).then(() => {
      // this.loadJsService.removeScripts()
    });
  }

  openOptionsModal(optionsContent) {
    this.modalService.open(optionsContent, { centered: true });
  }

  openStatusChangeModal(statusContent) {
    this.modalService.open(statusContent, { centered: true });
  }

  openUserSearchModal(searchContent) {
    this.modalService.open(searchContent, { centered: true });
  }
  searchUser(searchText) {
    clearTimeout(this.timeout);
    var $this = this;
    this.timeout = setTimeout(function () {
      if (searchText.keyCode != 13) {
        $this.startLoading = true
        $this.loading = true
        $this.searchSocket(searchText.target.value);
      }else{
        $this.startLoading = true
        $this.loading = true
        $this.getAll()
      }
    }, 1000);
  } 
  public searchSocket(searchText) {
    this.searchText = searchText.toString();
    if(this.searchText == ''){
      this.startLoading = true
      this.loading = true
      this.setPage(1)
      return
    }
    this.data = []
    this.setPage(1)
  }

  getAllResultSearch() {
    if(this.searchText == '') return
    this.loading = true;
    this.startLoading = true;
    this.count =  1
    this.pageNoTotal = 1
    this.data = []
    this.atmService.searchATM(this.searchText, this.me._id,this.pageNo,this.resultsPerPage,this.statusSelected).subscribe(
      (result) => {
        if (result.status == 'success') {
          this.count = result.data.count;
          this.data = result.data.data;
          this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
          this.loading = false;
          this.startLoading = false;
        }
      },
      (respond_error) => {
        this.loading = false;
        this.startLoading = false;
        // this.toastrService.error(
        //   respond_error?.error.message,
        //   respond_error?.name
        // );
      }
    );
  }

  async selectUser(user){
    this.modalService.dismissAll()
    // console.log("selectUser: ",user)
    this.userSelected = user
  }

  async prepareExchangeForm(creditsAmount,realMoneyAmount,feeCreditsAmount){ 
    this.exchangeForm = this.formBuilder.group({
      creditsAmount: new UntypedFormControl(creditsAmount),
      realMoneyAmount: new UntypedFormControl(realMoneyAmount),
      feeCreditsAmount: new UntypedFormControl(feeCreditsAmount),
    });
  }

  async prepareSettingsForm(currency,exchangeRate){ 
    this.settingsForm = this.formBuilder.group({
      feePercentage: new UntypedFormControl(0),
      currency: new UntypedFormControl(currency.toString()),
      exchangeRate: new UntypedFormControl(exchangeRate),
    });
  }

  async prepareForm(detailDescription,status){ 
    this.form = this.formBuilder.group({
      detailDescription: new UntypedFormControl(detailDescription),
      status: new UntypedFormControl(status.toString()),
    });
  }
  async selectAtmTransaction(data){
    this.atmTransactionSelected = data
    this.prepareForm(
      this.atmTransactionSelected?.detailDescription,
      this.atmTransactionSelected?.status)
  }


  typeCreditsAmount(creditsAmount) {
    clearTimeout(this.timeout);
    var $this = this;
    this.timeout = setTimeout(function () {
      if (creditsAmount.keyCode != 13) {
        $this.calculaterRealMoneyAmount(creditsAmount.target.value);
      }
    }, 1000);
  }

  calculaterRealMoneyAmount(creditsAmount){
    this.prepareExchangeForm(
      creditsAmount,
      this.calculateLeftPrice(creditsAmount, this.settingsForm?.value?.feePercentage),
      this.calculateFee(creditsAmount, this.settingsForm?.value?.feePercentage ))
  }

  calculateFee(num,amount){
    return  num - this.calculateLeftPrice(num,amount)
  }

  calculateLeftPrice(num,amount){
    return num - this.per(num, amount)
  }

  per(num, amount){
    return num*amount/100;
  }

  updateStatus(){
    if(this.atmTransactionSelected?.status === 'waitingConfirm'){
      return
    }
    let atmTransactionId = this.atmTransactionSelected?._id
    let data = {
      updatedAt: new Date(),
      userWhoUpdated: this.me._id,
      status: this.form.value.status ,
      detailDescription: this.form.value.detailDescription
    }
    if(this.atmTransactionSelected == null){
      this.toastrService.error('ATM Transaction Not Selected  ');
      return
    }
    console.log("this.atmTransactionSelected?.status:",this.atmTransactionSelected?.status)
    
    this.atmService.updateStatus( 
      atmTransactionId.toString(), 
      data
    ).subscribe( respond  => {
            if( respond.status = "success"){

              let nativeTitle = ''

              if(this.form.value.status == 'processing'){
                nativeTitle = 'Your credit to euro conversion is being processed.'
              }
              if(this.form.value.status == 'canceled'){
                nativeTitle = "We're sorry, but your credit to euro conversion has been canceled."
              }
              if(this.form.value.status == 'waiting'){
                nativeTitle = "Credits to euro conversion is underway. Please wait while we process your request."
              }
              if(this.form.value.status == 'succes'){
                nativeTitle = 'Success! Your credit conversion to euros is complete.'
              }
              
              
              this.createNotification(
                this.me?._id,
                '630a2e5d4989aae851a657e4',
                this.atmTransactionSelected?.user?._id,
                nativeTitle,
                false,
                false,
                false,
                false,
                false,
                false,
                null,
                '/convert'
              );
              // '/convert?tabSelected?tabSelected=historyTab&atmTransactionsId='+this.atmTransactionSelected?._id
              const index = this.data.findIndex(item => item._id.toString() === atmTransactionId.toString());
              this.data[index].detailDescription = this.form.value.detailDescription
              this.data[index].status = this.form.value.status
              this.atmTransactionSelected = null
              this.modalService.dismissAll()
              this.toastrService.success( " Successfully updated status" );
            }
        },
        respond_error => {
          let error_message = respond_error.error.message;
          this.toastrService.error(error_message);
        }

      ); 
  }

  resultsPerPageChanged(event){
    this.loading = true;
    this.startLoading = true;
    this.count =  1
    this.pageNoTotal = 1
    this.data = []
    this.resultsPerPage = Number(event)
    this.setPage(1)
  }

  changeStatus(event){
    // searchStatus
    this.loading = true;
    this.startLoading = true;
    this.count =  1
    this.pageNoTotal = 1
    this.data = []
    this.setPage(1)
  }

  createNotification(
    userId,
    senderId,
    receiverId,
    text,
    isGiftTransaction,
    isPayPerViewTransaction,
    isPromotionSharingTransaction,
    isShareLink,
    isVideoContent,
    isPromotion,
    post,
    directURL
  ){
    this.socket.emit('createNotification', {
      userId: userId.toString(),
      from : senderId?.toString(),
      to : receiverId?.toString(),
      text : text,
      isGiftTransaction: isGiftTransaction,
      isPayPerViewTransaction: isPayPerViewTransaction,
      isPromotionSharingTransaction: isPromotionSharingTransaction,
      isShareLink: isShareLink,
      isVideoContent: isVideoContent,
      isPromotion: isPromotion,
      post: post?.toString(),
      directURL: directURL
    });
  }

  async exchange(){
      if (this.disableWithdrawButton) {
        return;
      }
      this.disableWithdrawButton = true;

      if(this.userSelected == null){
        this.toastrService.error('User is not selected for exchange');
        this.disableWithdrawButton = false;
        return
      }
      
      if(this.exchangeForm.value.creditsAmount > this.userSelected.coins){
        this.toastrService.error(this.userSelected?.name + ' ' + this.userSelected?.surname + 
        '  dont have enough credits for exchange');
        this.disableWithdrawButton = false;
        return
      }

      if(this.exchangeForm.value.creditsAmount == 0){
        this.toastrService.error('0 credit not allowed to exchange');
        this.disableWithdrawButton = false;
        return
      }

      if(this.userSelected == null){
        this.toastrService.error('User is not selected for exchange');
        this.disableWithdrawButton = false;
        return
      }

      this.userService.getProfileBalance(this.userSelected._id).subscribe(
        async (respond) => {
          if ((respond.status = 'success')) {
            let myBalance = respond.data.myBalance
              if (respond.data.myBalance < this.exchangeForm.value.creditsAmount) {
                this.toastrService.error('Not Enough To Convert');
              }else{
                this.withdrawCredits()
              }
            }
        },
        (respond_error) => {
          this.disableWithdrawButton = false;
          let error_message = respond_error.error.message;
          this.toastrService.error(error_message);
        }
      );

  }

  withdrawCredits(){
        let withdrawRealMoneyCurrency = this.settingsForm?.value?.currency

        this.atmService.create(
        withdrawRealMoneyCurrency,
        this.userSelected?.email,
        Number(this.exchangeForm.value.creditsAmount),
        this.calculateLeftPrice(this.exchangeForm.value.creditsAmount, this.settingsForm?.value?.feePercentage ),
        this.calculateFee(this.exchangeForm.value.creditsAmount, this.settingsForm?.value?.feePercentage ),
        this.userSelected?._id
        ).subscribe(
        (result) => {
          console.log("atmTransaction: ",result)
          if (result.status == 'success') {
            let dat = result?.atmTransaction

            this.createNotification(
              this.me?._id,
              '630a2e5d4989aae851a657e4',
              result?.atmTransaction?.user,
              'We have received your request to convert credits to '+withdrawRealMoneyCurrency+' . To proceed, confirm or cancel',
              false,
              false,
              false,
              false,
              false,
              false,
              null,
              '/confirm-convert/'+ result?.atmTransaction?._id
            );

            dat['user']= this.userSelected
            this.data = [result?.atmTransaction,...this.data]
          }
          this.exchangeForm.value.creditsAmount = 0
          this.exchangeForm.value.realMoneyAmount = 0
          this.userSelected = null
          this.disableWithdrawButton = false;
        },
        (respond_error) => {
          this.disableWithdrawButton = false;
          this.toastrService.error(
            respond_error?.error.message,
            respond_error?.name
          );
        }
        );
  }

}
