const express = require('express');
const searchListShownController = require('./../controllers/searchListShownController');

const router = express.Router();

router
.route('/')
.post(
    searchListShownController.create
);



router
.route('/getAll')
.post(
    searchListShownController.getAll
);


router
.route('/findByIdAndDelete')
.post(
    searchListShownController.findByIdAndDelete
);

router
  .route('/search')
  .post(searchListShownController.search);


module.exports = router;