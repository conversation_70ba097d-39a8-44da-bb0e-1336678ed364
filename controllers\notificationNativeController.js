const catchAsync = require('./../utils/catchAsync');
const NotificationNativeModel = require('./../models/notificationNativeModel');

exports.create = catchAsync(async (req, res, next) => {

    console.log("req.body.data: ",req.body.data)
      if(req.body.data !== null){
        await NotificationNativeModel.create(req.body.data);
      }
    res.status(201).json({
        status: 'success',
        data: {}
    });

});

exports.getAll = catchAsync(async (req, res, next) => {

    let resultsPerPage =  req.body.resultsPerPage  
    let page = req.body.page 
    page = page - 1
    
    let data =
     await NotificationNativeModel.find()
    .limit( resultsPerPage)
    .skip( resultsPerPage * page)
    .sort({ _id: -1 })

    
    let count = await NotificationNativeModel.countDocuments()

    res.status(200).json({
      status: 'success',
      data: {
        data: data,
        count: count
      }
    });
  
});