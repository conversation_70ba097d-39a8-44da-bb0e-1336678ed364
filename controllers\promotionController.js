const catchAsync = require("./../utils/catchAsync");
const Promotion = require("./../models/promotionModel");
const PromotionAvailable = require("./../models/promotionAvailableModel");

const Post = require("./../models/postModel");
const User = require("../models/userModel");

exports.resizePhoto = catchAsync(async (req, res, next) => {
  // console.log("req.files.file: ",req.files.file)
  // if (!req.files.file) return next();
  // req.body.images = []
  //  for(let file of  req.files.file){
  //         sharp(file.path)
  //         .rotate()
  //         .resize(        {
  //             width: 1080,
  //             height: 1080
  //           })
  //         .flatten(true)
  //         .flatten({ background: {  r: 255, g: 255, b: 255}})
  //         .jpeg({ mozjpeg: true })
  //         .toBuffer()
  //         .then( async data => {
  //             let buffer = await data
  //             req.body.images = [...req.body.images,await buffer]
  //             if(req.body.images.length === req.files.file.length ){
  //                 next();
  //             }
  //          }).catch( err => {});
  //   }
});

exports.create = catchAsync(async (req, res, next) => {
  let data = {
    linkUrl: req.body.linkUrl,
    cost: req.body.cost,
    allowedToShare: req.body.allowedToShare,
    followersCountActive: req.body.followersCountActive,
    followingCountActive: req.body.followingCountActive,
    postCountActive: req.body.postCountActive,
    earnCountActive: req.body.earnCountActive,
    timeAllowedToShareMinutes: req.body.timeAllowedToShareMinutes,
    minutesDiffernce: req.body.minutesDiffernce,
    startPromoteDateTime: req.body.startPromoteDateTime,
    specificUsersCount: req.body.specificUsersCount,
    user: req.body.user,
    images: req.body.images,
  };
  console.log("images: ", req.body.images);

  if (req.body.followersCountMax) {
    data.followersCountMax = req.body.followersCountMax;
  }
  if (req.body.followersCountMin) {
    data.followersCountMin = req.body.followersCountMin;
  }

  if (req.body.followingCountMin) {
    data.followingCountMin = req.body.followingCountMin;
  }
  if (req.body.followingCountMax) {
    data.followingCountMax = req.body.followingCountMax;
  }

  if (req.body.postCountMin) {
    data.postCountMin = req.body.postCountMin;
  }
  if (req.body.postCountMax) {
    data.postCountMax = req.body.postCountMax;
  }

  if (req.body.earnCountMin) {
    data.earnCountMin = req.body.earnCountMin;
  }
  if (req.body.earnCountMax) {
    data.earnCountMax = req.body.earnCountMax;
  }

  // if(req.files.file){
  //     for (let i = 0; i < req?.files.file.length; i++) {
  //         if(req.body.images[i] && i === 0){
  //             let item = await {
  //                 size : await req.files.file[0].size,
  //                 name: await req.files.file[0].name,
  //                 data: await req.body.images[0],
  //                 contentType: await req.files.file[0].type
  //             }
  //             data.images = [...data.images,item]
  //         }
  //     }
  // }

  const doc = await Promotion.create(data);

  res.status(201).json({
    status: "success",
    data: {
      data: doc,
    },
  });
});

exports.getAll = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 5 ? req.body.resultsPerPage : 5;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;

  let promotions = await Promotion.find({ active: true }, "-images")
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ _id: -1 });

  let promotionCount = await Promotion.countDocuments({ active: true });

  for (let promotion of promotions) {
    // let count = await Post.countDocuments({promotion:promotion}).exec(); //if you are using promise
    promotion._doc["isSharedCount"] = 0;
  }
  res.status(200).json({
    status: "success",
    data: {
      data: promotions,
      promotionCount: promotionCount,
    },
  });
});

exports.getAllTargetedUsers = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 10 ? req.body.resultsPerPage : 10;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;
  let promotionsAvailableCount = await PromotionAvailable.countDocuments({
    promotion: req.params.id,
    isActive: true,
  });
  // ,{bio:0,promotion:0}
  let promotionsAvailable = await PromotionAvailable.find(
    { promotion: req.params.id, isActive: true },
    { promotion: 0 }
  )
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ _id: -1 });

  res.status(200).json({
    status: "success",
    data: {
      promotionsAvailable: promotionsAvailable,
      promotionsAvailableCount: promotionsAvailableCount,
    },
  });
});

exports.getPromotionData = catchAsync(async (req, res, next) => {
  // const posts = await Post.find({promotion:req.params.id});
  let promotionAvailableShared = await PromotionAvailable.find({
    promotion: req.params.id,
    isActive: true,
    iPromotionNotificationSendStatus: true,
  })
    .limit(3)
    .skip(0)
    .sort({ createdAt: -1 });

  let specificUsers = await PromotionAvailable.find({
    promotion: req.params.id,
    isActive: true,
  })
    .limit(3)
    .skip(0)
    .sort({ createdAt: -1 });

  let nativeNotificationUsers = await PromotionAvailable.find({
    promotion: req.params.id,
    isActive: true,
    nativeNotificationSendStatus: true,
  })
    .limit(3)
    .skip(0)
    .sort({ createdAt: -1 });

  let promotion = await Promotion.find({ _id: req.params.id });
  console.log("promotion: ", req.params.id);

  const data = {
    isSharedCount: 0,
    nativeNotificationUsers: nativeNotificationUsers,
    specificUsers: specificUsers,
    promotionAvailableShared: promotionAvailableShared,
    promotion: promotion[0],
  };
  res.status(200).json({
    status: "success",
    data: {
      data,
    },
  });
});

exports.changeActiveStatus = catchAsync(async (req, res, next) => {
  // console.log("req.body: ",req.body)
  let user = null;
  user = await Promotion.findByIdAndUpdate(
    req.body._id,
    { active: req.body.active },
    { new: true }
  );
  if (user !== null) {
    res.status(200).json({
      status: "success",
      data: user,
    });
  }
});

exports.changePauseStatus = catchAsync(async (req, res, next) => {
  // console.log("req.body: ",req.body)
  let user = null;
  user = await Promotion.findByIdAndUpdate(
    req.body._id,
    { pause: req.body.pause },
    { new: true }
  );
  if (user !== null) {
    res.status(200).json({
      status: "success",
      data: user,
    });
  }
});

exports.getFilteredUsers = catchAsync(async (req, res, next) => {
  // const posts = await Post.find({promotion:req.params.id});
  // if(){

  // }
  // const promotion = await User.find(
  //   {
  //     followers : { $gte :  50},
  //     maxNum : { $lte :  100}
  //     }
  //   {_id:req.params.id}
  //   );

  const data = {
    isSharedCount: 0,
    posts: [],
    promotion: promotion[0],
  };
  res.status(200).json({
    status: "success",
    data: {
      data,
    },
  });
});
