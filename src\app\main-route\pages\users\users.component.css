.users-section {
  display: grid;
  grid-template-columns: 1fr;
  padding: 32px !important;
  max-height: 1000px;
}

@media screen and (max-width: 893px) {
  .users-section {
    grid-template-columns: 1fr !important;

    grid-template-rows: 1fr 1fr !important;
    overflow: scroll;
  }
  .searchModalAtm {
    transform: translateY(-42px);
  }
}
.dark-modal .modal-content {
  background-color: #292b2c !important;
  color: white;
}
.dark-modal .close {
  color: white;
}
.light-blue-backdrop {
  background-color: #5cb3fd;
}
.optionStyle {
  font-size: 16px;
  text-align: center;
  max-width: 100px;
  background-color: var(--app-bg);
  line-height: 2;
  border: none;
  color: white;
  font-weight: 600;
  height: 40px;
}
.userInfoWrapper {
  background-color: var(--app-bg);
  padding: 0 4px;
  border-radius: 10px;
}

/* Second Screen */
.view-more-section {
  display: grid;
  grid-template-columns: 210px 2fr;
  padding: 32px !important;
  max-height: 1000px;
}
.innerTabItem {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 50px;
  border: 1px solid var(--app-bg);
  padding: 0 5px;
  border-radius: 10px;
}
.innerTabItem:hover {
  background: var(--app-bg);
  scale: 1.1;
}
/* Gallery Section */

.gallery {
  display: flex;
  flex-wrap: wrap;
  margin: -1rem -1rem;
  padding-bottom: 3rem;
}

.gallery-item {
  position: relative;
  flex: 1 0 11rem;
  margin: 1rem;
  color: #fff;
  cursor: pointer;
  border-radius: 23px;
  overflow: hidden;
}

.gallery-item:hover .gallery-item-info,
.gallery-item:focus .gallery-item-info {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  gap: 1em;
}

.gallery-item-info {
  display: none;
}

.gallery-item-info li {
  display: flex;
  font-size: 1.3rem;
  font-weight: 600;
}

.gallery-item-type {
  position: absolute;
  top: 0rem;
  right: 1rem;
  font-size: 1.5rem;
  text-shadow: 0.2rem 0.2rem 0.2rem rgb(0 0 0 / 10%);
}
.gallery-item-type > img {
  height: 20px;
  width: 20px;
}

.fa-clone,
.fa-comment {
  transform: rotateY(180deg);
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Loader */

.loader {
  width: 5rem;
  height: 5rem;
  border: 0.6rem solid #999;
  border-bottom-color: transparent;
  border-radius: 50%;
  margin: 0 auto;
  animation: loader 500ms linear infinite;
}

/* Media Query */

@media screen and (max-width: 40rem) {
  .profile {
    display: flex;
    flex-wrap: wrap;
    padding: 4rem 0;
  }

  .profile::after {
    display: none;
  }

  .profile-image,
  .profile-user-settings,
  .profile-bio,
  .profile-stats {
    float: none;
    width: auto;
  }

  .profile-image img {
    width: 7.7rem;
  }

  .profile-user-settings {
    flex-basis: calc(100% - 10.7rem);
    display: flex;
    flex-wrap: wrap;
    margin-top: 1rem;
  }

  .profile-user-name {
    font-size: 2.2rem;
  }

  .profile-edit-btn {
    order: 1;
    padding: 0;
    text-align: center;
    margin-top: 1rem;
  }

  .profile-edit-btn {
    margin-left: 0;
  }

  .profile-bio {
    font-size: 1.4rem;
    margin-top: 1.5rem;
  }

  .profile-edit-btn,
  .profile-bio,
  .profile-stats {
    flex-basis: 100%;
  }

  .profile-stats {
    order: 1;
    margin-top: 1.5rem;
  }

  .profile-stats ul {
    display: flex;
    text-align: center;
    padding: 1.2rem 0;
    border-top: 0.1rem solid #dadada;
    border-bottom: 0.1rem solid #dadada;
  }

  .profile-stats li {
    font-size: 1.4rem;
    flex: 1;
    margin: 0;
  }

  .profile-stat-count {
    display: block;
  }
}
.listWrapper {
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  gap: 5px;
}
.listWrapper > li {
  align-items: center;
}
.reaction {
  background-color: rgba(128, 128, 128, 0.371);
  border-radius: 30px;
  height: 100%;
  width: 25%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20%;
  max-width: 30%;
  max-height: 35px;
  min-height: 28px;
}
.reaction img {
  height: 10px;
  width: 10px;
}
.reaction p {
  font-size: 11px;
  color: white;
  font-weight: 800;
  margin: 0;
}

summary {
  width: fit-content;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  color: white;
  cursor: pointer;
}

.conteudo {
  width: fit-content;
  padding: 1px 5px;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  background-color: var(--app-container);
  position: absolute;
  border-radius: 10px;
}

.conteudo p {
  cursor: pointer;
  padding: 10px;
  border-radius: 3px;
}

.conteudo p:hover {
  background-color: #0c172f;
}
p,
span,
div {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
