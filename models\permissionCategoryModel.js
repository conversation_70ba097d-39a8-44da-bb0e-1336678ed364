const mongoose = require('mongoose');

const permissionCategorySchema = new mongoose.Schema(
  {
    title: {
      type: String,
      trim: true
    },
    createdBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Permission Category must been created from a User!']
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }

);
permissionCategorySchema.pre('save', async function( next) {
  next();
});

permissionCategorySchema.pre(/^find/, function(next) {
  next();
})

const PermissionCategory = mongoose.model('PermissionCategory', permissionCategorySchema);

module.exports = PermissionCategory;
