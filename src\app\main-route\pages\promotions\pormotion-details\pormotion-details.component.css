.wrapper {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2em;
  margin: 3em 1em;
}
.pormotionDetailButtons {
  display: flex;
  justify-content: space-around;
  width: 100%;
  height: 50px;
}
.pormotionDetailButtons > * {
  width: 40%;
}

.button {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  border-radius: 30px;
  background-color: #32dbc6;
  cursor: pointer;
}
.buttonActive {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  border-radius: 30px;
  background-color: #039582;
  cursor: pointer;
}
.tomatoBackround {
  background-color: rgb(250, 76, 45);
}
.paragraphButtonStyle {
  margin-bottom: 0;
  color: white;
  font-weight: 600;
  font-size: 20px;
}
.promDetailsInfoList {
  display: flex;
  flex-direction: column;
  gap: 1em;
  height: 600px;
}
.promDetailsInfo {
  padding: 1em 2em;
  background-color: rgb(238, 238, 238);
  width: 100%;
  display: flex;
  align-items: center;
  border-radius: 20px;
}
.promDetailsInfoTime {
  margin-left: auto;
  color: rgb(92, 92, 92);
  font-weight: 600;
  font-size: 16px;
}
.promDetailsInfoFirstChild {
  color: black;
  font-weight: 600;
  font-size: 16px;
}

.grayBackground {
  background: rgb(197, 197, 197) !important;
}


@media (max-width: 1355px) {
  .wrapper {
    grid-template-columns: 1.5fr 1fr;
  }
}

@media (max-width: 1355px) {
}
p,
span,
div {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
