import { Injectable } from '@angular/core';




export const dynamicScripts = [
  // "../assets/js/jquery-3.3.1.min.js",
  // "../assets/js/bootstrap.bundle.min.js",
  // "../assets/js/icons/feather-icon/feather.min.js",
  // "../assets/js/icons/feather-icon/feather-icon.js",
  // "../assets/js/sidebar-menu.js",
  // "../assets/js/slick.js",
  // "../assets/js/chart/chartist/chartist.js",
  // "../assets/js/chart/chartjs/chart.min.js",
  // "../assets/js/prism/prism.min.js",
  // "../assets/js/clipboard/clipboard.min.js",
  // "../assets/js/custom-card/custom-card.js",
  // "../assets/js/counter/jquery.waypoints.min.js",
  // "../assets/js/counter/jquery.counterup.min.js",
  // "../assets/js/counter/counter-custom.js",
  // "../assets/js/chart/sparkline/sparkline.js",
  // "../assets/js/dashboard/default.js",
  // "../assets/js/height-equal.js",
  // "../assets/js/lazysizes.min.js",
  // "../assets/js/admin-script.js",
];



@Injectable({
  providedIn: 'root'
})
export class LoadJsService {

  private scripts: any = {};

  constructor() {}

   loadScripts() {
    for (let i = 0; i < dynamicScripts.length; i++) {
      const node = document.createElement('script');
      node.src = dynamicScripts[i];
      node.type = 'text/javascript';
      document.getElementsByTagName('head')[0].appendChild(node);
    }
  }

  removeScripts(){
    let head = document.getElementsByTagName('head')[0];
    let scripts = head.getElementsByTagName('script');

    for (let i = 0; i < scripts.length; i++) {
      head.removeChild(scripts[i]);
    }
  }

}
