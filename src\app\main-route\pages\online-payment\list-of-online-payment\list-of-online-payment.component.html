<div class="projects-section">
  <div
    style="
      display: grid;
      grid-template-rows: 4em 2em 1fr 4em;
      background-color: var(--sidebar);
      padding: 5px 1em;
      border-radius: 20px;
      height: 95%;
    "
  >
    <div class="disp-flex a-i-center">
      <span class="no-wrap-1-line">Recent Payment Online Transactions</span>
      <div class="m-l-auto disp-flex">
        <div class="search-bar">
          <input
            (keyup)="searchUser($event)"
            type="text"
            placeholder="Search"
          />
        </div>
        <!-- <button class="add-btn" title="Add New Project">
          <svg
            class="btn-icon"
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="3"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="feather feather-plus"
          >
            <line x1="12" y1="5" x2="12" y2="19" />
            <line x1="5" y1="12" x2="19" y2="12" />
          </svg>
        </button> -->
      </div>
    </div>
    <div
      style="
        display: grid;
        grid-template-columns: 1.5fr 1fr 1fr 1fr 0.5fr;
        background-color: rgba(26, 37, 59, 0.5);
        align-items: center;
        margin-bottom: 5px;
        border-radius: 10px;
        gap: 1em;
      "
    >
      <span style="margin-left: 1em" class="no-wrap-1-line">User Details</span>
      <span class="no-wrap-1-line">Credits Amount</span>
      <span class="no-wrap-1-line">Money Amount</span>
      <span class="no-wrap-1-line">Order Source</span>
      <span class="disp-flex j-c-center">Actions</span>
    </div>
    <ul style="padding-inline-start: 0px; overflow: scroll" class="">
      <!-- List empty  -->
      <div
        *ngIf="data?.length === 0 && !startLoading"
        style="
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
        "
      >
        <img
          style="width: 70%; max-width: 299px"
          src="/assets/icons/animatedIconsTable/ONLINEPAYMENTLIST_EMPTY_INFINITE.svg"
          alt=""
        />
        <p style="font-size: 16px">List is empty</p>
      </div>
      <!-- List empty end -->
      <div *ngIf="startLoading">
        <li
          *ngFor="let i of [].constructor(15)"
          class="skeleton1"
          style="
            display: grid;
            grid-template-columns: 1.5fr 1fr 1fr 1fr 0.5fr;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div style="gap: 7px" class="disp-flex">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 40px;
                height: 40px;
                border-radius: 50%;
              "
            ></span>
            <div class="disp-grid">
              <span
                class="skeleton2"
                style="
                  font-size: 14px;
                  font-weight: 500;
                  opacity: 0.7;
                  width: 177px;
                  height: 17px;
                  margin-bottom: 4px;
                "
              ></span>
              <span
                class="skeleton2"
                style="
                  font-size: 14px;
                  font-weight: 500;
                  opacity: 0.7;
                  width: 111px;
                  height: 16px;
                "
              ></span>
            </div>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 111px;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 111px;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 177px;
                height: 17px;
                margin-bottom: 4px;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 111px;
                height: 16px;
              "
            ></span>
          </div>

          <div class="disp-flex j-c-center">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 28px;
                height: 10px;
                margin-bottom: 4px;
              "
            ></span>
          </div>
        </li>
      </div>

      <li
        *ngFor="let dat of data; let i = index"
        style="
          display: grid;
          grid-template-columns: 1.5fr 1fr 1fr 1fr 0.5fr;
          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
        "
      >
        <div 
        class="disp-flex gap-05">
        <div
        style="position: relative;">
          <app-avatar-photo
          [buffer]="dat?.user?.photoProfile?.data"
          [userId]="dat?.user?._id"
          [circleColor]="dat?.user?.photoColor"
          [name]="dat?.user?.name"
          [surname]="dat?.user?.surname"
          [class]="'userImgBloggers'"
          [classAvatarInitials]="'initialsClass'"
        ></app-avatar-photo>
        <img
        *ngIf="dat?.user?.isVerified"
          style="
            height: 15px;
            width: 15px;
            position: absolute;
            bottom: 0;
            right: 0;"
          src="/assets/icons/fiberVerified.svg"
          alt=""
        />
        <img
        *ngIf="dat?.user?.untrusted"
          style="
            height: 15px;
            width: 15px;
            position: absolute;
            bottom: 0;
            right: 0;"
          src="/assets/icons/fiberUnverified.svg"
          alt=""
        />
        </div>

          <div class="disp-grid">
            <span style="font-size: 16px" class="no-wrap-1-line"
              >{{ dat?.user?.name }} {{ dat?.user?.surname }}</span
            >
            <span
              class="no-wrap-1-line"
              style="font-size: 14px; font-weight: 500; opacity: 0.7"
              >{{ dat?.createdAt | date : "short" }}</span
            >
          </div>
        </div>

        <div class="disp-grid">
          <span style="font-size: 16px" class="no-wrap-1-line"
            >{{ dat?.creditsAmount }} credits</span
          >
        </div>
        <div class="disp-grid">
          <span style="font-size: 16px" class="no-wrap-1-line"
            >{{ dat?.realMoneyAmount }} {{ dat?.realMoneyCurrency }}</span
          >
        </div>
        <div class="disp-grid">
          <span style="font-size: 16px" class="no-wrap-1-line">{{
            dat?.type
          }}</span>
          <span
            class="no-wrap-1-line"
            style="font-size: 14px; font-weight: 500; opacity: 0.7"
          >
            <!-- a**@****.com -->
          </span>
        </div>
        <div class="disp-flex j-c-center">
          <span
            (click)="selectOnlinePayment(dat); openOptionsModal(optionsContent)"
            class="no-wrap-1-line"
            >•••</span
          >
        </div>
      </li>
    </ul>
    <div class="list-number disp-flex" style="">
      <div
        class="showingInfoWrapper"
        style="
          margin: 0 0 0.75rem;
          display: flex !important;
          align-items: center;
          gap: 1em;
        "
      >
        <span class="showingInfo"
          >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
          {{ pageNo * resultsPerPage - (resultsPerPage - data.length) }} of
          {{ count }}</span
        >
        <div style="border: 1px solid gray; border-radius: 15px">
          <div>
            <input style="display: none" id="dropdownInput" type="checkbox" />
            <div
              style="
                display: flex;
                justify-content: center;
                height: 45px;
                width: 50px;
              "
              class="dropdown"
            >
              <select
                style="
                  display: flex;
                  background: transparent;
                  border: none;
                  color: white;
                  font-size: 18px;
                  width: 90%;
                  font-weight: 600;
                "
                (ngModelChange)="resultsPerPageChanged($event)"
                [(ngModel)]="resultsPerPage"
              >
                <option class="optionStyle colorCancel">7</option>
                <option class="optionStyle colorCancel">10</option>
                <option class="optionStyle colorCancel">15</option>
                <option class="optionStyle colorCancel">20</option>
                <option class="optionStyle colorCancel">30</option>
                <option class="optionStyle colorCancel">50</option>
              </select>
              <label for="dropdownInput" class="overlay"></label>
            </div>
          </div>
        </div>
      </div>
      <nav>
        <ul class="pager">
          <li class="pager__item pager__item--prev">
            <a
              *ngIf="pageNo !== 1"
              (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Previous
            </a>
          </li>
          <li *ngIf="pageNo !== 1" class="pager__item">
            <a (click)="setPage(1)" class="pager__link">...</a>
          </li>
          <li
            *ngFor="
              let item of [].constructor(pageNoTotal) | slice : 0 : 5;
              let i = index
            "
            [ngClass]="pageNo + i === pageNo ? 'active' : ''"
            class="pager__item"
          >
            <a
              *ngIf="pageNo + i <= pageNoTotal"
              (click)="setPage(pageNo + i)"
              class="pager__link"
              >{{ pageNo + i }}</a
            >
          </li>
          <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
            <a (click)="setPage(pageNoTotal)" class="pager__link">...</a>
          </li>
          <li
            *ngIf="pageNo !== pageNoTotal"
            class="pager__item pager__item--next"
          >
            <a
              (click)="pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Next
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
  <ng-template #optionsContent let-modal>
    <div
      style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1"
    >
      <div class="modal-body">
        <!-- header -->
        <div style="display: flex; justify-content: end; width: 100%">
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; margin-left: auto"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->
        <div
          style="
            background: var(--app-bg);
            min-height: 350px;
            height: 100%;
            max-height: fit-content;
            width: 100%;
            display: grid;
            justify-content: center;
            align-items: center;
          "
        >
          <div
            style="display: flex; flex-direction: column; align-items: center"
          >
            <div style="padding: 0 2em; width: 100%">
              <div
                style="
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
              >
                <p style="font-size: 18px; font-weight: 700; margin: 1em 0 0">
                  Online Payment Transaction Detail
                </p>
              </div>
            </div>
            <div
              style="
                padding: 2em 2em;
                width: 100%;
                display: flex;
                flex-direction: column;
                gap: 1em;
              "
            >
              <div style="border: 1px solid gray; border-radius: 15px">
                <p
                  style="
                    margin: -11px 0px 0 14px;
                    /* font-size: 16px; */
                    font-weight: 600;
                    background: var(--app-bg);
                    width: fit-content;
                    padding: 0 13px;
                  "
                >
                  Order Details
                </p>
                <div
                  style="
                    width: 100%;
                    padding: 0em 2em 1em;
                    text-align: center;
                    border-radius: 10px;
                    display: flex;
                    flex-direction: column;
                    gap: 1em;
                  "
                  class=""
                >
                  <div style="display: flex; gap: 1em">
                    <div style="width: 100%">
                      <p
                        class="no-wrap-1-line"
                        style="text-align: start; margin: 0 1em"
                      >
                        Name Surname
                      </p>
                      <p
                        style="
                          box-shadow: 0px 2px 2px #67aeb1;
                          border-radius: 10px;
                          margin: 0;
                          line-height: 2.5;
                        "
                      >
                        {{ selectedOnlinePayment?.user?.name }}
                        {{ selectedOnlinePayment?.user?.surname }}
                      </p>
                    </div>
                    <div style="width: 100%">
                      <p style="text-align: start; margin: 0 1em">Time</p>
                      <p
                        class="no-wrap-1-line"
                        style="
                          box-shadow: 0px 2px 2px #67aeb1;
                          border-radius: 10px;
                          margin: 0;
                          line-height: 2.5;
                          padding: 0 9px;
                        "
                      >
                        {{
                          selectedOnlinePayment?.createdAt
                            | date : "MMMM d, y, h:mm:ss a"
                        }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div style="display: flex; gap: 1em">
                <div
                  style="
                    border: 1px solid gray;
                    border-radius: 15px;
                    width: 100%;
                  "
                >
                  <p
                    style="
                      margin: -11px 0px 0 7px;
                      font-weight: 600;
                      background: var(--app-bg);
                      width: fit-content;
                      padding: 0 3px;
                    "
                  >
                    Credits Amount
                  </p>
                  <div
                    style="
                      width: 100%;
                      padding: 0em 2em 1em;
                      text-align: center;
                      border-radius: 10px;
                      display: flex;
                      flex-direction: column;
                      gap: 1em;
                    "
                    class=""
                  >
                    <div style="width: 100%">
                      <p
                        style="
                          text-align: start;
                          padding: 0 1em;
                          margin: 1em 0 0 0;
                        "
                      >
                        Credits
                      </p>
                      <p
                        style="
                          box-shadow: 0px 2px 2px #67aeb1;
                          border-radius: 10px;
                          margin: 0;
                          line-height: 2.5;
                        "
                      >
                        {{ selectedOnlinePayment?.creditsAmount }} credits
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  style="
                    border: 1px solid gray;
                    border-radius: 15px;
                    width: 100%;
                  "
                >
                  <p
                    style="
                      margin: -11px 0px 0 7px;
                      font-weight: 600;
                      background: var(--app-bg);
                      width: fit-content;
                      padding: 0 3px;
                    "
                    class="no-wrap-1-line-200"
                  >
                    Exchanged Amount
                  </p>
                  <div
                    style="
                      width: 100%;
                      padding: 0em 2em 1em;
                      text-align: center;
                      border-radius: 10px;
                      display: flex;
                      flex-direction: column;
                      gap: 1em;
                    "
                    class=""
                  >
                    <div style="width: 100%">
                      <p
                        style="
                          text-align: start;
                          padding: 0 1em;
                          margin: 1em 0 0 0;
                        "
                      >
                        Exchanged
                      </p>
                      <p
                        style="
                          box-shadow: 0px 2px 2px #67aeb1;
                          border-radius: 10px;
                          margin: 0;
                          line-height: 2.5;
                        "
                      >
                        {{ selectedOnlinePayment?.realMoneyAmount }}
                        {{ selectedOnlinePayment?.realMoneyCurrency }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
</div>
