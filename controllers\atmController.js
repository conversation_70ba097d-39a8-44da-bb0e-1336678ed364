const catchAsync = require("./../utils/catchAsync");
// const CentralBank = require('./../models/centralBankModel');
// const MainData = require('./../models/mainDataModel');
const User = require("./../models/userModel");

const Transaction = require("./../models/transactionModel");
const ATM = require("./../models/atmModel");
// const MinistryOfFinance = require('./../models/ministryOfFinanceModel');
// const OperationLog = require('./../models/operationLogModel');

exports.updateStatus = catchAsync(async (req, res, next) => {
  let data = {
    updatedAt: req.body.updatedAt,
    userWhoUpdated: req.body.userWhoUpdated,
    status: req.body.status,
    detailDescription: req.body.detailDescription,
  };
  const doc = await ATM.findByIdAndUpdate(req.params.id, data);
  res.status(201).json({
    status: "success",
  });
});

exports.create = catchAsync(async (req, res, next) => {
  let transaction = null;
  let ministryOfFinanceTransaction = null;
  let atmTransaction = null;
  let operationLog = null;

  let userUpdated = {
    coins: 0,
  };

  console.log("req.body: ", req.body);

  // console.log("req.body.transaction: ",req.body.transaction)
  // console.log("req.body.centralBank: ",req.body.centralBank)
  // console.log("req.body.onlinePayment: ",req.body.onlinePayment)

  atmTransaction = await ATM.create({
    withdrawRealMoneyCurrency: req.body.withdrawRealMoneyCurrency,
    withdrawCreditsAmount: req.body.withdrawRealMoneyAmount,
    withdrawRealMoneyAmount: req.body.withdrawRealMoneyAmount,
    realMoneyAmount: req.body.withdrawRealMoneyAmount,
    feeCreditsAmount: req.body.feeCreditsAmount,
    creditsAmount: req.body.withdrawCreditsAmount,
    status: "waitingConfirm",
    detailDescription:
      "We have received your request to convert credits to " +
      req.body.withdrawRealMoneyCurrency +
      '. To proceed, confirm by selecting "confirm" or cancel by selecting "cancel" if uncertain. Contact us for assistance.',
    email: req.body.email,
    type: "officeWithdraw",
    user: req.body.userId,
  });
  if (atmTransaction !== null) {
    res.status(201).json({
      status: "success",
      atmTransaction: atmTransaction,
    });
  }
});

exports.getMyAtmTransactions = catchAsync(async (req, res, next) => {
  let resultsPerPage =
    req.body.resultsPerPage >= 5 ? req.body.resultsPerPage : 5;
  let page = req.body.page >= 1 ? req.body.page : 1;
  page = page - 1;

  const data = await ATM.find(
    { user: req.params.id.toString() },
    { transaction: 0, fee: 0 }
  )
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ createdAt: -1 });

  res.status(200).json({
    status: "success",
    data: {
      data,
    },
  });
});

exports.getAll = catchAsync(async (req, res, next) => {
  console.log("getAll");
  let resultsPerPage = req.body.resultsPerPage;
  let page = req.body.page;
  page = page - 1;

  let filter = {};
  if (req.body.status !== "All") {
    filter.status = req.body.status.toLowerCase();
  }

  if (req.body.status == "Waiting Confirm") {
    filter.status = "waitingConfirm";
  }

  if (req.body.status == "Success") {
    filter.status = "succes";
  }

  let data = await ATM.find(filter)
    .populate({
      path: "user",
      select: [
        "id",
        "_id",
        "name",
        "surname",
        "isVerified",
        "photoColor",
        "untrusted",
        "earnCount",
        "likesCount",
        "commentsCount",
        "postCount",
        "email",
      ],
    })
    .limit(resultsPerPage)
    .skip(resultsPerPage * page)
    .sort({ _id: -1 });

  let countList = await ATM.find(filter, { user: 0 });
  let count = countList.length;

  res.status(200).json({
    status: "success",
    data: {
      data: data,
      count: count,
    },
  });
});

exports.searchATM = catchAsync(async (req, res, next) => {
  if (req.body.searchText !== "") {
    let resultsPerPage = req.body.resultsPerPage;
    let page = req.body.page;
    page = page - 1;
    req.body.searchText = req.body.searchText.trim();

    let name = req.body.searchText.split(" ")[0];
    let surname =
      req.body.searchText.split(" ")[1] === undefined
        ? name
        : req.body.searchText.split(" ")[1];

    let nameRegex = name.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");
    let surnameRegex = surname.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");

    nameRegex = new RegExp(nameRegex, "i");
    surnameRegex = new RegExp(surnameRegex, "i");

    let filter = {
      $or: [
        { "user.name": { $regex: nameRegex } },
        { "user.surname": { $regex: surnameRegex } },
      ],
    };
    if (req.body.status !== "All") {
      filter.status = req.body.status.toLowerCase();
    }

    if (req.body.status == "Success") {
      filter.status = "succes";
    }
    console.log("req.body: ", req.body);
    ATM.find(filter)
      .populate({
        path: "user",
        select: [
          "id",
          "_id",
          "name",
          "surname",
          "isVerified",
          "photoColor",
          "untrusted",
        ],
      })
      .limit(resultsPerPage)
      .skip(resultsPerPage * page)
      .sort({ _id: -1 })
      .then(async (result) => {
        // console.log("result: ",result)
        let countList = await ATM.find(filter, { user: 0 });
        let count = countList.length;
        res.status(200).json({
          status: "success",
          data: {
            data: result,
            count: count,
          },
        });
      });
  } else {
    res.status(200).json({
      status: "success",
      resultSearch: result,
    });
  }
});
