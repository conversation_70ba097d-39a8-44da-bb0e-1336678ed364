import { SearchUserModalComponent } from './search-user-modal/search-user-modal.component';
import { Component, OnInit } from '@angular/core';
import { Options, LabelType } from 'ngx-slider-v2';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/shared/services/user.service';
import { ToastrService } from 'ngx-toastr';
import { ListModalComponent } from './list-modal/list-modal.component';
import * as moment from 'moment';
import { SocketService } from 'src/app/shared/services/socket.service';
import { PromotionAvailableService } from 'src/app/shared/services/promotion-available.service';
import { AuthService } from 'src/app/shared/services/auth.service';
import { CompressImageService } from 'src/app/shared/services/compress-image.service';
import { PromotionService } from 'src/app/shared/services/promotion.service';
import { FiberBankTransactionService } from 'src/app/shared/services/fiber-bank-transaction.service';
import { ActivatedRoute, Router } from '@angular/router';
import { UntypedFormBuilder } from '@angular/forms';
import { User } from 'src/app/shared/models/user';
import { ImageService } from 'src/app/shared/services/image.service';

@Component({
  selector: 'app-send-promotion',
  templateUrl: './send-promotion.component.html',
  styleUrls: ['./send-promotion.component.css'],
})
export class SendPromotionComponent implements OnInit {

  loading = false
  startLoading = false
  startSubmitLoading = false

  disableSubmit = false 

  userFilteredData = []
  pageNumber = 0
  loadingData =  false
  resultLength = 0

  checkboxFalseUsersId = []
  checkboxFalseUsers = []
  allUsers = []

  followersCountActive = false 
  followersCountMin = 0
  followersCountMax = 0

  followingCountActive = false 
  followingCountMin = 0
  followingCountMax = 0

  postCountActive = false 
  postCountMin = 0
  postCountMax = 0

  earnCountActive = false 
  earnCountMin = 0
  earnCountMax = 0

  filters : any = {
    followersCountActive : false,
    followingCountActive : false,
    postCountActive : false, 
    earnCountActive : false 
  }

  totalUsersWithFilter = 0


  options: Options = {
    floor: 0,
    ceil: 100,
    translate: (value: number, label: LabelType): string => {
      switch (label) {
        case LabelType.Low:
          return '' + value;
        case LabelType.High:
          return '' + value;
        default:
          return value + '';
      }
    },
  };

  thumbnailFile: File = null; 
  thumbnail = null;
  thumbnailImageId = null;

  timeout: any = null;


  timeAllowedToShareMinutes = 5
  minutesDiffernce = 10
  startPromoteDateTime = new Date();

  socket

  selectedFile: File[] = []; 
  img = null;
  imageSize = '0'
  imageName = ''

  public selectedMoment = new Date();

  cost :  number = 0
  allowedToShare :  number = 0
  linkUrl:string = ''

  me: User

  images = []

  constructor(
    private router: Router,
    private imageService: ImageService,
    private promotionAvailableService: PromotionAvailableService, 
    private authService: AuthService,
    private compressImageService: CompressImageService,
    private promotionService: PromotionService,
    private fiberBankTransactionService: FiberBankTransactionService,
    private activatedRoute: ActivatedRoute,
    private formBuilder: UntypedFormBuilder,
    private socketService: SocketService,
    private toastrService: ToastrService,
    private userService: UserService, 
    private modalService: NgbModal) {
      this.socket = this.socketService.setupSocketConnection();

    }


  ngOnInit(): void {
    this.authService.user.pipe().subscribe(appUser => {
      this.me = appUser
    })
    this.userService.sentFinalUserFilteredObservable.subscribe((data:any) => {
      console.log("SendPromotionComponent: ",data)
      if(data !== null){
        if(data.allUsers.length !== 0){
        this.allUsers = data.allUsers
        }
        if(data.checkboxFalseUsersId.length !== 0){
          this.checkboxFalseUsersId = data.checkboxFalseUsersId
        }
        if(data.userFilteredData.length !== 0){
          this.userFilteredData = data.userFilteredData
          this.totalUsersWithFilter = this.userFilteredData.length
        }
      }
    });
  }

  openUserModal() {
    this.resultLength = 0
    this.pageNumber =  0
    // this.checkboxFalseUsersId = []
    
    this.userService.changeUserFiltered(
      {
        allUsers: this.allUsers,
        filters: this.filters
      })
    this.modalService.open(ListModalComponent, { size: 'xl' });
  }

  selectThumbnail(event) {
    console.log("thumbnailFile")
    if(event.target.files[0]){

      this.imageSize = (event.target.files[0].size / (1024 * 1024) ).toFixed(2)
      this.imageName = event.target.files[0].name
  
      this.compressImageService.compress(event.target.files[0]).subscribe( result =>{
        // this.selectedFile = result;
        this.selectedFile = [...this.selectedFile,result] 
  
      })

      this.thumbnailFile = event.target.files[0]
      var reader = new FileReader();
      reader.readAsDataURL(event.target.files[0])
      reader.onload = (_event) => {
        this.compressImageService.compressImage(reader.result,800,800).then(compressed => {
          this.img = compressed;
        })
        this.thumbnail = reader.result;
      }
  
    }

  }

  search(object,type){
    console.log("searchText: ",object)
    this.startLoading = true
    let obj = {
      searchText: object?.searchText,
      inputForm: object?.inputForm,
      value: object?.value,
      highValue: object?.highValue,
    }
    if(obj?.value !== undefined){
      if(this.followersCountMin === obj?.value && type == 'followers'){
        obj.searchText =obj?.value
        obj.inputForm = 'followersCountMin'
      }

      if(this.followingCountMin === obj?.value && type == 'following'){
        obj.searchText =obj?.value
        obj.inputForm = 'followingCountMin'
      }

      if(this.postCountMin === obj?.value && type == 'post'){
        obj.searchText =obj?.value
        obj.inputForm = 'postCountMin'
      }

    }
    if(obj?.highValue !== undefined){
      if(this.followersCountMax == obj?.highValue && type == 'followers' ){
        obj.searchText =obj?.highValue
        obj.inputForm = 'followersCountMax'
      }
      if(this.followingCountMax == obj?.highValue && type == 'following' ){
        obj.searchText =obj?.highValue
        obj.inputForm = 'followingCountMax'
      }
      if(this.postCountMax == obj?.highValue && type == 'post' ){
        obj.searchText =obj?.highValue
        obj.inputForm = 'postCountMax'
      }
    }

    clearTimeout(this.timeout);
    var $this = this;
    this.timeout = setTimeout(function () {
        $this.getUserFiltered(obj?.searchText,obj?.inputForm);
    }, 1000);

  }

  getUserFiltered(value,inputForm){
    if(inputForm === 'followersCountMin'){
      if(value !== 0){
        this.filters['followersCountMin'] = value
      }else{
        delete  this.filters.followersCountMin
      }
    }
    if(inputForm === 'followersCountMax'){
      if(value !== 0){
        this.filters['followersCountMax'] = value
      }else{
        delete  this.filters.followersCountMax
      }
    }


    if(inputForm === 'followingCountMin'){
      if(value !== 0){
        this.filters['followingCountMin'] = value
      }else{
        delete  this.filters.followingCountMin
      }
    }
    if(inputForm === 'followingCountMax'){
      if(value !== 0){
        this.filters['followingCountMax'] = value
      }else{
        delete  this.filters.followingCountMax
      }
    }


    if(inputForm === 'postCountMin'){
      if(value !== 0){
        this.filters['postCountMin'] = value
      }else{
        delete  this.filters.postCountMin
      }
    }
    if(inputForm === 'postCountMax'){
      if(value !== 0){
        this.filters['postCountMax'] = value
      }else{
        delete  this.filters.postCountMax
      }
    }


    if(inputForm === 'earnCountMin'){
      if(value !== 0){
        this.filters['earnCountMin'] = value
      }else{
        delete  this.filters.earnCountMin
      }
    }
    if(inputForm === 'earnCountMax'){
      if(value !== 0){
        this.filters['earnCountMax'] = value
      }else{
        delete  this.filters.earnCountMax
      }
    }
    this.prepareFilter()    
  }

  prepareFilter(){
    this.filters.followersCountActive = this.followersCountActive
    this.filters.followingCountActive = this.followingCountActive
    this.filters.postCountActive = this.postCountActive
    this.filters.earnCountActive =  this.earnCountActive
    this.loading = true
    this.userService.getUserFiltered(this.filters).subscribe( respond  => {
                if( respond.status = "success"){
                  this.allUsers = []
                  this.allUsers = respond.data.data
                  // console.log("respond.data: ",respond.data.data)
                  this.totalUsersWithFilter = respond.data.totalUsersWithFilter
                  this.loading = false
                  this.startLoading = false
                }
            },
            respond_error => {
                this.startLoading = false
                this.loading = false
                this.disableSubmit = false
                let error_message = respond_error.error.message;
                this.toastrService.error(error_message);            
      })
  }
  
  followersCountChange(event){
    if(event?.checked == true){
      this.followersCountActive = true;
    }
    if(event?.checked == false){
      this.followersCountActive = false;
      this.followersCountMin = null;
      this.followersCountMax = null;
      this.getUserFiltered('','followersCountMin');
      this.getUserFiltered('','followersCountMax')
    }

  }

  followingCountChange(event){
    if(event?.checked == true){
      this.followingCountActive = true;
    }
    if(event?.checked == false){
      this.followingCountActive = false;
      this.followingCountMin = null;
      this.followingCountMax = null;
      this.getUserFiltered('','followingCountMin');
      this.getUserFiltered('','followingCountMax')
    }

  }


  postCountChange(event){
    if(event?.checked == true){
      this.postCountActive = true;
    }
    if(event?.checked == false){
      this.postCountActive = false;
      this.postCountMin = null;
      this.postCountMax = null;
      this.getUserFiltered('','postCountMin');
      this.getUserFiltered('','postCountMax')
    }

  }

  earnCountChange(event){
    if(event?.checked == true){
      this.earnCountActive = true;
    }
    if(event?.checked == false){
      this.earnCountActive = false;
      this.earnCountMin = null;
      this.earnCountMax = null;
      this.getUserFiltered('','earnCountMin');
      this.getUserFiltered('','earnCountMax')
    }

  }


  async submit(){


    this.disableSubmit = true
    this.loading = true

    if (this.cost*this.allowedToShare <= 0){
      this.disableSubmit = false
      this.loading = false
      this.toastrService.error('Budget of this promotion need to be more than 0 coins');   
      return
    }
    if (this.selectedFile.length == 0) {
      this.disableSubmit = false
      this.loading = false
      this.toastrService.error('Empty post without images is not allowed to be posted');   
       return 
    }
    if(this.startSubmitLoading === true){
      return
    }
    this.startSubmitLoading = true

    this.uploadOriginalImage(this.selectedFile)
  } 

  async uploadOriginalImage(files){
   await this.imageService.uploadOriginalImage(
      files
    ).subscribe( async respond  => {
            if( respond.status = "success"){
                  console.log("uploadOriginalImage: ",respond.data._id.toString())
                  // this.thumbnail = `${BACKEND_Image_URL}${respond.data._id.toString()}`;
                  this.thumbnailImageId = respond.data._id
                  this.images = []
                  this.images.push({
                    data: null,
                    _id:this.thumbnailImageId})
                  this.create()
            }
        },
        respond_error => {
            this.startSubmitLoading = false
            let error_message = respond_error.error.message;
            this.toastrService.error(error_message);            
        }
    );
  }


  async create(){
    let startEnd = this.startPromoteDateTime
    console.log("this.allUsers: ",this.allUsers)
    let  totalUsersWithFilter = await this.allUsers.filter(data => {
      if(this.checkboxFalseUsersId.indexOf(data._id) === -1){
        return data._id
      }
    });

    let startTime = startEnd
    startEnd = moment(startTime).add(this.timeAllowedToShareMinutes, 'm').toDate();  
    

      this.disableSubmit = false
      let specificUsersCount = totalUsersWithFilter.length
      this.startSubmitLoading = true
      this.promotionService.create(
        this.me._id,
        this.cost,
        specificUsersCount,
        this.allowedToShare,
        this.followersCountActive,
        this.followersCountMin,
        this.followersCountMax,
        this.followingCountActive,
        this.followingCountMin,
        this.followingCountMax,
        this.postCountActive,
        this.postCountMin,
        this.postCountMax,
        this.earnCountActive,
        this.earnCountMin,
        this.earnCountMax,
        this.timeAllowedToShareMinutes,
        this.minutesDiffernce,
        this.startPromoteDateTime,
        this.linkUrl,
        this.images
      ).subscribe( async respond  => {
        this.loading = false
        this.disableSubmit = false
              if( respond.status = "success"){
                let reason = "Budget from " +this.linkUrl + " for Fiber Bank Account to promote"
                let i = 1
                for(let user of totalUsersWithFilter){
                    startTime = moment(startEnd).add(this.minutesDiffernce, 'm').toDate();
                    startEnd = moment(startTime).add(this.timeAllowedToShareMinutes, 'm').toDate();  
                    await this.createPromotionAvailable(
                      startTime,
                      startEnd,
                      respond.data.data._id,
                      user._id
                    )
                    if(i === totalUsersWithFilter.length){
                      this.socket.emit('ipromotion', {
                        promotionId: respond.data.data._id,
                        sendStatus: true,
                        meId: user._id,
                        startTime: this.startPromoteDateTime
                      });
                      this.startSubmitLoading = false
                    }
                    i = i + 1
                }
                this.toastrService.success( "Successfully promotion has been created" );
                this.img = null
                this.selectedFile = []
                this.cost = 0
                this.allowedToShare = 0
                this.linkUrl = ''
                this.loading = false
                this.disableSubmit = false
                this.allUsers = []
                this.checkboxFalseUsersId = []
                this.totalUsersWithFilter = 0
                this.userFilteredData = []
                this.userService.changeFinalUserFiltered(
                  {
                    userFilteredData: [],
                    allUsers: [],
                    checkboxFalseUsersId: []
                  })
                this.userService.changeUserFiltered(
                    {
                      allUsers: [],
                      filters: null
                    }
                )
                this.router.navigate(["/" + 'en' + "/list-promotions"]).then(() => {});
                // this.create(this.cost*this.allowedToShare,reason)
              }
          },
          respond_error => {
              this.loading = false
              this.disableSubmit = false
              this.startSubmitLoading = false
              let error_message = respond_error.error.message;
              this.toastrService.error(error_message);            
          }
      ); 
    

  } 

  async createPromotionAvailable(
    timeStart,
    timeEnd,
    promotion,
    user
  ){
    this.promotionAvailableService.create(
      timeStart,
      timeEnd,
      user,
      promotion
      ).subscribe( respond  => {
      if( respond.status = "success" ){}
      },
      respond_error => {
          let error_message = respond_error.error.message;
          this.toastrService.error(error_message);            
    })
  }

}
