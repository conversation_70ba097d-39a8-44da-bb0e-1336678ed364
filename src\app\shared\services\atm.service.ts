import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';

// const BACKEND_URL = environment.apiUrl + '/atm';
const BACKEND_URL = environment.base_url + "/atm/";

@Injectable({
  providedIn: 'root'
})
export class AtmService {

  constructor(
    private http: HttpClient
  ) { }

  create(
    withdrawRealMoneyCurrency,
    email,
    withdrawCreditsAmount,
    withdrawRealMoneyAmount,
    feeCreditsAmount,
    userId
  ){

    let data = {
      email:email,
      withdrawCreditsAmount: withdrawCreditsAmount,
      withdrawRealMoneyAmount: withdrawRealMoneyAmount,
      feeCreditsAmount: feeCreditsAmount,
      withdrawRealMoneyCurrency: withdrawRealMoneyCurrency,
      userId: userId
    }
    return  this.http.post<any>( BACKEND_URL, data)
  }


  getMyAtmTransactions(userId,page,resultsPerPage){

    let data = {
      page : page,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL+'/getMyAtmTransactions/'+userId,data)

  }


  updateStatus(atmTransactionId,data){
    return this.http.patch<any>(BACKEND_URL+atmTransactionId,data)
  }

  getAll(page,resultsPerPage,status){
    let data = {
      status:status,
      page : page,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL+'getAll',data)
  }


  searchATM(searchText,_id,pageNumber,resultsPerPage,status){
    let data = {
      searchText:searchText,
      userId: _id,
      page : pageNumber,
      resultsPerPage: resultsPerPage,
      status : status
    }
    return this.http.post<any>(BACKEND_URL + 'searchATM',data)
  }
  
}
