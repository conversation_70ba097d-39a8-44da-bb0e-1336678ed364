const mongoose = require('mongoose');

const promotionAvailableSchema = new mongoose.Schema(
  {
    promotion: {
      type: mongoose.Schema.ObjectId,
      ref: 'Promotion',
    },
    userAllowed: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'PromotionAvailable must have a user']
    },
    isActive: {
      type: Boolean,
      default: true
    },
    isShared: {
      type: Boolean,
      default: false
    },
    iPromotionNotificationSendStatus: {
      type: Boolean,
      default: false
    },
    nativeNotificationSendStatus: {
      type: Boolean,
      default: false
    },
    nativeMissedNotificationSendStatus: {
      type: Boolean,
      default: false
    },
    timeStart: {
      type: Date,
      default: Date.now
    },
    timeEnd: {
      type: Date,
      default: Date.now
    },
    seenMissed: {
      type: Boolean,
      default: false
    },
    createdAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: {
      type: Date,
      default: Date.now
    }
  }
);

promotionAvailableSchema.pre('save', async function( next) {

    // await this.populate({
    //   path: 'user',
    // }).execPopulate();;

    next();
  });
  
  
  
  promotionAvailableSchema.pre(/^find/, function(next) {
    // this.populate({
    //   path: 'user',
    // });
    this.populate({
      path: 'promotion',
    });
    this.populate({
      path: 'userAllowed',
    });
    next();
  })

const PromotionAvailable = mongoose.model('PromotionAvailable', promotionAvailableSchema);

module.exports = PromotionAvailable;
