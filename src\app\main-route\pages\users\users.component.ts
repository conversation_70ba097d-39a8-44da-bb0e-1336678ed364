import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { AuthService } from 'src/app/shared/services/auth.service';
import { UserService } from 'src/app/shared/services/user.service';

@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.css'],
})
export class UsersComponent implements OnInit {
  timeout: any = null;
  resultSearch: any = null;
  searchText = '';

  public pageNo: number = 1;
  public pageNoTotal: number = 1;
  count: number = 0;
  resultsPerPage: number = 7;
  data: any[] = [];
  loading = false;

  me: User;
  userSelected: any;

  listItems = [];
  statusSelected = 'All';
  userStatusSelected = {
    untrusted: false,
    isVerified: false,
  };

  src: string;
  title: string;
  imageBlobURL: string;

  constructor(
    private toastrService: ToastrService,
    private userService: UserService,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private modalService: NgbModal,
    private http: HttpClient
  ) {
    this.authService.user.pipe().subscribe((appUser) => {
      this.me = appUser;
    });
  }
  ngOnInit(): void {
    this.loading = true;
    this.activatedRoute.queryParams.subscribe((params) => {
      this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;
    });
    this.getAll();
  }

  ngOnChanges() {
    // This method is called whenever there are changes to the input properties, including userId and id.
    // You can handle changes here if needed.
  }

  // getProfileImage(userId) {
  //   // if (userId !== this.id) {
  //     // getProfileImage()
  //     // if (this.userId) {
  //       // this.loading = true;
  //       // this.id = this.userId;
  //       this.src = "https://images.fiber.al/api/v1/post/getProfileImageSrc/" + userId.toString();
  //     // }
  //   // }
  // }

  getAll() {
    this.userService
      .getAll(this.pageNo, this.resultsPerPage, this.statusSelected)
      .subscribe(
        async (result) => {
          if (result.status == 'success') {
            this.count = result.data.count;
            this.data = result.data.data;
            this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
            this.loading = false;
          }
        },
        (respond_error) => {
          this.toastrService.error(
            respond_error?.error.message,
            respond_error?.name
          );
        }
      );
  }

  resultsPerPageChanged(event) {
    this.loading = true;
    this.count = 1;
    this.pageNoTotal = 1;
    this.data = [];
    this.resultsPerPage = Number(event);
    this.setPage(1);
  }

  public setPage(page: number) {
    this.data = [];
    this.loading = true;
    this.router
      .navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: page },
        queryParamsHandling: 'merge', // preserve the existing query params in the route
        skipLocationChange: false, // do trigger navigation
      })
      .finally(() => {
        if (this.searchText === '') {
          this.getAll();
        } else {
          this.getAllResultSearch();
        }
      });
  }

  searchUser(searchText) {
    clearTimeout(this.timeout);
    var $this = this;
    this.timeout = setTimeout(function () {
      if (searchText.keyCode != 13) {
        $this.loading = true;
        $this.searchSocket(searchText.target.value);
      } else {
        $this.loading = true;
        $this.getAll();
      }
    }, 1000);
  }
  public searchSocket(searchText) {
    this.searchText = searchText.toString();
    if (this.searchText == '') {
      this.loading = true;
      this.setPage(1);
      return;
    }
    this.data = [];
    this.setPage(1);
  }
  getAllResultSearch() {
    if (this.searchText == '') return;

    this.loading = true;
    this.count = 1;
    this.pageNoTotal = 1;
    this.data = [];
    this.userService
      .search(
        this.searchText,
        this.me._id,
        this.pageNo,
        this.resultsPerPage,
        this.statusSelected
      )
      .subscribe(
        (result) => {
          if (result.status == 'success') {
            this.count = result.data.count;
            this.data = result.data.data;
            this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
            this.loading = false;
          }
        },
        (respond_error) => {
          this.loading = false;
          // this.toastrService.error(
          //   respond_error?.error.message,
          //   respond_error?.name
          // );
        }
      );
  }

  openSelectImgModal(selectImgModal) {
    this.modalService.open(selectImgModal, { centered: true });
  }

  openPostModal(postContent) {
    this.modalService.open(postContent, { centered: true });
  }

  openDeleteUserModal(deleteUserContent) {
    this.modalService.open(deleteUserContent, { centered: true });
  }

  openMoreInfoModal(moreInfoModal) {
    this.modalService.open(moreInfoModal, { centered: true });
  }

  changeStatus(event) {
    this.loading = true;
    this.count = 1;
    this.pageNoTotal = 1;
    this.data = [];
    this.setPage(1);
  }

  getStatus(isVerified, untrusted): String {
    if (untrusted) return 'Untrusted';
    if (isVerified) return 'Verified';
    if (untrusted == false && untrusted == false) return 'None';
  }

  changeUserStatus(event) {
    if (event == 'None') {
      this.userStatusSelected = {
        untrusted: false,
        isVerified: false,
      };
    }
    if (event == 'Untrusted') {
      this.userStatusSelected = {
        untrusted: true,
        isVerified: false,
      };
    }
    if (event == 'Verified') {
      this.userStatusSelected = {
        untrusted: false,
        isVerified: true,
      };
    }
  }

  saveUserStatus() {
    this.findUserByIdAndUpdate(this.userSelected._id, this.userStatusSelected);
  }

  deleteUser() {
    this.findUserByIdAndUpdate(this.userSelected._id, {
      untrusted: false,
      isVerified: false,
      active: !this.userSelected.active,
    });
  }

  findUserByIdAndUpdate(userId, updateData) {
    // this.getProfileImage(userId);
    // Ktau duhet te krijohet Postimi Verified
    this.userService.findByIdAndUpdate(userId, updateData).subscribe(
      async (result) => {
        if (result.status == 'success') {
          this.modalService.dismissAll();
          let index = this.data.findIndex((x) => x._id === result.data._id);
          this.data[index] = result.data;

          this.src =
            'https://images.fiber.al/api/v1/post/getProfileImageSrc/' +
            userId.toString();
          this.title = this.data[index].name + ' ' + this.data[index].surname;
          console.log(
            '🚀 ~ file: users.component.ts:267 ~ UsersComponent ~ this.title:',
            this.title
          );
          console.log(
            '🚀 ~ file: users.component.ts:265 ~ UsersComponent ~ this.src:',
            this.src
          );

          this.apiPosts(userId, '', false, this.src);
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }

  downloadImage() {
    const imageUrl = 'https://example.com/path-to-your-image.jpg'; // Replace with the actual URL of the image on the server

    this.http.get(imageUrl, { responseType: 'blob' }).subscribe(
      (response: Blob) => {
        this.imageBlobURL = URL.createObjectURL(response);
      },
      (error) => {
        console.error('Error downloading image:', error);
      }
    );
  }

  async apiPosts(
    userId,
    comment: string,
    isPayPerView: boolean,
    imageFile: any
  ) {
    console.log('apiPosts: ');
    // if (gettingData) {
    //   return false;
    // }

    const formData = new FormData();
    formData.append('fontSize', JSON.stringify(32));
    formData.append('fontColor', JSON.stringify('rgb(255 255 255 / 92%)'));
    formData.append('isStatusContent', JSON.stringify(false));
    formData.append('isEditedCropImage', JSON.stringify(true));
    formData.append('user', JSON.stringify(userId.toString()));
    formData.append('title', JSON.stringify(''));
    formData.append('comment', JSON.stringify(comment));
    formData.append('isPayPerView', JSON.stringify(isPayPerView));
    formData.append('isImageContent', JSON.stringify(true));
    formData.append('numberOfImages', JSON.stringify(0));
    formData.append('isVideoContent', JSON.stringify(false));
    formData.append('isShareLink', JSON.stringify(false));
    formData.append('isPromotion', JSON.stringify(false));
    formData.append('linkUrl', JSON.stringify(''));
    try {
      // setGettingData(true);
      const DevFiberUrl = 'http://192.168.31.249:8000/';
      const ProdFiberUrl = 'https://api.fiber.al/';

      const url = ProdFiberUrl + 'api/v1/post/createEditedPost/';
      const headers = new HttpHeaders({
        // Authorization: `Bearer ${token}`,
        Accept: 'application/json',
      });

      const response = await this.http
        .post<any>(url, formData, { headers })
        .toPromise();
      console.log('res: ', response?.postId);

      if (response.status === 'success') {
        console.log("🚀 ~ file: users.component.ts:324 ~ UsersComponent ~ response.status:Success",)
        // setGettingData(false);
        this.uploadPostImages(response.postId, imageFile,userId);
      } else {
        // setIsLoading(false);
        // setGettingData(false);
        console.log('Error success');
      }
    } catch (error) {
      // setIsLoading(false);
      // setGettingData(false);
      console.error('error', error);
    }
  }

  async uploadPostImages(postId: string, imageFile: any,userId) {
    const formData = new FormData();
    formData.append('postId', JSON.stringify(postId));
    formData.append('userId', JSON.stringify(userId.toString()));
    formData.append('isPayPerView', JSON.stringify(false));
    // console.log('imageFile: ', imageFile);

    const fileKey = 'file[0]';
    const { path, mime } = imageFile;
    const file = new Blob([path], { type: mime });
    formData.append(fileKey, file, `image_0.${mime.split('/')[1]}`);

    try {
      // setGettingData(true);
      const url = 'https://images.fiber.al/api/v1/post/uploadPostImages/';
      const headers = new HttpHeaders({
        Accept: 'application/json'
      });

      const response = await this.http.post<any>(url, formData, { headers }).toPromise();
      console.log('images: ', response);

      if (response.status === 'success') {
        // setIsLoading(false);
        // setGettingData(false);
        // this.router.navigate(['/home']); // Change to your desired route
      } else {
        // setIsLoading(false);
        // setGettingData(false);
      }
    } catch (error) {
      // setIsLoading(false);
      // setGettingData(false);
      console.error('errorAmr', error);
    }
  }
}
