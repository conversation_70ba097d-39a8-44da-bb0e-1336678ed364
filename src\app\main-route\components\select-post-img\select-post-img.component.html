<div
style="
  background-color: var(--app-bg);
  border-radius: 20px;
  scale: 1.1;
  height: 100%;
"
>
<div class="modal-body">
  <!-- header -->
  <div style="display: flex; justify-content: end; width: 100%">
    <img
      (click)="modalDismiss()"
      style="height: 17px; width: 17px; margin-left: auto"
      src="/assets/icons/close.svg"
      alt=""
    />
  </div>

  <div style="margin: 1em 0">
    <div
      style="
        display: flex;
        flex-direction: column;
        gap: 1em;
        align-items: center;
      "
    >
      <!-- list -->
      <!-- display: flex; -->
      <div
        style="
          display: flex;
          overflow: scroll;
          width: 100%;
          padding: 0 2em;
          flex-direction: column;
          gap: 1em;
          margin-bottom: 1em;
          transform: translateX(5px);
          max-height: 54vh;">

        <ng-container *ngIf="loading">
          <div
            *ngFor="let i of [].constructor(20)"
            style="
              padding: 0.5em 1em !important;
              height: 64px;
              align-items: center;
              gap: 1em;
            "
            class="skeleton1 disp-flex"
          >
            <div
              style="height: 40px; width: 40px; border-radius: 14px"
              class="skeleton2"
            ></div>
            <div
              style="height: 18px; width: 118px; border-radius: 3px"
              class="skeleton2"
            ></div>
            <div
              style="
                height: 25px;
                width: 47px;
                border-radius: 6px;
                margin-left: auto;
              "
              class="skeleton2"
            ></div>
          </div>
        </ng-container>

        <ng-container *ngIf="data?.length && !loading">
          <li *ngFor="let dat of data" style="list-style: none">
            <div
              style="padding: 0.5em 1em !important"
              class="cardWrapper"
            >
              <div
                style="display: flex; align-items: center; gap: 1em"
                class="cardRec"
              >
                <div
                  style="
                    display: flex;
                    gap: 5px;
                    border-radius: 5px;
                    position: relative;
                  ">
                  <app-img
                  [class]="'postImg'"
                  [(meId)]= me._id
                  [(postId)]= dat._id
                  [(postUserId)]= userSelected._id
                  ></app-img>
                </div>
                <div
                  style="display: flex; gap: 0.5em; align-items: center"
                >
                  <div
                    style="
                      display: grid;
                      justify-content: center;
                      align-items: center;
                      background-color: var(--sidebar);
                      border-radius: 10px;
                      padding: 5px 1em;
                    "
                  >
                    <span style="text-align: center">Like</span>
                    <span style="text-align: center">{{dat?.likesCount}}</span>
                  </div>
                  <div
                    style="
                      display: grid;
                      justify-content: center;
                      align-items: center;
                      background-color: var(--sidebar);
                      border-radius: 10px;
                      padding: 5px 1em;
                    "
                  >
                    <span style="text-align: center">Comment</span>
                    <span style="text-align: center">{{dat?.commentsCount}}</span>
                  </div>
                  <div
                    style="
                      display: grid;
                      justify-content: center;
                      align-items: center;
                      background-color: var(--sidebar);
                      border-radius: 10px;
                      padding: 5px 1em;
                    "
                  >
                    <span style="text-align: center">Gift</span>
                    <span style="text-align: center">{{dat?.giftsCount}}</span>
                  </div>
                </div>
                <div
                (click)="add(dat)"
                  style="
                    margin-left: auto;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  <span
                    style="
                      background: var(--action-color);
                      padding: 3px 1em;
                      border-radius: 8px;
                    "
                  >
                    Add
                  </span>
                </div>
              </div>
            </div>
          </li>
        </ng-container>
      </div>
      <!-- list -->
    </div>
  </div>
</div>
</div>