<div class="projects-section settings-details-section">
  <div class="headerDetails1" style="display: flex">
    <div>
      <p
        style="font-size: 12px; font-weight: 800; line-height: 36px; margin: 0"
      >
        <span style="opacity: 0.5">Settings</span> • <span>Fees</span>
      </p>
      <p
        style="font-size: 28px; font-weight: 800; line-height: 36px; margin: 0"
      >
        Main Data
      </p>
    </div>

    <div style="margin-left: auto; display: flex; gap: 2em">
      <p
        class=""
        style="
          font-size: 15px;
          font-weight: 400;
          line-height: 21px;
          margin: 5px 1em 1em 0;
          display: flex;
          align-self: flex-end;
          padding: 5px 1.2em;
          color: white;
          border: 1px solid var(--action-color);
          border-radius: 10px;
        "
      >
        Back
      </p>
    </div>
  </div>
  <div
    style="
      display: grid;
      grid-template-rows: 4em 2em 1fr 4em;
      background-color: var(--sidebar);
      padding: 5px 1em;
      border-radius: 20px;
      height: 74vh;
      max-height: 950px;
    "
  >
    <div class="disp-flex a-i-center">
      <span class="no-wrap-1-line">Main Data</span>
      <div class="m-l-auto disp-flex"></div>
    </div>
    <div
      style="
        display: grid;
        grid-template-columns: 1.5fr  1fr 0.5fr;
        background-color: rgba(26, 37, 59, 0.5);
        align-items: center;
        margin-bottom: 5px;
        border-radius: 10px;
        gap: 1em;
      "
    >
      <span style="margin-left: 1em" class="no-wrap-1-line">Variable Name</span>
      <span style="text-align: center;" class="no-wrap-1-line">Value</span>
      <span style="text-align: center;" class="no-wrap-1-line">Edit</span>
    </div>
    <ul style="padding-inline-start: 0px; overflow: auto" class="">
      <!-- Skeleton -->
      <ng-container
      *ngIf="loading">
        <li
        *ngFor="let i of [].constructor(10)"
        class="skeleton1"
        style="
          display: grid;
          grid-template-columns: 1.5fr  1fr 0.5fr;
          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
        "
      >
        <div style="margin-left: 13px" class="disp-flex">
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 111px;
                height: 16px;
              "
            ></span>
          </div>
        </div>
       
        <div class="disp-grid">
          <span
            class="skeleton2"
            style="
              font-size: 14px;
              font-weight: 500;
              opacity: 0.7;
              width: 277px;
              height: 17px;
            "
          ></span>
        </div>
        <div style="justify-content: center" class="disp-grid">
          <span
            class="skeleton2"
            style="
              font-size: 14px;
              font-weight: 500;
              opacity: 0.7;
              width: 27px;
              height: 26px;
            "
          ></span>
        </div>
        </li>
      </ng-container>
      <!-- Skeleton ENd -->
      <ng-container
      *ngIf="!loading">
        <li
          *ngFor="let dat of data"
          style="
            display: grid;
            grid-template-columns: 1.5fr  1fr 0.5fr;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div class="disp-flex gap-05">
            <span style="margin-left: 1em" class="no-wrap-1-line">{{dat?.variableName}}</span>
          </div>
        
          <div class="disp-grid">
            <span  style="text-align: center;" class="no-wrap-1-line"
              >{{dat?.value}}</span>
          </div>

          <div style="justify-content: center" class="disp-flex">
            <img
              style="height: 20px; width: 20px"
              src="assets/icons/edit.svg"
              alt=""
            />
          </div>
        </li>
      </ng-container>
    </ul>
    <div class="list-number disp-flex" style="">
      <div
        class="showingInfoWrapper"
        style="
          margin: 0 0 0.75rem;
          display: flex !important;
          align-items: center;
          gap: 1em;
        ">
        <span class="showingInfo">        
          <span class="showingInfo">
          Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
          {{ pageNo * resultsPerPage - (resultsPerPage - data.length) }} of
          {{ count }}</span>
        </span>
        <div style="border: 1px solid gray; border-radius: 15px">
          <div>
            <input style="display: none" id="dropdownInput" type="checkbox" />
            <div
              style="
                display: flex;
                justify-content: center;
                height: 45px;
                width: 50px;
              "
              class="dropdown"
            >
              <select
                style="
                  display: flex;
                  background: transparent;
                  border: none;
                  color: white;
                  font-size: 18px;
                  width: 90%;
                  font-weight: 600;
                "
                (ngModelChange)="resultsPerPageChanged($event)"
                [(ngModel)]="resultsPerPage">
                <option class="optionStyle colorCancel">5</option>
                <option class="optionStyle colorCancel">10</option>
                <option class="optionStyle colorCancel">15</option>
                <option class="optionStyle colorCancel">20</option>
                <option class="optionStyle colorCancel">30</option>
                <option class="optionStyle colorCancel">50</option>
              </select>
              <label for="dropdownInput" class="overlay"></label>
            </div>
          </div>
        </div>
      </div>
      <nav>
        <ul class="pager">
          <li class="pager__item pager__item--prev">
            <a
              *ngIf="pageNo !== 1"
              (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Previous
            </a>
          </li>
          <li *ngIf="pageNo !== 1" class="pager__item">
            <a (click)="setPage(1)" class="pager__link">...</a>
          </li>
          <li
            *ngFor="
              let item of [].constructor(pageNoTotal) | slice : 0 : 5;
              let i = index
            "
            [ngClass]="pageNo + i === pageNo ? 'active' : ''"
            class="pager__item"
          >
            <a
              *ngIf="pageNo + i <= pageNoTotal"
              (click)="setPage(pageNo + i)"
              class="pager__link"
              >{{ pageNo + i }}</a
            >
          </li>
          <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
            <a (click)="setPage(pageNoTotal)" class="pager__link">...</a>
          </li>
          <li
            *ngIf="pageNo !== pageNoTotal"
            class="pager__item pager__item--next"
          >
            <a
              (click)="pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Next
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
  <div style="" class="disp-grid settingsDetailsWrapper">
    <div class="headerDetails" style="display: flex">
      <div>
        <p
          style="
            font-size: 12px;
            font-weight: 800;
            line-height: 36px;
            margin: 0;
          "
        >
          <span style="opacity: 0.5">Settings</span> • <span>Main Data</span>
        </p>
        <p
          style="
            font-size: 28px;
            font-weight: 800;
            line-height: 36px;
            margin: 0;
          "
        >
         Details
        </p>
      </div>
    </div>
    <div style="width: 100%; display: flex; flex-direction: column; gap: 2em">

      <div class="settingItemsWrapper" style="">
        <div style="display: flex; flex-direction: column; gap: 2em">
          <div
            style="
              display: grid;
              border: 1.2px solid var(--link-color-active-bg);
              border-radius: 10px;
            "
          >
            <div>
              <div
                style="
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  padding: 14px 22px 14px 22px;
                  justify-content: space-between;
                "
                href=""
              >
                <div style="width: 100%">
                  <p>Variable Name</p>
                  <input
                    [(ngModel)]="variableName"
                    class="fee-amount-input"
                    type="text"
                    placeholder="Fee Name"
                  />
                </div>
              </div>
            </div>

            <div 
            *ngIf="!createLoading"
            (click)="submit()"
            style="border-bottom: 1px solid var(--link-color-hover)">
              <div
                style="
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  padding: 14px 22px 14px 22px;
                  justify-content: space-between;
                "
              >
                <div style="width: 100%; display: flex; justify-content: end">
                  <span
                    style="
                      color: white;
                      font-weight: 500;
                      font-size: 13px;
                      background: var(--action-color);
                      padding: 10px 2.5em;
                      border-radius: 10px;
                    "
                    >Add</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
