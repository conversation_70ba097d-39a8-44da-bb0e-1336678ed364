import { ToastrService } from 'ngx-toastr';
import { User } from '../models/user';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, of, forkJoin } from 'rxjs';
import { take, map, tap, delay, switchMap } from 'rxjs/operators';
import { from, Observable } from 'rxjs';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';

const BACKEND_URL = environment.base_url + "/fiberBankTransaction/";

@Injectable({
  providedIn: 'root'
})
export class FiberBankTransactionService {

  constructor(
    private http: HttpClient,
    private router: Router,
    private toastrService: ToastrService
  ) { }

  create(
    total,
    reason,
    isSenderFiberBank,
    isReceiverFiberBank,
    createdBy
  ){
    let data = {
      total: total,
      reason: reason,
      isSenderFiberBank: isSenderFiberBank,
      isReceiverFiberBank: isReceiverFiberBank,
      createdBy : createdBy
    }
    return  this.http.post<any>( BACKEND_URL + 'create', data)
  }

}
