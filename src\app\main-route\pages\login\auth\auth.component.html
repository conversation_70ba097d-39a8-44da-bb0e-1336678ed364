<div class="app-container disp-flex j-c-center a-i-center">
  <div
    style="
      border-radius: 20px;
      box-shadow: 0px 0px 24px #568b96;
      overflow: hidden;
    "
    class="container"
  >
    <div class="screen">
      <div class="screen__content">
        <div style="padding: 2em" class="disp-flex a-i-center">
          <img src="../assets/icons/fiberLogoWhite.svg" alt="" />
        </div>
        <form  [formGroup]="loginForm"  class="login">
          <div class="login__field">
            <img
              style="height: 20px; width: 20px"
              class="login__icon"
              src="/assets/icons/userIcon.svg"
              alt=""
            />
            <input
              formControlName="email" 
              type="email"
              class="login__input"
              placeholder="Email"
            />
            <div class="errors-container" *ngIf="loginForm.get('email').invalid && (loginForm.get('email').dirty || loginForm.get('email').touched)">
              <ng-container  *ngIf="loginForm.get('email').errors.required">
                  <p style="color: red;">Email is required</p>
              </ng-container>
              <ng-container style="color: red;" *ngIf="loginForm.get('email').errors.invalidEmail">
                  <p style="color: red;"> Email is invalid </p>
              </ng-container>
            </div>
          </div>
          <div class="login__field">
            <img
              style="height: 20px; width: 20px"
              class="login__icon"
              src="/assets/icons/lock.svg"
              alt=""
            />
            <input
              type="{{typeInput}}"
              formControlName="password"
              type="password"
              class="login__input"
              placeholder="Password"
            />
            <div class="errors-container" *ngIf="loginForm.get('password').invalid && (loginForm.get('password').dirty || loginForm.get('password').touched)">
              <ng-container style="color: red;" *ngIf="loginForm.get('password').errors.required">
                  <p style="color: red;">Password is required </p>
              </ng-container>
          </div>
          </div>
          <button 
          (click)="login()"
          [disabled]="loginForm.invalid"
          class="button login__submit">
            <span style="color: black" class="button__text">Log In Now</span>
            <img
              style="height: 20px; width: 20px"
              class="button__icon"
              src="/assets/icons/login.svg"
              alt=""
            />
          </button>
        </form>
      </div>
      <div class="screen__background">
        <span
          class="screen__background__shape screen__background__shape4"
        ></span>
        <span
          class="screen__background__shape screen__background__shape3"
        ></span>
        <span
          class="screen__background__shape screen__background__shape2"
        ></span>
        <span
          class="screen__background__shape screen__background__shape1"
        ></span>
      </div>
    </div>
  </div>
</div>
