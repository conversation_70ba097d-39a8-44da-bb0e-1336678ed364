
.promImgS{
  width: 40%; 
  object-fit: contain
}
.loader:before,
.loader:after {   
  border-radius: 50%;
  content: '';
  display: block;
  height: 20px;  
  width: 20px;
}
.loader:before {
  animation: ball1 1s infinite;  
  background-color: #cb2025;
  box-shadow: 30px 0 0 #f8b334;
  margin-bottom: 10px;
}
.loader:after {
  animation: ball2 1s infinite; 
  background-color: #00a096;
  box-shadow: 30px 0 0 #97bf0d;
}

/* @media print {
    .host {
      display: none;
    }
}


@media print {
    app-root > * { display: none; }
    app-root app-faktura { display: block; }

    .page-wrapper{
      padding: 0px 0px 0px 0px !important;
      margin: 0 !important;  
      width: 100%;
    }
    .page-body-wrapper{
      padding: 0px 0px 0px 0px !important;
      margin: 0 !important;  
      width: 100%;
    }
    .page-body{
      padding: 0px 0px 0px 0px !important;
      margin: 0 !important;  
      width: 100%;
    }

} */



