import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/services/auth.service';
import { GiftService } from 'src/app/shared/services/gift.service';
import { NotificationNativeService } from 'src/app/shared/services/notification-native.service';
import { environment } from 'src/environments/environment';
const BACKEND_Image_URL = environment.imageUrl + '/post/getImage/';

@Component({
  selector: 'app-list-notification',
  templateUrl: './list-notification.component.html',
  styleUrls: ['./list-notification.component.css']
})
export class ListNotificationComponent implements OnInit {

  public pageNo: number = 1;
  public pageNoTotal: number = 1;
  count: number = 0;
  resultsPerPage: number = 7;
  data: any[] = [];
  loading = false;

  backendImageUrl = BACKEND_Image_URL

  me = null

  timeout: any = null;
  resultSearch: any = null;
  searchText = '';

  constructor(
    private toastrService: ToastrService,
    private giftService: GiftService,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private notificationNativeService: NotificationNativeService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loading = true;
    this.activatedRoute.queryParams.subscribe((params) => {
      this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;
    });
    this.getAll();
  }

  redirectTo(uri:string){
    this.router.navigateByUrl('/en/'+ uri)
      .then(() =>{
      });
  }

  public setPage(page: number) {
    this.data = [];
    this.loading = true;
    this.router
      .navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: page },
        queryParamsHandling: 'merge', // preserve the existing query params in the route
        skipLocationChange: false, // do trigger navigation
      })
      .finally(() => {
          if(this.searchText === ''){
            this.getAll();
          }else{
            // this.getAllResultSearch()
          }
      });
  }

  resultsPerPageChanged(event){
    this.loading = true;
    this.count =  1
    this.pageNoTotal = 1
    this.data = []
    this.resultsPerPage = Number(event)
    this.setPage(1)
  }

  getAll() {
    this.notificationNativeService.getAll(this.pageNo, this.resultsPerPage).subscribe(
      async (result) => {
        if (result.status == 'success') {
          this.count = result.data.count;
          this.data = result.data.data;
          this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
          this.loading = false;
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }

}
