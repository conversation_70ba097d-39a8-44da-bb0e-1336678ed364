import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';

const BACKEND_URL = environment.base_url + "/ministryOfFinance/";

@Injectable({
  providedIn: 'root'
})
export class MinistryOfFinanceService {

  
  constructor(
    private http: HttpClient
  ) { } 

  
  create(
    data
  ){
    return  this.http.post<any>( BACKEND_URL, {data:data})
  }

  getAll(page,resultsPerPage){
    let data = {
      page : page,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL+'getAll',data)
  }

  search(searchText,_id,pageNumber,resultsPerPage){
    let data = {
      searchText:searchText,
      userId: _id,
      page : pageNumber,
      resultsPerPage: resultsPerPage,
    }
    return this.http.post<any>(BACKEND_URL + 'search',data)
  }

}
