import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { io, Socket } from 'socket.io-client';

const BACKEND_URL = environment.socketEndpoint;
const BACKEND_URL_Chat = environment.socketChatEndpoint;

@Injectable({
  providedIn: 'root'
})
export class SocketService {

  constructor() { }

  setupSocketConnection() {
    return io(BACKEND_URL);
  }

  setupChatSocketConnection() {
    return io(BACKEND_URL_Chat);
  }

}
