import { Component, Input, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { AuthService } from 'src/app/shared/services/auth.service';
import { Router, ActivatedRoute, Params, RoutesRecognized } from '@angular/router';
import { NavigationService } from 'src/app/shared/services/navigation.service';
import { LoadJsService } from 'src/app/shared/services/load-js.service';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-page-main-header',
  templateUrl: './page-main-header.component.html',
  styleUrls: [
    './page-main-header.component.css']
})
export class PageMainHeaderComponent implements OnInit {
  user: User;
  userIsAuthenticated = false

  delay = ms => new Promise(res => setTimeout(res, ms));

  languages: any[] = []
  selectedLang = {
    code : "en",
    nativeName : ""
  }
  lang = 'en';
  @Input() params_lang_code = 'en'
  loading = false
  profileImage = null

  displayDropDown = false

  constructor(
    private domSanitizer: DomSanitizer,
    public navigation: NavigationService,
    private userService: AuthService,
    private toastrService: ToastrService,
    public loadJsService: LoadJsService,
    private router: Router) {

    this.userService.user.pipe().subscribe(appUser => {
      this.user = appUser
      if(this.user != null){
        // if(this.user?.photo?.data){
        //   this.profileImage = this.domSanitizer.bypassSecurityTrustResourceUrl('data:image/jpg;base64,'+ this.user.photo?.data)
        // }
        this.userIsAuthenticated = true

  this.loading = false
  this.loadJsService.loadScripts()
      }
    })

  }
  ngOnInit(): void {



  }


refreshComponent(){
    let route  = this.router.url
    this.router.navigate(['/'+this.lang+'/loading']).then(async ()=>{
      await this.delay(1020);
      this.router.navigate([route])
  });
}


changeLang(lang){
    this.lang = 'en'
    // this.router.navigateByUrl(this.router.url.replace( this.params_lang_code, this.selectedLang.code));
   
}
redirectTo(uri:string){
  this.router.navigateByUrl(this.lang+ uri)
    .then(() =>{
      this.loadJsService.removeScripts()
    });
}

 signOut() {
  this.userService.signOut();
  this.redirectTo('/auth')
  }

}
