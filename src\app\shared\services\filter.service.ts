import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { take } from 'rxjs';
import { TranslatePipe } from '../pipes/translate.pipe';
import { UserService } from './user.service';

@Injectable({
  providedIn: 'root'
})
export class FilterService {

  constructor(
    public userServices: UserService,
    private toastrService: ToastrService
  ) { }

  async prepareUser(){
    let users: any[] = []
    await this.getUsers().then((data:any)=>{users = data})
    users = users.map(   item => (
      {   
        id: item._id,
        value:  item.name + ' ' +item.surname,
      }
    ));

    return users
    
  }

getUsers(){
  return new Promise(resolve=>{
    this.userServices.getUsers().pipe(
       take(1) //useful if you need the data once and don't want to manually cancel the subscription again
     )
     .subscribe( 
      result =>{
        if(result.status == "success"){
          resolve(result.data.data);
        }else{
          resolve([]);
        }
      },
      respond_error => {

        this.toastrService.error(
          respond_error?.error.message
            , respond_error?.name);   
          
        resolve([]);
       }
    )
  })

}




}
