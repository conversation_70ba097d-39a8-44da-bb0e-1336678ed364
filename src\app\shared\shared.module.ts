import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReplacePipe } from './pipes/replace.pipe';
import { TranslatePipe } from './pipes/translate.pipe';
import { SearchFilterPipe } from './pipes/search-filter.pipe';
import { FilterPipe } from './pipes/filter.pipe';
import { HumanizePipe } from './pipes/humanize.pipe';

@NgModule({
  declarations: [
    ReplacePipe,
    TranslatePipe,
    SearchFilterPipe,
    FilterPipe,
    HumanizePipe,
  ],
  imports: [
    CommonModule
  ],
  exports:[
    HumanizePipe,
    TranslatePipe,
    SearchFilterPipe,
    ReplacePipe,
    FilterPipe
  ]
})
export class SharedModule { }
