const mongoose = require('mongoose');

const fiberBankTransactionSchema = new mongoose.Schema({
  senderId:{
    type: mongoose.ObjectId,
    ref: 'User'
  },  
  receiverId:{
    type: mongoose.ObjectId,
    ref: 'User'
  },  
  total:{
    type: Number,
    default: 0
  },
  reason: {
    type: String,
    trim: true,
  },
  isSenderFiberBank: {
    type: Boolean,
    default: false
  },
  isReceiverFiberBank: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: mongoose.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});


const FiberBankTransaction = mongoose.model('FiberBankTransaction', fiberBankTransactionSchema);

module.exports = FiberBankTransaction;