<div style="overflow: auto" class="projects-section">
  <div
    style="
      /* display: none !important; */
      display: grid;
      grid-template-rows: 4em 2em 1fr 4em;
      background-color: var(--sidebar);
      padding: 5px 1em;
      border-radius: 20px;
      height: fit-content;
      max-height: 95%;
    "
  >
    <div class="disp-flex a-i-center">
      <span class="no-wrap-1-line">Recent Transactions</span>
      <div class="m-l-auto disp-flex">
       
        <div (click)="openAddRoleModal(addRoleContent)" class="plusButton">
            +
          </div>
      </div>
    </div>
    <div
      style="
        display: grid;
        grid-template-columns: 1.5fr 1fr 1fr 0.3fr;
        padding: 0 1em;
        background-color: rgba(26, 37, 59, 0.5);
        align-items: center;
        margin-bottom: 5px;
        border-radius: 10px;
        gap: 1em;
      "
    >
      <span style="margin-left: 1em" class=""
        >Roles name</span
      >
      <span style="text-align: center" class=""
        >Users in Role</span
      >
      <span style="text-align: center" class=""
        >Permissions</span
      >
      <span style="text-align: center" class=""> Action </span>
    </div>
    <ul style="padding-inline-start: 0px; overflow: auto" class="">
      <!-- <ng-container *ngIf="startLoading"> -->
      <li
        *ngFor="let i of [].constructor(1)"
        class="skeleton1"
        style="
          display: grid;
          grid-template-columns: 1.5fr 1fr 1fr 0.3fr;
        padding: 0 1em;

          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
        "
      >
       
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 177px;
                height: 17px;
                margin-bottom: 4px;
              "
            ></span>
           
          </div>
        <div class="disp-grid j-c-center">
          <span
            class="skeleton2"
            style="
              font-size: 14px;
              font-weight: 500;
              opacity: 0.7;
              width: 77px;
              height: 17px;
              margin-bottom: 4px;
            "
          ></span>
        
        </div>
        <div class="disp-grid j-c-center">
            <span
            class="skeleton2"
            style="
              font-size: 14px;
              font-weight: 500;
              opacity: 0.7;
              width: 77px;
              height: 17px;
              margin-bottom: 4px;
            "
          ></span>
        
        </div>
        
        <div class="disp-grid j-c-center">
            <span
            class="skeleton2"
            style="
              font-size: 14px;
              font-weight: 500;
              opacity: 0.7;
              width: 28px;
              height: 17px;
              margin-bottom: 4px;
            "
          ></span>
        </div>
      </li>
      <!-- </ng-container> -->
      <!-- Skeleton ENd -->

      <!-- List empty  -->
      <!-- <div
          *ngIf="data?.length === 0 && !startLoading"
            style="
              position: relative;
              display: flex;
              justify-content: center;
              align-items: center;
              flex-direction: column;
            "
          >
            <img
              style="width: 70%; max-width: 299px"
              src="/assets/icons/animatedIconsTable/list_empty_transaction_infinite.svg"
              alt=""
            />
            <p style="font-size: 16px">List is empty</p>
          </div> -->
      <!-- List empty end -->

      <!-- List empty  -->
      <!-- *ngIf="data?.length === 0 && !startLoading" -->
      <!-- <div
          style="
            position: relative;
            min-height: 365px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            height: 50%;
          "
        >
          <div>
            <img
              style="width: 70%; max-width: 299px"
              src="../../../../../assets/icons/animatedIconsTable/list_empty_transaction_infinite.svg"
              alt=""
            />
            <p style="font-size: 16px">Transaction list is empty</p>
          </div>
        </div> -->

      <!-- List empty end -->
      <!-- *ngFor="
          let dat of data | searchFilter : searchForm.value.search;
          let i = index
        " -->
      <li
        *ngFor="let i of [].constructor(20)"
        style="
          display: grid;
          grid-template-columns: 1.5fr 1fr 1fr 0.3fr;
          padding: 0 1em;
          align-items: center;
          min-height: 4em;
          gap: 1em;
          border-bottom: 1px solid rgba(26, 37, 59, 0.5);
        "
      >
        <div class="disp-grid">
          <span class="no-wrap-1-line"> Administrator </span>
        </div>
        <div class="disp-grid">
          <span style="text-align: center" class="no-wrap-1-line">20</span>
        </div>
        <div class="disp-grid">
          <span style="text-align: center" class="no-wrap-1-line"
            >15</span
          >
        </div>

        <div style="gap: 5px" class="disp-flex j-c-center">
          <!-- (click)="selectTransaction(dat); openOptionsModal(optionsContent)" -->
          <span
          (click)="openDelRoleModal(delRoleContent)"

            style="
              height: 20px;
              width: 20px;
              border-radius: 50%;
              background-color: var(--app-bg);
              display: flex;
              align-items: center;
              justify-content: center;
            "
            >x</span
          >
          <span class="no-wrap-1-line">•••</span>
        </div>
      </li>
    </ul>
    <!-- <div class="list-number disp-flex" style="">
        <div
          class="showingInfoWrapper"
          style="
            margin: 0 0 0.75rem;
            display: flex !important;
            align-items: center;
            gap: 1em;
          "
        >
          <span class="showingInfo"
            >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
            {{ pageNo * resultsPerPage - (resultsPerPage - data.length) }} of
            {{ count }}</span
          >
          <div style="border: 1px solid gray; border-radius: 15px">
            <div>
              <input style="display: none" id="dropdownInput" type="checkbox" />
              <div
                style="
                  display: flex;
                  justify-content: center;
                  height: 45px;
                  width: 50px;
                "
                class="dropdown"
              >
                <select
                  style="
                    display: flex;
                    background: transparent;
                    border: none;
                    color: white;
                    font-size: 18px;
                    width: 90%;
                    font-weight: 600;
                  "
                  (ngModelChange)="resultsPerPageChanged($event)"
                  [(ngModel)]="resultsPerPage"
                >
                  <option class="optionStyle colorCancel">7</option>
                  <option class="optionStyle colorCancel">10</option>
                  <option class="optionStyle colorCancel">15</option>
                  <option class="optionStyle colorCancel">20</option>
                  <option class="optionStyle colorCancel">30</option>
                  <option class="optionStyle colorCancel">50</option>
                </select>
                <label for="dropdownInput" class="overlay"></label>
              </div>
            </div>
          </div>
        </div>
        <nav>
          <ul class="pager">
            <li class="pager__item pager__item--prev">
              <a
                *ngIf="pageNo !== 1"
                (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
                style="width: fit-content !important; padding: 0 10px"
                class="pager__link"
              >
                Previous
              </a>
            </li>
            <li *ngIf="pageNo !== 1" class="pager__item">
              <a (click)="setPage(1)" class="pager__link">...</a>
            </li>
            <li
              *ngFor="
                let item of [].constructor(pageNoTotal) | slice : 0 : 5;
                let i = index
              "
              [ngClass]="pageNo + i === pageNo ? 'active' : ''"
              class="pager__item"
            >
              <a
                *ngIf="pageNo + i <= pageNoTotal"
                (click)="setPage(pageNo + i)"
                class="pager__link"
                >{{ pageNo + i }}</a
              >
            </li>
            <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
              <a (click)="setPage(pageNoTotal)" class="pager__link">...</a>
            </li>
            <li
              *ngIf="pageNo !== pageNoTotal"
              class="pager__item pager__item--next"
            >
              <a
                (click)="pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''"
                style="width: fit-content !important; padding: 0 10px"
                class="pager__link"
              >
                Next
              </a>
            </li>
          </ul>
        </nav>
      </div> -->
  </div>
  <ng-template #addRoleContent let-modal>
    <div
      style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1"
    >
      <div class="modal-body">
        <!-- header -->
        <div
          style="
            display: flex;
            justify-content: center;
            width: 100%;
            position: relative;
          "
        >
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; position: absolute; right: 0"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->

        <div style="margin: 1em 0">
          <div
            style="
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
              gap: 2rem;
            "
          >
            <div
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
              "
            >
              <span style="color: white; font-weight: 600; font-size: 17px"
                >Create New Role</span
              >
            </div>

            <div class="creditsInputWrapper">
              <input
                formControlName="creditsAmount"
                class="creditsInput"
                type="text"
                placeholder="Role name"
              />
            </div>

            <div
              style="
                width: 50%;
                display: flex;
                justify-content: space-between;
                gap: 1em;
              "
            >
              <div
                (click)="modal.dismiss('Cross click')"
                class="modalButtonsCancel"
              >
                Cancel
              </div>

              <div class="modalButtonsDelete">Create</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
  <ng-template #delRoleContent let-modal>
    <div
      style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1"
    >
      <div class="modal-body">
        <!-- header -->
        <div
          style="
            display: flex;
            justify-content: center;
            width: 100%;
            position: relative;
          "
        >
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; position: absolute; right: 0"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->

        <div style="margin: 1em 0">
          <div
            style="
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
              gap: 2rem;
            "
          >
            <span style="color: white; font-weight: 600; font-size: 17px"
              >Are you sure ou want to delete role</span
            >
            <span style="color: white; font-weight: 800; font-size: 20px"
              >Basic</span
            >

            <div
              style="
                width: 50%;
                display: flex;
                justify-content: space-between;
                gap: 1em;
              "
            >
              <div
                (click)="modal.dismiss('Cross click')"
                class="modalButtonsCancel"
              >
                Cancel
              </div>

              <div class="modalButtonsDelete">Delete</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
</div>
