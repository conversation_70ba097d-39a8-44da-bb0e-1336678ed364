const mongoose = require('mongoose');

const updateSchema = new mongoose.Schema(
  {
    current:{
        type: String,
        trim: true,
    },
    description:{
      type: String,
      trim: true,
    },
    thumbnailImageId:{
        type: String,
        trim: true,
    },
    enabled:{
      type: Boolean,
      default: true
    },
    appStore:{
      type: Boolean,
      default: false
    },
    googlePlay:{
      type: Boolean,
      default: false
    },
    mandatoryToUpdate:{
      type: Boolean,
      default: false
    },


    msg: {
        title:{
            type: String,
            trim: true,
        },
        msg:{
            type: String,
            trim: true,
        },
        btn:{
            type: String,
            trim: true,
        },
      },
      majorMsg: {
        title:{
            type: String,
            trim: true,
        },
        msg:{
            type: String,
            trim: true,
        },
        btn:{
            type: String,
            trim: true,
        },
      },
      minorMsg: {
        title:{
            type: String,
            trim: true,
        },
        msg:{
            type: String,
            trim: true,
        },
        btn:{
            type: String,
            trim: true,
        },
      },
    user: {
        type: mongoose.Schema.ObjectId,
        ref: 'User',
    },
    createdAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: {
      type: Date,
      default: Date.now
    }

  }

);

updateSchema.pre('save', async function( next) {
    // await this.populate({
    //   path: 'user',
    // })
    next();
  });
  
  
  
  updateSchema.pre(/^find/, function(next) {
    // this.populate({
    //   path: 'user',
    // });
    next();
  })

const Update = mongoose.model('Update', updateSchema);

module.exports = Update;
