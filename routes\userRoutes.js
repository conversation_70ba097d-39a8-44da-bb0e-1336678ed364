const express = require("express");
const userController = require("./../controllers/userController");
const authController = require("./../controllers/authController");

const router = express.Router();
var multipart = require("connect-multiparty");

var multipartMiddleware = multipart();

router.post("/signup", authController.signup);
router.post("/login", authController.login);
router.get("/logout", authController.logout);

router.post("/forgotPassword", authController.forgotPassword);
router.patch("/resetPassword/:token", authController.resetPassword);

// Protect all routes after this middleware
// router.use(authController.protect);

router.post("/getUserFiltered", userController.getUserFiltered);
router.post("/getUserFilteredData", userController.getUserFilteredData);

router.post("/searchWithFilters", userController.searchWithFilters);

router.post("/searchUser", userController.searchUser);

router.post("/getUserUntrusted", userController.getUserUntrusted);
router.post("/getUserVerified", userController.getUserVerified);

router.patch("/updateMyPassword", authController.updatePassword);
router.get("/me", userController.getMe, userController.getUser);
router.patch(
  "/updateMe",
  multipartMiddleware,
  userController.resizePhoto,
  userController.updateMe
);
router.delete("/deleteMe", userController.deleteMe);

// router.use(authController.restrictTo('admin'));
router.route("/getAll").post(userController.getAll);

router.route("/").get(userController.getAllUsers);

router.route("/getAllBankAccounts").get(userController.getAllBankAccounts);

router.route("/updateUserRole/:id").patch(userController.updateUserRole);

router.route("/createUser").post(userController.createUser);

router
  .route("/changeActiveStatus/:id")
  .patch(userController.changeActiveStatus);

router
  .route("/:id")
  .get(userController.getUser)
  .patch(userController.updateUser)
  .delete(userController.deleteUser);

router.route("/searchUser").post(userController.searchUser);

router.route("/search").post(userController.search);

router.route("/findByIdAndUpdate").post(userController.findByIdAndUpdate);

router.post("/getProfileBalance/:id", userController.getProfileBalance);

module.exports = router;
