const mongoose = require('mongoose');
const { MongooseFindByReference } = require('mongoose-find-by-reference');

const transactionSchema = new mongoose.Schema({
  senderId:{
    type: mongoose.Types.ObjectId,
    ref: 'User'
  },  
  receiverId:{
    type: mongoose.Types.ObjectId,
    ref: 'User'
  },  
  total:{
    type: Number,
    default: 0
  },
  reason: {
    type: String,
    trim: true,
  },
  fiberFee:{
    type: Number,
    default: 0
  },
  isBuyingGiftTransaction: {
    type: Boolean,
    default: false
  },
  isSellingGiftTransaction: {
    type: Boolean,
    default: false
  },
  isPayPerViewTransaction: {
    type: Boolean,
    default: false
  },
  isPromotionSharingTransaction: {
    type: Boolean,
    default: false
  },
  isTransferTransaction: {
    type: Boolean,
    default: false
  },
  isSenderFiberBank: {
    type: Boolean,
    default: false
  },
  isReceiverFiberBank: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});
transactionSchema.pre(/^find/, function(next) {
  // this.populate({
  // path: 'senderId',
  // });
  // this.populate({
  //   path: 'receiverId',
  //   });
  next();
})
transactionSchema.plugin(MongooseFindByReference);

const Transaction = mongoose.model('Transaction', transactionSchema);

module.exports = Transaction;