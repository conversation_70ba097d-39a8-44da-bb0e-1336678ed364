const express = require('express');
const updateController = require('./../controllers/updateController');

const router = express.Router();

router
.route('/')
.get(updateController.getAll)
.post(
    updateController.create
);

router
.route('/getAllVersions')
.post(
    updateController.getAllVersions
);

router
.route('/getData/:id')
.get(
    updateController.getData
);


router
.route('/edit/:id')
.post(
    updateController.edit
);



module.exports = router;