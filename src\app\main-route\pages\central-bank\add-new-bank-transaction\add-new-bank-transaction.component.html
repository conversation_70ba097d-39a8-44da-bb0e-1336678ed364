                <!-- Container-fluid starts-->
                <div class="container-fluid" >
                    <div class="page-header">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="page-header-left">
                                    <h3> Central Bank	
                                        <small> Fiber Admin Panel </small>
                                    </h3>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <ol class="breadcrumb pull-right">
                                    <li class="breadcrumb-item"> Central Bank </li>
                                    <li class="breadcrumb-item active"> New Transaction </li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Container-fluid Ends-->

                <!-- Container-fluid starts-->
                <div class="container-fluid" *ngIf="!loading">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="card tab2-card">
                                <div class="card-body">
                                    <div class="tab-content" id="myTabContent">
                                        <div class="tab-pane fade active show" id="account" role="tabpanel"
                                            aria-labelledby="account-tab">
                                            <form [formGroup]="form" class="needs-validation user-add">
                                                <h4> Central Bank Total Amount  - {{centralBankTotalAmount}} credits</h4>
                                                <h4 *ngIf="form?.value?.type === 'tax-collection'">
                                                    The Minister of finance total amount - {{theMinisterOfFinanceTotalaAmount}} credits</h4>
                                                <div class="form-group row">
                                                    <label 
                                                        class="col-xl-3 col-md-4"><span>*</span>Total of transaction credits cost </label>
                                                    <div class="col-xl-8 col-md-7">
                                                        <input 
                                                            formControlName="total" 
                                                            class="form-control" type="number">
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label  class="col-xl-3 col-md-4"><span>*</span>
                                                         Type </label>
                                                    <div class="col-xl-8 col-md-7">
                                                        <select 
                                                        class="form-control" formControlName="type"
                                                        (ngModelChange)="onChange($event)">
                                                            <option *ngFor="let type of exchangeTypes" [ngValue]="type">
                                                                 <ng-container *ngIf="type === 'loan-to-central-bank'">
                                                                    Loan to Central Bank
                                                                 </ng-container>
                                                                 <ng-container 
                                                                 *ngIf="type === 'tax-collection'">
                                                                    Tax collection
                                                                </ng-container>
                                                            </option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    <div class="tab-group pull-right">
                                        <button  (click)="create()" class="btn btn-primary d-block tab"> Save </button>
                                        <a [routerLink]="['/'+lang +'/list-of-central-bank-transactions']" class="tab">
                                            <button type="button" class="btn btn-primary d-block"> Cancel </button>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Container-fluid Ends-->