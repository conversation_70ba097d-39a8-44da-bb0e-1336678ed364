import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { BloggerService } from 'src/app/shared/services/blogger.service';

@Component({
  selector: 'app-select-post-img',
  templateUrl: './select-post-img.component.html',
  styleUrls: ['./select-post-img.component.css']
})
export class SelectPostImgComponent implements OnInit {

  @Input() userSelected: User;
  @Input() me: User;
  @Output() postSelected: EventEmitter<any> = new EventEmitter();
  @Output() closeModal: EventEmitter<any> = new EventEmitter();
  data = [];

  pageNo = 1
  resultsPerPage = 10
  loading = false;

  constructor(
    private toastrService: ToastrService,
    private bloggerService: BloggerService
  ) { }

  ngOnInit(): void {
    this.getAllPostUserId()
  }

  getAllPostUserId(){
    this.loading = true;
    this.bloggerService.getAllPostUserId(this.pageNo, this.resultsPerPage,this.userSelected?._id).subscribe(
      async (result) => {
        if (result.status == 'success') {
          this.data = result.data.data;
          this.loading = false;
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }

  modalDismiss(){
    this.closeModal.emit()
  }

  add(post){
    post['user'] = this.userSelected._id
    this.postSelected.emit(post)
  }
}
