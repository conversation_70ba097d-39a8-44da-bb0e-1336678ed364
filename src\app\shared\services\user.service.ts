import { ToastrService } from 'ngx-toastr';
import { User } from '../models/user';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, of, forkJoin } from 'rxjs';
import { take, map, tap, delay, switchMap } from 'rxjs/operators';
import { from, Observable } from 'rxjs';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';

const BACKEND_URL = environment.base_url + "/users/";

@Injectable({
  providedIn: 'root'
})
export class UserService {

  private sentUserFiltered = new BehaviorSubject(null);
  sentUserFilteredObservable = this.sentUserFiltered.asObservable();

  private sentFinalUserFiltered = new BehaviorSubject(null);
  sentFinalUserFilteredObservable = this.sentFinalUserFiltered.asObservable();

  constructor(
    private http: HttpClient,
    private router: Router,
    private toastrService: ToastrService
  ) { }

  updateMe(
    _id:any,
    name: string,
    surname: string
  ){
    let uploadData = new FormData();

    
    uploadData.append('_id', JSON.stringify(_id));
    uploadData.append('name', JSON.stringify(name));
    uploadData.append('surname', JSON.stringify(surname));


    return  this.http.patch<any>( BACKEND_URL + 'updateMe', uploadData)
  }

  updateUserRole(
    _id,
    role
  ){  

    let data = {
      "role": role,
    }

    return  this.http.patch<any>( BACKEND_URL + 'updateUserRole/'+_id ,data)
  }

  changeActiveStatus(
    _id,
    status
  ){  

    let data = {
      "_id": _id,
      "active": status
    }

    return  this.http.patch<any>( BACKEND_URL + 'changeActiveStatus/'+_id ,data)
  }

  getUserFiltered(
    filter
  ){  

    // let data = {
    //   "_id": _id,
    //   "active": status
    // }

    return  this.http.post<any>( BACKEND_URL + 'getUserFiltered' ,filter)
  }
  changeUserFiltered(allUsers) {
    this.sentUserFiltered.next(allUsers)
  }
  changeFinalUserFiltered(allUsers) {
    this.sentFinalUserFiltered.next(allUsers)
  }
  // searchUser(
  //   searchText){
  //     let data = {
  //       "searchText": searchText
  //     }
  //   return this.http.post<any>(BACKEND_URL+'searchUser',data)
  // }

  getUserFilteredData(
    filter,page,resultsPerPage){

    filter["page"] = page
    filter["resultsPerPage"] = resultsPerPage
    return this.http.post<any>(BACKEND_URL+'getUserFilteredData',filter)
  }
  searchWithFilters(
    filters,
    searchText,
    _id,
    pageNumber,
    resultsPerPage){
    let data = {
      filters: filters,
      userId: _id,
      searchText : searchText,
      page : pageNumber,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL + 'searchWithFilters',data)
  }

  getUserById(_id:string){
    return this.http
    .get<any>(
      BACKEND_URL + _id
    )
  }

  getUsers(){
    // let data = {
    //   "user": user
    // }
    return this.http.get<any>(BACKEND_URL)
  }

  getAllBankAccounts(){
    // let data = {
    //   "user": user
    // }
    return this.http.get<any>(BACKEND_URL+'getAllBankAccounts')
  }

  getUserUntrusted(
    page,resultsPerPage
  ){
    let data = {
      page: page,
      resultsPerPage: resultsPerPage
    }
    return  this.http.post<any>( BACKEND_URL + 'getUserUntrusted',data)
  }


  getUserVerified(
    page,resultsPerPage
  ){
    let data = {
      page: page,
      resultsPerPage: resultsPerPage
    }
    return  this.http.post<any>( BACKEND_URL + 'getUserVerified',data)
  }


  updateUser(data,userId){
    return this.http.patch<any>(BACKEND_URL+userId,data)
  }

  searchUser(searchText,_id,pageNumber){
    let data = {
      userId: _id,
      searchText : searchText,
      page : pageNumber,
      resultsPerPage: 10
    }
    return this.http.post<any>(BACKEND_URL + 'searchUser',data)
  }

  getAll(page,resultsPerPage,status){
    let data = {
      status:status,
      page : page,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL+'getAll',data)
  }


  search(searchText,_id,pageNumber,resultsPerPage,status){
    let data = {
      searchText:searchText,
      userId: _id,
      page : pageNumber,
      resultsPerPage: resultsPerPage,
      status:status
    }
    return this.http.post<any>(BACKEND_URL + 'search',data)
  }


  findByIdAndUpdate(userId,updateData){
    let data = {
      userId: userId,
      updateData: updateData
    }
    return this.http.post<any>(BACKEND_URL + 'findByIdAndUpdate',data)
  }

  getProfileBalance(_id:string){
    return this.http
    .post<any>(
      BACKEND_URL + 'getProfileBalance/' + _id,{meId:_id }
    )
  }
}
