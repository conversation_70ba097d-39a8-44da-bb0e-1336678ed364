export class User {
  constructor(
    public _id: string,
    public name: string,
    public surname: string,
    public bio: string,
    public email: string,
    public role: string,
    public photoProfile: any,
    public photoCover: any,
    public photoColor: string,
    public coins: any,
    private _token: string,
    public tokenExpirationDate: Date
  ) {}

  get token() {
    if (!this.tokenExpirationDate || this.tokenExpirationDate <= new Date()) {
      return null;
    }
    return this._token;
  }

  get tokenDuration() {
    if (!this.token) {
      return 0;
    }
    return this.tokenExpirationDate.getTime();
  }
}
