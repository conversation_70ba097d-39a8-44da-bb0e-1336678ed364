<div class="projects-section">
  <div class="tiles">
    <article class="tile">
      <p class="tile-header">OutFlow Credits</p>
      <div
        style="
          justify-content: space-between;
          justify-content: space-between;
          margin: 0 3em 0 0;
        "
        class="disp-flex a-i-center"
      >
        <span style="color: #e3e3e3; font-size: 30px; font-weight: 700"
          >0</span
        >
        <div style="scale: 1.5" class="chart-svg">
          <svg viewBox="0 0 36 36" class="circular-chart blue">
            <path
              class="circle-bg"
              d="M18 2.0845
          a 15.9155 15.9155 0 0 1 0 31.831
          a 15.9155 15.9155 0 0 1 0 -31.831"
            ></path>
            <path
              class="circle"
              stroke-dasharray="0, 100"
              d="M18 2.0845
          a 15.9155 15.9155 0 0 1 0 31.831
          a 15.9155 15.9155 0 0 1 0 -31.831"
            ></path>
            <text x="18" y="20.35" class="percentage">0%</text>
          </svg>
        </div>
      </div>
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: start;
          gap: 1em;
        "
      >
        <img
          class="downAnimation"
          src="../assets/icons/downwardsanimated.svg"
          alt="arrowUp"
        />
        <div style="display: grid">
          <p
            style="
              margin: 0;
              color: #e3e3e3;
              font-size: 19px;
              line-height: 1;
              display: flex;
              align-items: center;
              font-weight: 500;
            "
          >
            0
          </p>
          <p
            style="
              margin: 0;
              color: #e3e3e3;
              font-size: 14px;
              display: flex;
              font-weight: 500;
              opacity: 0.8;
            "
          >
            this month
          </p>
        </div>
      </div>
    </article>
    <article class="tile">
      <p style="color: #e3e3e3; font-size: 19px; font-weight: 600; margin: 5px">
        Inflow Credits
      </p>
      <div
        style="justify-content: space-between; margin: 0 3em 0 0"
        class="disp-flex a-i-center"
      >
        <span style="color: #e3e3e3; font-size: 30px; font-weight: 700"
          >0</span
        >
        <div style="scale: 1.5" class="chart-svg">
          <svg viewBox="0 0 36 36" class="circular-chart orange">
            <path
              class="circle-bg"
              d="M18 2.0845
          a 15.9155 15.9155 0 0 1 0 31.831
          a 15.9155 15.9155 0 0 1 0 -31.831"
            ></path>
            <path
              class="circle"
              stroke-dasharray="0, 100"
              d="M18 2.0845
          a 15.9155 15.9155 0 0 1 0 31.831
          a 15.9155 15.9155 0 0 1 0 -31.831"
            ></path>
            <text x="18" y="20.35" class="percentage">0%</text>
          </svg>
        </div>
      </div>
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: start;
          gap: 1em;
        "
      >
        <img
          class="upAnimation"
          src="../assets/icons/upwardsanimated.svg"
          alt="arrowUp"
        />
        <div style="display: grid">
          <p
            style="
              margin: 0;
              color: #e3e3e3;
              font-size: 19px;
              line-height: 1;
              display: flex;
              align-items: center;
              font-weight: 500;
            "
          >
            0
          </p>
          <p
            style="
              margin: 0;
              color: #e3e3e3;
              font-size: 14px;
              display: flex;
              font-weight: 500;
              opacity: 0.8;
            "
          >
            this month
          </p>
        </div>
      </div>
    </article>
  </div>
  <div
    style="
      height: 100%;
      width: 100%;
      display: none;
      align-items: center;
      justify-content: center;
    "
  >
    <p>There is no Transactions</p>
  </div>
  <div
    style="
      /* display: none !important; */
      display: grid;
      grid-template-rows: 4em 2em 1fr 4em;
      background-color: var(--sidebar);
      padding: 5px 1em;
      border-radius: 20px;
      height: 65%;
    "
  >
    <div class="disp-flex a-i-center">
      <span class="no-wrap-1-line">Recent Transactions</span>
      <div class="m-l-auto disp-flex">
        <div class="search-bar">
          <input
          (keyup)="searchUser($event)"
           type="text" placeholder="Search" />
        </div>
        <!-- <div>
          <button class="add-btn" title="Add New Project">
            <svg
              class="btn-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="3"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="feather feather-plus"
            >
              <line x1="12" y1="5" x2="12" y2="19" />
              <line x1="5" y1="12" x2="19" y2="12" />
            </svg>
          </button>
        </div> -->
      </div>
    </div>
    <div
      style="
        display: grid;
        grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.5fr;
        background-color: rgba(26, 37, 59, 0.5);
        align-items: center;
        margin-bottom: 5px;
        border-radius: 10px;
        gap: 1em;
      "
    >
      <span style="margin-left: 1em" class="no-wrap-1-line">Order Details</span>
      <span class="no-wrap-1-line"></span>
      <span class="no-wrap-1-line">Inflow</span>
      <span class="no-wrap-1-line">OutFlow</span>
      <span class="no-wrap-1-line">Type</span>
      <!-- <span class="disp-flex j-c-center">Actions</span> -->
    </div>
    <ul style="padding-inline-start: 0px; overflow: scroll" class="">
      <div
      *ngIf="data?.length === 0  && !loading" 
        style="
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
        "
      >
        <img
          style="width: 14%; max-width: 299px"
          src="/assets/icons/animatedIconsTable/MOF_listempty_infinite.svg"
          alt=""
        />
        <p style="font-size: 16px">List is empty</p>
      </div>

      <ng-container *ngIf="loading">
        <li
          *ngFor="let i of [].constructor(15)"
          class="skeleton1"
          style="
            display: grid;
            grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.5fr;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div style="gap: 7px" class="disp-flex">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 40px;
                height: 40px;
                border-radius: 50%;
              "
            ></span>
            <div class="disp-grid">
              <span
                class="skeleton2"
                style="
                  font-size: 14px;
                  font-weight: 500;
                  opacity: 0.7;
                  width: 177px;
                  height: 17px;
                  margin-bottom: 4px;
                "
              ></span>
              <span
                class="skeleton2"
                style="
                  font-size: 14px;
                  font-weight: 500;
                  opacity: 0.7;
                  width: 111px;
                  height: 16px;
                "
              ></span>
            </div>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 177px;
                height: 17px;
                margin-bottom: 4px;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 111px;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 177px;
                height: 17px;
                margin-bottom: 4px;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 111px;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 177px;
                height: 17px;
                margin-bottom: 4px;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 111px;
                height: 16px;
              "
            ></span>
          </div>
          <div class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 100%;
                height: 19px;
                margin-bottom: 4px;
              "
            ></span>
          </div>
          <div class="disp-flex j-c-center">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 28px;
                height: 10px;
                margin-bottom: 4px;
              "
            ></span>
          </div>
        </li>
      </ng-container>

      <ng-container *ngIf="!loading">
        <li
        *ngFor="
          let dat of data;
          let i = index"
          style="
            display: grid;
            grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.5fr;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div class="disp-flex gap-05">
            <img
            *ngIf="dat?.outflowCredits"
              style="min-width: 30px; max-width: 40px; opacity: 1"
              src="../assets/icons/arrowDown.svg"
              alt=""
            />
            <img
            *ngIf="dat?.inflowCredits"              
            style="min-width: 30px; max-width: 40px; opacity: 1"
              src="../assets/icons/arrowUp.svg"
              alt=""
            />
            
            <div class="disp-grid">
              <span class="no-wrap-1-line">{{ dat?.createdBy?.name }} {{ dat?.createdBy?.surname }}</span>
              <span
                class="no-wrap-1-line"
                style="font-size: 14px; font-weight: 500; opacity: 0.7"
                >{{ dat?.createdAt | date : "MMM d, y, h:mm a" }}</span
              >
            </div>
          </div>
          <div class="disp-grid"></div>
          <div class="disp-grid">
            <span class="no-wrap-1-line" 
            *ngIf="dat?.inflowCredits">{{dat?.creditsAmount}} credits</span>
            <span class="no-wrap-1-line" 
            *ngIf="!dat?.inflowCredits">0 credits</span>
            <span
              class="no-wrap-1-line"
              style="font-size: 14px; font-weight: 500; opacity: 0.7"
              ></span>
          </div>
          <div class="disp-grid">
            <span class="no-wrap-1-line" 
            *ngIf="dat?.outflowCredits">{{dat?.creditsAmount}} credits</span>
            <span class="no-wrap-1-line" 
            *ngIf="!dat?.outflowCredits">0 credits</span>
            <span
              class="no-wrap-1-line"
              style="font-size: 14px; font-weight: 500; opacity: 0.7"
              ></span>
          </div>
          <span 
          style="font-size: 12px;"
          class="status-inProgress ">{{dat?.type}}</span>
          <div class="disp-flex j-c-center">
            <!-- <span class="no-wrap-1-line">•••</span> -->
          </div>
        </li>
      </ng-container>
    </ul>
    <div class="list-number disp-flex" style="">
      <div
        class="showingInfoWrapper"
        style="
          margin: 0 0 0.75rem;
          display: flex !important;
          align-items: center;
          gap: 1em;
        "
      >
        <span class="showingInfo"
          >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
          {{ pageNo * resultsPerPage - (resultsPerPage - data.length) }} of
          {{ count }}</span>
        <div style="border: 1px solid gray; border-radius: 15px">
          <div>
            <input style="display: none" id="dropdownInput" type="checkbox" />
            <div
              style="
                display: flex;
                justify-content: center;
                height: 45px;
                width: 50px;
              "
              class="dropdown"
            >
              <select
                style="
                  display: flex;
                  background: transparent;
                  border: none;
                  color: white;
                  font-size: 18px;
                  width: 90%;
                  font-weight: 600;
                "
                (ngModelChange)='resultsPerPageChanged($event)' 
                [(ngModel)]="resultsPerPage">
                <option class="optionStyle colorCancel">5</option>
                <option class="optionStyle colorCancel">7</option>
                <option class="optionStyle colorCancel">10</option>
                <option class="optionStyle colorCancel">15</option>
                <option class="optionStyle colorCancel">20</option>
                <option class="optionStyle colorCancel">30</option>
                <option class="optionStyle colorCancel">50</option>
              </select>
              <label for="dropdownInput" class="overlay"></label>
            </div>
          </div>
        </div>
      </div>
      <nav>
        <ul class="pager">
          <li class="pager__item pager__item--prev">
            <a
              *ngIf="pageNo !== 1"
              (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Previous
            </a>
          </li>
          <li *ngIf="pageNo !== 1" class="pager__item">
            <a (click)="setPage(1)" class="pager__link">...</a>
          </li>
          <li
            *ngFor="
              let item of [].constructor(pageNoTotal) | slice : 0 : 5;
              let i = index
            "
            [ngClass]="pageNo + i === pageNo ? 'active' : ''"
            class="pager__item"
          >
            <a
              *ngIf="pageNo + i <= pageNoTotal"
              (click)="setPage(pageNo + i)"
              class="pager__link"
              >{{ pageNo + i }}</a
            >
          </li>
          <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
            <a (click)="setPage(pageNoTotal)" class="pager__link">...</a>
          </li>
          <li
            *ngIf="pageNo !== pageNoTotal"
            class="pager__item pager__item--next"
          >
            <a
              (click)="pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Next
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</div>
