const catchAsync = require('./../utils/catchAsync');
const User = require('./../models/userModel');
const BannedSuggestedUsers = require('./../models/bannedSuggestedUsersModel');
const Post = require('./../models/postModel');

exports.create = catchAsync(async (req, res, next) => {
      let fingUser = await BannedSuggestedUsers.findOne({user: req.body.data.user})
      if(fingUser !== null){
        await BannedSuggestedUsers.findByIdAndUpdate(req.body.data.user,req.body.data);
      }else{
        await BannedSuggestedUsers.create(req.body.data);
      }
    res.status(201).json({
        status: 'success',
        data: {}
    });

});


exports.getAll = catchAsync(async (req, res, next) => {

    let resultsPerPage =  req.body.resultsPerPage  
    let page = req.body.page 
    page = page - 1
    
    let data =
     await BannedSuggestedUsers.find()
     .populate({
      path: 'user',
      select: ['id','_id','name','surname','isVerified','photoColor','untrusted','earnCount','likesCount','commentsCount','postCount','email']
    })
    .limit( resultsPerPage)
    .skip( resultsPerPage * page)
    .sort({ _id: -1 })
    
    let count = await BannedSuggestedUsers.countDocuments()

    res.status(200).json({
      status: 'success',
      data: {
        data: data,
        count: count
      }
    });
  
});

exports.findByIdAndDelete = catchAsync(async (req, res, next) => {
    let data = await BannedSuggestedUsers.findOne({user: req.body.userId})
    if(data !== null){
        await BannedSuggestedUsers.findByIdAndDelete(data._id);
    }
    res.status(201).json({
        status: 'success'
    });
});

exports.search = catchAsync(async (req, res, next) => {
    if(req.body.searchText !== ''){
        let resultsPerPage =   req.body.resultsPerPage 
        let page = req.body.page
        page = page - 1
        req.body.searchText = req.body.searchText.trim()
  
        let name = req.body.searchText.split(" ")[0]; 
        let surname = req.body.searchText.split(" ")[1] === undefined ? name : req.body.searchText.split(" ")[1]; 
  
        let nameRegex = name.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
        let surnameRegex = surname.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
        
        nameRegex = new RegExp(nameRegex, 'i') 
        surnameRegex = new RegExp(surnameRegex, 'i') 
  
        let filter = {
          $or: [
            { 'user.name':  { $regex: nameRegex } },
            { 'user.surname':  { $regex: surnameRegex } },
          ]
        }
  
        BannedSuggestedUsers.find(filter)
        .populate({
            path: 'user',
            select: ['id','_id','name','surname','isVerified','photoColor','untrusted']
        })
        .limit(resultsPerPage)
        .skip(resultsPerPage * page)
        .sort({ _id: -1 })
        .then(  async result => {
          // console.log("result: ",result)
          let countList = await BannedSuggestedUsers.find(filter,{user:0})
          let count = countList.length
          res.status(200).json({
            status: 'success',
            data: {
              data: result,
              count: count
            }
          });
        })
  
      }else{
        res.status(200).json({
          status: 'success',
          resultSearch: result 
        });
      }
  });