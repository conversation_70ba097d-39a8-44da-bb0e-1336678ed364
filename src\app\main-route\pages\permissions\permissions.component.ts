import { Component, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { AuthService } from 'src/app/shared/services/auth.service';
import { PermissionCategoryService } from 'src/app/shared/services/permission-category.service';
import { PermissionService } from 'src/app/shared/services/permission.service';
import { RoleTitleService } from 'src/app/shared/services/role-title.service';

@Component({
  selector: 'app-permissions',
  templateUrl: './permissions.component.html',
  styleUrls: ['./permissions.component.css']
})
export class PermissionsComponent implements OnInit {
 
  me: User;

  permissionCategoryTitle = ''
  permissionCategoryLoading = false;
  addCategoryContent = null;

  categories: any = []

  permissionTitle = ''
  permissionLoading = false;
  addPermissionContent = null;
  selectPermissionCategory = null;

  permissions: any = []

  roleTitle = ''
  roleTitleLoading = false;
  addRoleTitleContent = null;

  rolesTitle: any = []

  constructor(
    private authService: AuthService,
    private permissionCategoryService: PermissionCategoryService,
    private permissionService: PermissionService,
    private roleTitleService: RoleTitleService,
    private modalService: NgbModal,
    private toastrService: ToastrService
  ) { 
    this.authService.user.pipe().subscribe((appUser) => {
      this.me = appUser;
    });
  }

  ngOnInit(): void {
    this.getAll()
    this.getAllRoles()
  }


  openDelRoleModal(delRoleContent) {
     this.modalService.open(delRoleContent, { centered: true });
  }

  openAddRoleModal(addRoleContent) {
    this.addRoleTitleContent = this.modalService.open(addRoleContent, { centered: true });
  }
  openAddPermissionModal(addPermissionContent,category) {
    this.selectPermissionCategory = category
    this.addPermissionContent = this.modalService.open(addPermissionContent, { centered: true });
  }
  openAddCategoryModal(addCategoryContent) {
    this.addCategoryContent = this.modalService.open(addCategoryContent, { centered: true });
  }


  createPermissionCategory(){
    if(this.permissionCategoryLoading === true){
      return
    }
    if(this.permissionCategoryTitle === ''){
      this.toastrService.error('Permission Category cannot be empty');
      return
    }
    this.permissionCategoryLoading = true;
    this.permissionCategoryService.create({
      title: this.permissionCategoryTitle,
      createdBy: this.me._id
    }).subscribe(
      (result) => {
        if (result.status == 'success') {
          console.log("result: ",result)
          this.categories = [...this.categories,{
            _id: result.data._id,
            title: this.permissionCategoryTitle,
            permissions: []
          }]
          this.permissionCategoryTitle = ''
          this.permissionCategoryLoading = false;
          this.addCategoryContent.close();
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }
  getAll(){
    this.permissionCategoryService.getAll({})
    .subscribe(
      (result) => {
        if (result.status == 'success') {
          // console.log("this.resultSearch: ",result.resultSearch)
          this.categories = result.data.data.map( category =>{
            category['permissions'] = []
            return category
          })

          for(let category of this.categories){
              this.getAllByCategoryId(category)
          }

          console.log('this.categories: ', this.categories);
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }

  createPermission(){
    if(this.permissionLoading === true){
      return
    }
    if(this.permissionTitle === ''){
      this.toastrService.error('Permission cannot be empty');
      return
    }
    this.permissionLoading = true;
    this.permissionService.create({
      category: this.selectPermissionCategory._id,
      title: this.permissionTitle,
      createdBy: this.me._id
    }).subscribe(
      (result) => {
        if (result.status == 'success') {
          const index = this.categories.findIndex(item => item._id === this.selectPermissionCategory._id);
          // console.log("this.resultSearch: ",result.resultSearch)
          this.categories[ 
            index
          ] = {
            _id: this.selectPermissionCategory._id,
            title: this.selectPermissionCategory.title,
            permissions: [ ...this.selectPermissionCategory?.permissions,{
              title: this.permissionTitle
            }]
          }
          this.selectPermissionCategory = null
          this.permissionTitle = ''
          this.permissionLoading = false;
          this.addPermissionContent.close();
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }
  getAllByCategoryId(category){
    this.permissionService.getAllByCategoryId({
      category: category._id
    })
    .subscribe(
      (result) => {
        if (result.status == 'success') {
          console.log("getAllByCategoryId ",result)
          const index = this.categories.findIndex(item => item._id === category._id);
          this.categories[ 
            index
          ] = {
            _id: category._id,
            title: category.title,
            permissions: result.data.data
          }

          // this.categories = result.data.data.map( category =>{
          //   category['permissions'] = []
          //   return category
          // })
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }


  createRoleTitle(){
    if(this.roleTitleLoading === true){
      return
    }
    if(this.roleTitle === ''){
      this.toastrService.error('Role Title cannot be empty');
      return
    }
    this.roleTitleLoading = true;
    this.roleTitleService.create({
      title: this.roleTitle,
      createdBy: this.me._id
    }).subscribe(
      (result) => {
        if (result.status == 'success') {
          console.log("result: ",result)
          this.rolesTitle = [...this.rolesTitle,{
            _id: result.data._id,
            title: this.roleTitle,
            permissions: []
          }]
          this.roleTitle = ''
          this.roleTitleLoading = false;
          this.addRoleTitleContent.close();
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  } 
  getAllRoles(){
    this.roleTitleService.getAll({})
    .subscribe(
      (result) => {
        if (result.status == 'success') {
          // console.log("this.resultSearch: ",result.resultSearch)
          this.rolesTitle = result.data.data

          console.log('this.rolesTitle: ', this.rolesTitle);
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }

}
