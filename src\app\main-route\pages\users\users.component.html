<div style="" class="projects-section users-section">
  <div
    style="
      /* display: none !important; */
      display: grid;
      grid-template-rows: 4em 2em 1fr 4em;
      background-color: var(--sidebar);
      padding: 5px 1em;
      border-radius: 20px;
      height: 76vh;
    "
  >
    <div class="disp-flex a-i-center">
      <span class="no-wrap-1-line">Users List</span>
      <div class="m-l-auto disp-flex">
        <div>
          <input style="display: none" id="dropdownInput" type="checkbox" />
          <div
            style="
              display: flex;
              justify-content: center;
              height: 35px;
              width: 147px;
            "
            class="dropdown"
          >
            <select
              style="
                display: flex;
                background: transparent;
                border: none;
                color: white;
                font-size: 18px;
                width: 90%;
                font-weight: 600;
              "
              (ngModelChange)='changeStatus($event)' 
              [(ngModel)]="statusSelected">
              <option class="optionStyle colorCancel">All</option>
              <option class="optionStyle colorCancel">untrusted</option>
              <option class="optionStyle colorCancel">isVerified</option>
              <option class="optionStyle colorCancel">deleted</option>
            </select>
            <label for="dropdownInput" class="overlay"></label>
          </div>
        </div>
        <div class="search-bar">
          <input
          (keyup)="searchUser($event)" 
          type="text" 
          placeholder="Search for user..." />
        </div>
      </div>
    </div>
    <div
      style="
        display: grid;
        grid-template-columns: 1.2fr 3fr 0.5fr 0.5fr 0.3fr;
        background-color: rgba(26, 37, 59, 0.5);
        align-items: center;
        margin-bottom: 5px;
        border-radius: 10px;
        gap: 1em;
      "
    >
      <span style="margin-left: 1em" class="no-wrap-1-line">Users Details</span>
      <span class="no-wrap-1-line"></span>
      <span class="no-wrap-1-line">Status</span>
      <span class="no-wrap-1-line"></span>
      <span class="no-wrap-1-line"></span>
    </div>
    <ul style="padding-inline-start: 0px; overflow: scroll" class="">
      <!-- Skeleton -->
      <ng-container
      *ngIf="loading">
        <li
          *ngFor="let i of [].constructor(15)"
          style="
            display: grid;
            grid-template-columns: 1.2fr 3fr 0.5fr 0.5fr 0.3fr;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div style="gap: 5px; align-items: center" class="disp-flex">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 40px;
                height: 40px;
                border-radius: 50%;
              "
            ></span>
            <div class="disp-grid">
              <span
                class="skeleton2"
                style="
                  font-size: 14px;
                  font-weight: 500;
                  opacity: 0.7;
                  width: 111px;
                  height: 16px;
                  margin-bottom: 3px;
                "
              ></span>
              <span
                class="skeleton2"
                style="
                  font-size: 14px;
                  font-weight: 500;
                  opacity: 0.7;
                  width: 111px;
                  height: 16px;
                "
              ></span>
            </div>
          </div>
          <div style="gap: 8px" class="disp-flex">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 72px;
                height: 38px;
                border-radius: 5px;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 72px;
                height: 38px;
                border-radius: 5px;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 43px;
                height: 38px;
                border-radius: 5px;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 65px;
                height: 38px;
                border-radius: 5px;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 43px;
                height: 38px;
                border-radius: 5px;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 50px;
                height: 38px;
                border-radius: 5px;
              "
            ></span>
          </div>
          <div style="justify-content: center" class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 120px;
                height: 25px;
              "
            ></span>
          </div>
          <div style="justify-content: center" class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 100px;
                height: 20px;
              "
            ></span>
          </div>
        </li>
      </ng-container>
      <!-- Skeleton ENd -->
      <ng-container
      *ngIf="!loading">
        <li
          *ngFor="let dat of data"
          style="
            display: grid;
            grid-template-columns: 1.2fr 3fr 0.5fr 0.5fr 0.3fr;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div class="disp-flex gap-05">
            <div
              style="
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;
              "
            >
            <app-avatar-photo
            [buffer]="dat?.photoProfile?.data"
            [userId]="dat?._id"
            [circleColor]="dat?.photoColor"
            [name]="dat?.name"
            [surname]="dat?.surname"
            [class]="'userImgBloggers'"
            [classAvatarInitials]="'initialsClass'"
            ></app-avatar-photo>
          
              <img
              *ngIf="!dat?.active"
                style="
                  height: 15px;
                  width: 15px;
                  position: absolute;
                  bottom: 0;
                  right: 0;"
                src="/assets/icons/delete-bin-2-fill.svg"
                alt=""
              />
              <img
              *ngIf="dat?.isVerified"
                style="
                  height: 15px;
                  width: 15px;
                  position: absolute;
                  bottom: 0;
                  right: 0;"
                src="/assets/icons/fiberVerified.svg"
                alt=""
              />
              <img
              *ngIf="dat?.untrusted"
                style="
                  height: 15px;
                  width: 15px;
                  position: absolute;
                  bottom: 0;
                  right: 0;"
                src="/assets/icons/fiberUnverified.svg"
                alt=""
              />
            </div>

            <div class="disp-grid a-i-center">
              <span style="font-size: 12px" class="no-wrap-1-line">
                {{dat?.name}} {{dat?.surname}} </span>
                <span style="font-size: 10px; opacity: 0.7" class="no-wrap-1-line"
                >{{dat?.email}}</span>
              <span style="font-size: 10px; opacity: 0.7" class="no-wrap-1-line"
                >{{ dat?.updatedAt | date : 'short' }}</span>
            </div>
          </div>
          <div style="gap: 0.5em" class="disp-flex">
            <div
              style="position: relative; display: flex; gap: 0.5em"
            >
              <div
                class="disp-grid a-i-center j-c-center userInfoWrapper userCreditsInfoWrapper"
              >
                <span class="no-wrap-1-line" style="text-align: center"
                  >Credits</span
                >
                <span class="no-wrap-1-line" style="text-align: center">
                  {{dat?.coins}}</span>
              </div>
              <div class="disp-grid a-i-center j-c-center userInfoWrapper">
                <span class="no-wrap-1-line" style="text-align: center"
                  >Followers</span
                >
                <span class="no-wrap-1-line" style="text-align: center">{{
                  dat?.followersCount
                }}</span>
              </div>
              <div class="disp-grid a-i-center j-c-center userInfoWrapper">
                <span class="no-wrap-1-line" style="text-align: center"
                  >Following</span
                >
                <span class="no-wrap-1-line" style="text-align: center">{{
                  dat?.followingCount
                }}</span>
              </div>
              <div class="disp-grid a-i-center j-c-center userInfoWrapper">
                <span class="no-wrap-1-line" style="text-align: center"
                  >Posts</span
                >
                <span class="no-wrap-1-line" style="text-align: center">{{
                  dat?.postCount
                }}</span>
              </div>
              <div class="disp-grid a-i-center j-c-center userInfoWrapper">
                <span class="no-wrap-1-line" style="text-align: center"
                  >Comments</span
                >
                <span class="no-wrap-1-line" style="text-align: center">{{
                  dat?.commentsCount
                }}</span>
              </div>
              <div class="disp-grid a-i-center j-c-center userInfoWrapper">
                <span class="no-wrap-1-line" style="text-align: center"
                  >Likes</span
                >
                <span class="no-wrap-1-line" style="text-align: center">{{
                  dat?.likesCount
                }}</span>
              </div>
              <div class="disp-grid a-i-center j-c-center userInfoWrapper">
                <span class="no-wrap-1-line" style="text-align: center"
                  >Earned</span
                >
                <span class="no-wrap-1-line" style="text-align: center">{{
                  dat?.earnCount
                }}</span>
              </div>
            </div>
          </div>
          <div style="gap: 1em" class="disp-flex j-c-center a-i-center">
            <span
              (click)="
              userSelected=dat;
              openSelectImgModal(selectImgModal)"
              style="
                height: 25px;
                width: 100%;
                background-color: var(--action-color);
                border-radius: 10px;
                font-size: 15px;
                display: flex;
                align-items: center;
                justify-content: center;
              "
              class="no-wrap-1-line"
              >Status</span>
          </div>
          <div style="gap: 1em" class="disp-flex j-c-center a-i-center">
            <span
              style="
                height: 25px;
                width: 100%;
                border-radius: 10px;
                font-size: 15px;
                display: flex;
                align-items: center;
                justify-content: center;
              "
              class="no-wrap-1-line"
              >View More</span>
          </div>

          <div
            (click)="
            userSelected=dat;
            openDeleteUserModal(deleteUserContent)"
            style="gap: 1em"
            class="disp-flex j-c-center a-i-center"
          >
            <img
            *ngIf="dat?.active"
              style="height: 20px; width: 20px"
              src="/assets/icons/bin.svg"
              alt=""
            />
            <img
            *ngIf="!dat?.active"
              style="height: 25px; width: 25px"
              src="/assets/icons/RecycleBin.svg"
              alt=""
            />
          </div>
        </li>
      </ng-container>
    </ul>
    <div class="list-number disp-flex" style="">
      <div
        class="showingInfoWrapper"
        style="
          margin: 0 0 0.75rem;
          display: flex !important;
          align-items: center;
          gap: 1em;
        "
      >
        <span class="showingInfo"
          >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
          {{ pageNo * resultsPerPage - (resultsPerPage - data.length) }} of
          {{ count }}</span>
        <div style="border: 1px solid gray; border-radius: 15px">
          <div>
            <input style="display: none" id="dropdownInput" type="checkbox" />
            <div
              style="
                display: flex;
                justify-content: center;
                height: 45px;
                width: 50px;
              "
              class="dropdown"
            >
              <select
                style="
                  display: flex;
                  background: transparent;
                  border: none;
                  color: white;
                  font-size: 18px;
                  width: 90%;
                  font-weight: 600;
                "
                (ngModelChange)="resultsPerPageChanged($event)"
                [(ngModel)]="resultsPerPage"
              >
              <option class="optionStyle colorCancel">7</option>
              <option class="optionStyle colorCancel">10</option>
              <option class="optionStyle colorCancel">15</option>
              <option class="optionStyle colorCancel">20</option>
              <option class="optionStyle colorCancel">30</option>
              <option class="optionStyle colorCancel">50</option>
              </select>
              <label for="dropdownInput" class="overlay"></label>
            </div>
          </div>
        </div>
      </div>
      <nav>
        <ul class="pager">
          <li class="pager__item pager__item--prev">
            <a
              *ngIf="pageNo !== 1"
              (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Previous
            </a>
          </li>
          <li *ngIf="pageNo !== 1" class="pager__item">
            <a (click)="setPage(1)" class="pager__link">...</a>
          </li>
          <li
            *ngFor="
              let item of [].constructor(pageNoTotal) | slice : 0 : 5;
              let i = index
            "
            [ngClass]="pageNo + i === pageNo ? 'active' : ''"
            class="pager__item"
          >
            <a
              *ngIf="pageNo + i <= pageNoTotal"
              (click)="setPage(pageNo + i)"
              class="pager__link"
              >{{ pageNo + i }}</a
            >
          </li>
          <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
            <a (click)="setPage(pageNoTotal)" class="pager__link">...</a>
          </li>
          <li
            *ngIf="pageNo !== pageNoTotal"
            class="pager__item pager__item--next"
          >
            <a
              (click)="pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Next
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>

  <ng-template #deleteUserContent let-modal>
    <div
      style="
        background-color: var(--app-bg);
        border-radius: 20px;
        scale: 1.1;
        height: 100%;
      "
    >
      <div class="modal-body">
        <!-- header -->
        <div style="display: flex; justify-content: end; width: 100%">
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; margin-left: auto"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>

        <div style="margin: 1em 0">
          <div style="margin: 0em 1em 2em" class="disp-flex gap-05">
            <div
              style="
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;
              ">
            <app-avatar-photo
            [buffer]="userSelected?.photoProfile?.data"
            [userId]="userSelected?._id"
            [circleColor]="userSelected?.photoColor"
            [name]="userSelected?.name"
            [surname]="userSelected?.surname"
            [class]="'userImgBloggers'"
            [classAvatarInitials]="'initialsClass'"
          ></app-avatar-photo>
              <img
              *ngIf="userSelected?.isVerified"
                style="
                  height: 15px;
                  width: 15px;
                  position: absolute;
                  bottom: 0;
                  right: 0;"
                src="/assets/icons/fiberVerified.svg"
                alt=""
              />
              <img
              *ngIf="userSelected?.untrusted"
                style="
                  height: 15px;
                  width: 15px;
                  position: absolute;
                  bottom: 0;
                  right: 0;"
                src="/assets/icons/fiberUnverified.svg"
                alt=""
              />
              <img
              *ngIf="!userSelected?.active"
                style="
                  height: 15px;
                  width: 15px;
                  position: absolute;
                  bottom: 0;
                  right: 0;"
                src="/assets/icons/delete-bin-2-fill.svg"
                alt=""
              />
            </div>

            <div class="disp-grid a-i-center">
              <span style="font-size: 16px" class="no-wrap-1-line"
                >{{userSelected.name}} {{userSelected.surname}}</span>
              <span style="font-size: 12px; opacity: 0.7" class="no-wrap-1-line"
                >{{userSelected._id}} </span>
              <span style="font-size: 12px; opacity: 0.7" class="no-wrap-1-line"
                >{{userSelected.email}} </span>
            </div>
          </div>

          <p 
          *ngIf="userSelected.active"
          style="text-align: center; font-size: 16px; font-weight: 700">
            Are you sure that you want to DELETE this user
          </p>
          <p 
          *ngIf="!userSelected.active"
          style="text-align: center; font-size: 16px; font-weight: 700">
            Are you sure that you want to RECOVER this user
          </p>
          <div
            style="
              margin-top: 2em;
              display: flex;
              align-items: center;
              justify-content: center;
            "
          >
            <span
              *ngIf="userSelected.active"
              (click)="deleteUser()"
              style="
                background: var(--red);
                padding: 10px 4em;
                font-size: 20px;
                border-radius: 15px;
              "
              >Delete User</span>
              <span
              *ngIf="!userSelected.active"
              (click)="deleteUser()"
              style="
                background-color: var(--action-color);
                padding: 10px 4em;
                font-size: 20px;
                border-radius: 15px;
              "
              >Recover</span>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
  <ng-template #selectImgModal let-modal>
    <div
      style="
        background-color: var(--app-bg);
        border-radius: 20px;
        scale: 1.1;
        height: 100%;
      "
    >
      <div class="modal-body">
        <!-- header -->
        <div style="display: flex; justify-content: end; width: 100%">
          <img
            (click)="modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; margin-left: auto"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>

        <div style="margin: 1em 0">
          <div style="margin: 0em 1em 2em" class="disp-flex gap-05">
            <div
              style="
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;
              ">
            <app-avatar-photo
            [buffer]="userSelected?.photoProfile?.data"
            [userId]="userSelected?._id"
            [circleColor]="userSelected?.photoColor"
            [name]="userSelected?.name"
            [surname]="userSelected?.surname"
            [class]="'userImgBloggers'"
            [classAvatarInitials]="'initialsClass'"
          ></app-avatar-photo>
              <img
              *ngIf="userSelected?.isVerified"
                style="
                  height: 15px;
                  width: 15px;
                  position: absolute;
                  bottom: 0;
                  right: 0;"
                src="/assets/icons/fiberVerified.svg"
                alt=""
              />
              <img
              *ngIf="userSelected?.untrusted"
                style="
                  height: 15px;
                  width: 15px;
                  position: absolute;
                  bottom: 0;
                  right: 0;"
                src="/assets/icons/fiberUnverified.svg"
                alt=""
              />
              <img
              *ngIf="!userSelected?.active"
                style="
                  height: 15px;
                  width: 15px;
                  position: absolute;
                  bottom: 0;
                  right: 0;"
                src="/assets/icons/delete-bin-2-fill.svg"
                alt=""
              />
            </div>

            <div class="disp-grid a-i-center">
              <span style="font-size: 16px" class="no-wrap-1-line"
                >{{userSelected.name}} {{userSelected.surname}}</span>
              <span style="font-size: 12px; opacity: 0.7" class="no-wrap-1-line"
                >{{userSelected._id}} </span>
              <span style="font-size: 12px; opacity: 0.7" class="no-wrap-1-line"
                >{{userSelected.email}} </span>
            </div>
          </div>
          <div
            style="border: 1px solid gray; border-radius: 15px; height: 55px"
          >
            <label
              for="status"
              style="
                margin: -11px 0px 0 14px;
                font-size: 16px;
                font-weight: 600;
                background: var(--app-bg);
                width: fit-content;
                padding: 0 13px;
                display: flex;
              "
            >
              Status
            </label>
            <div>
              <input id="dropdownInput" type="checkbox" style="display: none" />
              <div
                class="dropdown"
                style="display: flex; justify-content: center"
              >
                <select
                  style="
                    display: flex;
                    background: transparent;
                    border: none;
                    color: white;
                    font-size: 18px;
                    width: 90%;
                    font-weight: 600; "
                    (ngModelChange)='changeUserStatus($event)'
                    [ngModel]="getStatus(userSelected.isVerified,
                    userSelected.untrusted)"
                  class="ng-touched ng-dirty ng-valid">
                  <option
                    style="width: 19px !important"
                    class="optionStyle">
                    None
                  </option>
                  <option
                    style="width: 19px !important"
                    class="optionStyle">
                    Untrusted
                  </option>
                  <option class="optionStyle">
                    Verified
                  </option>
                </select>
                <label for="dropdownInput" class="overlay"></label>
              </div>
            </div>
          </div>
          <div
            style="
              margin-top: 2em;
              display: flex;
              align-items: center;
              justify-content: center;
            ">
              <span
              (click)="saveUserStatus()"
              style="
                height: 35px;
                width: 100%;
                background-color: var(--action-color);
                border-radius: 10px;
                font-size: 15px;
                display: flex;
                align-items: center;
                justify-content: center;
              "
              class="no-wrap-1-line"
              >Save</span>

          </div>
        </div>
      </div>
    </div>
  </ng-template>
</div>

