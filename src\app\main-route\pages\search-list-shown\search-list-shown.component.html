<div class="projects-section bloggers-section">
  <div
    style="
      /* display: none !important; */
      display: grid;
      grid-template-rows: 4em 2em 1fr 4em;
      background-color: var(--sidebar);
      padding: 5px 1em;
      border-radius: 20px;
      height: 76vh;
    "
  >
    <div style="gap: 1em" class="disp-flex a-i-center">
      <span class="no-wrap-1-line">Search User Shown List</span>
      <div  class="search-bar" style="margin-left: auto;max-width: 220px;">
        <input 
        style="
         
        "
        (keyup)="searchUser($event)"
        type="text" placeholder="Search" />
      </div>
      <span
        (click)="save()"
        style="
          height: 30px;
          padding: 0 2em;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: var(--action-color);
          border-radius: 12px;
          cursor: pointer;
        "
        >Save</span
      >
      <span
        (click)="openUserSearchModal(searchContent)"
        style="
          height: 30px;
          width: 30px;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 30px;
          background-color: var(--action-color);
          margin-bottom: 1px;
          cursor: pointer;
        "
        >+</span
      >
    </div>
    <div
      style="
        display: grid;
        grid-template-columns: 1fr 2fr 0.5fr;
        background-color: rgba(26, 37, 59, 0.5);
        align-items: center;
        margin-bottom: 5px;
        border-radius: 10px;
        gap: 1em;
      "
    >
      <span style="margin-left: 1em" class="no-wrap-1-line">User Details</span>
      <span class="no-wrap-1-line"></span>
      <span style="text-align: center" class="no-wrap-1-line">Action</span>
    </div>
    <ul style="padding-inline-start: 0px; overflow: scroll" class="">

      <ng-container
      *ngIf="loading">
        <li
          *ngFor="let i of [].constructor(15)"
          style="
            display: grid;
            grid-template-columns: 1fr 2fr 0.5fr;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div style="gap: 5px; align-items: center" class="disp-flex">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 40px;
                height: 40px;
                border-radius: 50%;
              "
            ></span>
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 111px;
                height: 16px;
              "
            ></span>
          </div>
          <div style="gap: 8px" class="disp-flex"></div>
          <div style="justify-content: center" class="disp-grid">
            <span
              class="skeleton2"
              style="
                font-size: 14px;
                font-weight: 500;
                opacity: 0.7;
                width: 25px;
                height: 13px;
              "
            ></span>
          </div>
        </li>
      </ng-container>
      <ng-container
      *ngIf="!loading">
        <li
          *ngFor="let dat of data"
          style="
            display: grid;
            grid-template-columns: 1fr 2fr 0.5fr;
            align-items: center;
            min-height: 4em;
            gap: 1em;
            border-bottom: 1px solid rgba(26, 37, 59, 0.5);
          "
        >
          <div class="disp-flex gap-05">
              <app-avatar-photo
              [buffer]="dat?.user?.photoProfile?.data"
              [userId]="dat?.user?._id"
              [circleColor]="dat?.user?.photoColor"
              [name]="dat?.user?.name"
              [surname]="dat?.user?.surname"
              [class]="'userImgBloggers'"
              [classAvatarInitials]="'initialsClass'"
            ></app-avatar-photo>
            <div class="disp-grid a-i-center">
              <span style="font-size: 16px" class="no-wrap-1-line">
                {{ dat?.user?.name }} {{ dat?.user?.surname }}
              </span>
            </div>
          </div>
          <div style="gap: 0.5em" class="disp-flex"></div>

          <div style="gap: 1em" class="disp-flex j-c-center a-i-center">
            <img
            (click)="removeUser(dat?.user?._id)"
              style="
                position: relative;
                height: 15px;
                width: 15px;
                border-radius: 50%;
              "
              src="/assets/icons/close.svg"
              alt=""
            />
          </div>
        </li>
      </ng-container>
    </ul>
    <div class="list-number disp-flex" style="">
      <div
        class="showingInfoWrapper"
        style="
          margin: 0 0 0.75rem;
          display: flex !important;
          align-items: center;
          gap: 1em;
        "
      >
        <span class="showingInfo"
        >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
        {{ pageNo * resultsPerPage - (resultsPerPage - data.length) }} of
        {{ count }}</span>
        <div style="border: 1px solid gray; border-radius: 15px">
          <div>
            <input style="display: none" id="dropdownInput" type="checkbox" />
            <div
              style="
                display: flex;
                justify-content: center;
                height: 45px;
                width: 50px;
              "
              class="dropdown"
            >
              <select
                style="
                  display: flex;
                  background: transparent;
                  border: none;
                  color: white;
                  font-size: 18px;
                  width: 90%;
                  font-weight: 600;
                " (ngModelChange)="resultsPerPageChanged($event)"
                  [(ngModel)]="resultsPerPage">
                <option class="optionStyle colorCancel">7</option>
                <option class="optionStyle colorCancel">10</option>
                <option class="optionStyle colorCancel">12</option>
                <option class="optionStyle colorCancel">15</option>
                <option class="optionStyle colorCancel">17</option>
              </select>
              <label for="dropdownInput" class="overlay"></label>
            </div>
          </div>
        </div>
      </div>
      <nav>
        <ul class="pager">
          <li class="pager__item pager__item--prev">
            <a
              *ngIf="pageNo !== 1"
              (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Previous
            </a>
          </li>
          <li *ngIf="pageNo !== 1" class="pager__item">
            <a (click)="setPage(1)" class="pager__link">...</a>
          </li>
          <li
            *ngFor="
              let item of [].constructor(pageNoTotal) | slice : 0 : 5;
              let i = index
            "
            [ngClass]="pageNo + i === pageNo ? 'active' : ''"
            class="pager__item"
          >
            <a
              *ngIf="pageNo + i <= pageNoTotal"
              (click)="setPage(pageNo + i)"
              class="pager__link"
              >{{ pageNo + i }}</a
            >
          </li>
          <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
            <a (click)="setPage(pageNoTotal)" class="pager__link">...</a>
          </li>
          <li
            *ngIf="pageNo !== pageNoTotal"
            class="pager__item pager__item--next"
          >
            <a
              (click)="pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Next
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
  <div
    style="
      display: flex;
      border-radius: 20px;
      overflow: hidden;
      gap: 1.5em;
      min-height: 608px;
    "
    class="disp-grid"
  >
    <div
      style="
        background-image: url(assets/iphone.svg);
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        height: 90%;
        max-height: 608px;
      "
    >
      <div
        style="
          height: 100%;
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        "
      >
        <div
          style="
            height: 91%;
            width: 79%;
            margin: 20px 0 0 0;
            border-radius: 0 0 35px 35px;
            display: flex;
            flex-direction: column;
            gap: 1em;
            align-items: center;
            max-width: 260px;
          "
        >
          <div
            style="
              display: flex;
              align-items: center;
              padding: 0 1em;
              gap: 5px;
              margin-top: 5px;
              width: 100%;
            "
          >
            <img
              style="height: 20px; width: 20px; transform: rotate(180deg)"
              src="/assets/icons/next.svg"
              alt=""
            />
            <div
              style="
                background-color: black;
                flex: 1;
                height: 24px;
                border-radius: 10px;
                box-shadow: 0px 0px 4px 0px rgba(172, 172, 172, 0.5);
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 1em;
              "
            >
              <span style="font-size: 8px; color: gray">Search on FiberAl</span
              ><svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                fill="none"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                class="feather feather-search"
                viewBox="0 0 24 24"
              >
                <defs></defs>
                <circle cx="11" cy="11" r="8"></circle>
                <path d="M21 21l-4.35-4.35"></path>
              </svg>
            </div>
          </div>
          <div
            style="
              width: 100%;
              padding: 0 1em;
              display: flex;
              flex-direction: column;
              overflow: scroll;
              height: 100%;
            "
          >
            <div style="display: flex; flex-direction: column; gap: 2em">
              <ng-container
              *ngIf="loading">
                <li
                  *ngFor="let i of [].constructor(15)"
                  style="
                    list-style: none; /* remove the bullets/dots from the list */
                    display: flex;
                    flex-direction: column;
                    gap: 5px;
                    height: fit-content;
                    width: 100%;
                  "
                >
                  <div
                    style="
                      display: flex;
                      gap: 5px;
                      width: 100%;
                      align-items: center;
                    "
                  >
                    <span
                      class="skeleton1"
                      style="height: 40px; width: 40px; border-radius: 50%"
                    ></span>
                    <span
                      class="skeleton2"
                      style="height: 17px; width: 85px"
                    ></span>
                    <span
                      class="skeleton2"
                      style="
                        margin-left: auto;
                        height: 22px;
                        width: 63px;
                        border-radius: 5px;
                      "
                    ></span>
                  </div>
                </li>
              </ng-container>
              <ng-container
              *ngIf="!loading">
                <li
                  style="
                    list-style: none; /* remove the bullets/dots from the list */
                    display: flex;
                    flex-direction: column;
                    gap: 5px;
                  "
                  *ngFor="let dat of data"
                >
                  <div
                    style="
                      display: flex;
                      gap: 5px;
                      width: 100%;
                      align-items: center;
                    "
                  >
                    <app-avatar-photo
                    [buffer]="dat?.user?.photoProfile?.data"
                    [userId]="dat?.user?._id"
                    [circleColor]="dat?.user?.photoColor"
                    [name]="dat?.user?.name"
                    [surname]="dat?.user?.surname"
                    [class]="'userImgBloggers'"
                    [classAvatarInitials]="'initialsClass'"
                  ></app-avatar-photo>
                    <span>{{ dat?.user?.name }} {{ dat?.user?.surname }}</span>
                    <span
                      style="
                        margin-left: auto;
                        background: var(--action-color);
                        padding: 0 7px;
                        border-radius: 5px;
                      "
                      >Follow</span
                    >
                  </div>
                </li>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-template #searchContent let-modal>
    <app-search-content
      [(me)]="me"
      (closeModal)="modal.dismiss('Cross click')"
      (userSelected)="selectUser($event)"
    ></app-search-content>
  </ng-template>
</div>
