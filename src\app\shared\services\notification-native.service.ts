import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';

const BACKEND_URL = environment.base_url + "/notificationNative/";

@Injectable({
  providedIn: 'root'
})
export class NotificationNativeService {

  constructor(
    private http: HttpClient
  ) { } 

  
  create(
    data
  ){
    return  this.http.post<any>( BACKEND_URL, {data:data})
  }

  edit(
    id,
    data
  ){
    return  this.http.post<any>( BACKEND_URL+'edit/'+id , {data:data})
  }

  getAll(page,resultsPerPage){
    let data = {
      page : page,
      resultsPerPage: resultsPerPage
    }
    return this.http.post<any>(BACKEND_URL+'getAll',data)
  }
  
}
