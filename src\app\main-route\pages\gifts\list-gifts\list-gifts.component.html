<div style="overflow: auto" class="projects-section">
  <div
    style="
      /* display: none !important; */
      display: grid;
      grid-template-rows: 4em 1fr 4em;
      background-color: var(--sidebar);
      padding: 5px 1em;
      border-radius: 40px;
      height: 80vh;
    "
  >
    <div class="disp-flex a-i-center">
      <span style="margin-left: 1em" class="no-wrap-1-line">Gifts</span>
      <div class="m-l-auto disp-flex">
        <div>
          <input style="display: none" id="dropdownInput" type="checkbox" />
          <!-- <div
            style="
              display: flex;
              justify-content: center;
              height: 35px;
              width: 147px;
            "
            class="dropdown"
          >
            <select
              style="
                display: flex;
                background: transparent;
                border: none;
                color: white;
                font-size: 18px;
                width: 90%;
                font-weight: 600;
              "
            >
              (ngModelChange)="changeStatus($event)"
              [(ngModel)]="statusSelected"
              <option class="optionStyle colorCancel">All</option>
              <option class="optionStyle colorCancel">untrusted</option>
              <option class="optionStyle colorCancel">isVerified</option>
              <option class="optionStyle colorCancel">deleted</option>
            </select>
            <label for="dropdownInput" class="overlay"></label>
          </div> -->
        </div>

        <div (click)="redirectTo('create-gift')">
          <button class="add-btn" title="Add New Project">+</button>
        </div>
      </div>
    </div>
    <div
      *ngIf="gifts.length === 0 && !loading"
      style="
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
      "
    >
      <img
        style="width: 70%; max-width: 299px"
        src="/assets/icons/animatedIconsTable/list_empty.svg"
        alt=""
      />
      <p style="font-size: 16px">List is empty</p>
    </div>
    <ul
      class="no-bullets"
      style="
        display: flex;
        flex-wrap: wrap;
        overflow: auto;
        justify-content: center;
      "
    >
      <!-- skeleton -->
      <ng-container *ngIf="loading">
        <li
          style="
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 2em;
            border-radius: 15px;
            gap: 1em;
            background: var(--app-container);
            flex: 1 0 300px;
            margin: 1rem;
            color: #fff;
            cursor: pointer;
            max-width: 300px;
            max-height: 336px;
          "
          class="skeleton1"
          *ngFor="let i of [].constructor(4)"
        >
          <div
            style="
              height: 20px;
              width: 20px;
              position: absolute;
              top: 1em;
              right: 1em;
            "
            class="skeleton2"
          ></div>
          <div
            style="
              height: 20px;
              width: 20px;
              position: absolute;
              top: 3em;
              right: 1em;
            "
            class="skeleton2"
          ></div>
          <div
            style="width: 60%; align-self: center; aspect-ratio: 16/9"
            class="skeleton2"
          ></div>
          <div style="display: flex; gap: 5px">
            <div
              style="width: 50%; height: 40px; align-self: center"
              class="skeleton2"
            ></div>
            <div
              style="width: 100%; height: 40px; align-self: center"
              class="skeleton2"
            ></div>
          </div>

          <div
            style="
              position: relative;
              display: grid;
              grid-template-columns: 1fr 1fr 1fr;
              align-items: center;
              gap: 5px;
            "
          >
            <div style="height: 40px; width: 100%" class="skeleton2"></div>
            <div style="height: 40px; width: 100%" class="skeleton2"></div>
            <div style="height: 40px; width: 100%" class="skeleton2"></div>
            <div style="height: 40px; width: 100%" class="skeleton2"></div>
            <div style="height: 40px; width: 100%" class="skeleton2"></div>
            <div style="height: 40px; width: 100%" class="skeleton2"></div>
          </div>
        </li>
      </ng-container>

      <li
        style="
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding: 2em;
          border-radius: 15px;
          gap: 1em;
          background: var(--app-container);
          flex: 1 0 300px;
          margin: 1rem;
          color: #fff;
          cursor: pointer;
          max-width: 300px;
          max-height: 336px;
        "
        *ngFor="let gift of gifts"
      >
        <img
          (click)="redirectTo('edit-gift/' + gift._id)"
          style="
            height: 20px;
            width: 20px;
            position: absolute;
            top: 1em;
            right: 1em;
            opacity: 0.85;
          "
          src="assets/icons/edit.svg"
          alt=""
        />
        <img
          *ngIf="gift?.active"
          (click)="openStatusChangeModal(statusContent, gift)"
          style="
            height: 20px;
            width: 20px;
            position: absolute;
            top: 4em;
            right: 1em;
            opacity: 0.85;
          "
          src="assets/icons/deactivate.svg"
          alt=""
        />
        <img
          *ngIf="!gift?.active"
          (click)="openStatusChangeModal(statusContent, gift)"
          style="
            height: 20px;
            width: 20px;
            position: absolute;
            top: 4em;
            right: 1em;
            opacity: 0.85;
          "
          src="assets/icons/activate.svg"
          alt=""
        />
        <div
          style="display: flex; align-items: center; justify-content: center"
        >
          <img
            thumbnailId
            *ngIf="gift?.thumbnailId"
            style="width: 65%; object-fit: contain"
            src="{{ backendImageUrl + gift.thumbnailId.toString() }}"
            alt="gift thumbnail"
          />
        </div>

        <div
          style="
            position: relative;
            display: grid;
            grid-template-columns: 1fr;
            align-items: center;
            gap: 15px;
          "
        >
          <div style="display: flex; gap: 5px">
            <div
              style="
                display: grid;
                flex: 1 0 30%;
                border: 1px solid var(--app-bg);
                border-radius: 10px;
                align-items: center;
                justify-content: center;
              "
            >
              <span
                style="
                  text-align: center;
                  font-size: 10px;
                  opacity: 0.8;
                  font-weight: 500;
                  background: var(--app-container);
                  margin-top: -8px;
                  margin-bottom: 5px;
                "
                >Order Index</span
              ><span style="text-align: center; margin-bottom: 5px">
                {{ gift?.orderIndex }}
              </span>
            </div>

            <div
              style="
                display: grid;
                /* width: 100%; */
                border: 1px solid var(--app-bg);
                border-radius: 10px;
                align-items: center;
                justify-content: center;
                flex: 1 0 67%;
              "
            >
              <span
                style="
                  text-align: center;
                  font-size: 10px;
                  opacity: 0.8;
                  font-weight: 500;
                  background: var(--app-container);
                  margin-top: -8px;
                  margin-bottom: 5px;
                "
                >Name</span
              ><span style="text-align: center; margin-bottom: 5px">
                {{ gift.title }}
              </span>
            </div>
          </div>

          <div style="display: flex; gap: 5px">
            <div
              style="
                display: grid;
                border: 1px solid var(--app-bg);
                border-radius: 10px;
                align-items: center;
                justify-content: center;
                flex: 1;
              "
            >
              <span
                style="
                  text-align: center;
                  font-size: 10px;
                  opacity: 0.8;
                  font-weight: 500;
                  background: var(--app-container);
                  margin-top: -8px;
                  margin-bottom: 5px;
                "
                >Number Shared</span
              >
              <span style="text-align: center; margin-bottom: 5px"> 0 </span>
            </div>

            <div
              style="
                display: grid;
                flex: 1;
                border: 1px solid var(--app-bg);
                border-radius: 10px;
                align-items: center;
                justify-content: center;
              "
            >
              <span
                style="
                  text-align: center;
                  font-size: 10px;
                  opacity: 0.8;
                  font-weight: 500;
                  background: var(--app-container);
                  margin-top: -8px;
                  margin-bottom: 5px;
                "
                >Shared Today</span
              >
              <span style="text-align: center; margin-bottom: 5px"> 0 </span>
            </div>
            <div
              style="
                display: grid;
                flex: 1;
                border: 1px solid var(--app-bg);
                border-radius: 10px;
                align-items: center;
                justify-content: center;
              "
            >
              <span
                style="
                  text-align: center;
                  font-size: 10px;
                  opacity: 0.8;
                  font-weight: 500;
                  background: var(--app-container);
                  margin-top: -8px;
                  margin-bottom: 5px;
                "
                >Tex Collected</span
              >
              <span style="text-align: center; margin-bottom: 5px"> 0 </span>
            </div>
          </div>

          <div style="display: flex; gap: 5px">
            <div
              style="
                display: grid;
                flex: 1;
                border: 1px solid var(--app-bg);
                border-radius: 10px;
                align-items: center;
                justify-content: center;
              "
            >
              <span
                style="
                  text-align: center;
                  font-size: 10px;
                  opacity: 0.8;
                  font-weight: 500;
                  background: var(--app-container);
                  margin-top: -8px;
                  margin-bottom: 5px;
                "
                >Tex Collected</span
              >
              <span style="text-align: center; margin-bottom: 5px"> 0 </span>
            </div>
            <div
              style="
                display: grid;
                flex: 1;
                border: 1px solid var(--app-bg);
                border-radius: 10px;
                align-items: center;
                justify-content: center;
              "
            >
              <span
                style="
                  text-align: center;
                  font-size: 10px;
                  opacity: 0.8;
                  font-weight: 500;
                  background: var(--app-container);
                  margin-top: -8px;
                  margin-bottom: 5px;
                "
                >Cost</span
              ><span style="text-align: center; margin-bottom: 5px">
                {{ gift?.cost }}
              </span>
            </div>
            <div
              style="
                display: grid;
                flex: 1;
                border: 1px solid var(--app-bg);
                border-radius: 10px;
                align-items: center;
                justify-content: center;
              "
            >
              <span
                style="
                  text-align: center;
                  font-size: 10px;
                  opacity: 0.8;
                  font-weight: 500;
                  background: var(--app-container);
                  margin-top: -8px;
                  margin-bottom: 5px;
                "
                >Cost</span
              ><span style="text-align: center; margin-bottom: 5px">
                {{ gift?.cost }}
              </span>
            </div>
          </div>
        </div>
      </li>
    </ul>
    <div class="list-number disp-flex" style="">
      <div
        class="showingInfoWrapper"
        style="
          margin: 0 0 0.75rem;
          display: flex !important;
          align-items: center;
          gap: 1em;
        "
      >
        <span class="showingInfo"
          >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to
          {{ pageNo * resultsPerPage - (resultsPerPage - gifts.length) }} of
          {{ count }}</span
        >
        <div style="border: 1px solid gray; border-radius: 15px">
          <div>
            <input style="display: none" id="dropdownInput" type="checkbox" />
            <div
              style="
                display: flex;
                justify-content: center;
                height: 45px;
                width: 50px;
              "
              class="dropdown"
            >
              <select
                style="
                  display: flex;
                  background: transparent;
                  border: none;
                  color: white;
                  font-size: 18px;
                  width: 90%;
                  font-weight: 600;
                "
                (ngModelChange)="resultsPerPageChanged($event)"
                [(ngModel)]="resultsPerPage"
              >
                <option class="optionStyle colorCancel">12</option>
                <option class="optionStyle colorCancel">18</option>
                <option class="optionStyle colorCancel">24</option>
                <option class="optionStyle colorCancel">30</option>
                <option class="optionStyle colorCancel">36</option>
                <option class="optionStyle colorCancel">42</option>
              </select>
              <label for="dropdownInput" class="overlay"></label>
            </div>
          </div>
        </div>
      </div>
      <nav>
        <ul class="pager">
          <li class="pager__item pager__item--prev">
            <a
              *ngIf="pageNo !== 1"
              (click)="pageNo !== 1 ? setPage(pageNo - 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Previous
            </a>
          </li>
          <li *ngIf="pageNo !== 1" class="pager__item">
            <a (click)="setPage(1)" class="pager__link">...</a>
          </li>
          <li
            *ngFor="
              let item of [].constructor(pageNoTotal) | slice : 0 : 5;
              let i = index
            "
            [ngClass]="pageNo + i === pageNo ? 'active' : ''"
            class="pager__item"
          >
            <a
              *ngIf="pageNo + i <= pageNoTotal"
              (click)="setPage(pageNo + i)"
              class="pager__link"
              >{{ pageNo + i }}</a
            >
          </li>
          <li *ngIf="pageNo !== pageNoTotal" class="pager__item">
            <a (click)="setPage(pageNoTotal)" class="pager__link">...</a>
          </li>
          <li
            *ngIf="pageNo !== pageNoTotal"
            class="pager__item pager__item--next"
          >
            <a
              (click)="pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''"
              style="width: fit-content !important; padding: 0 10px"
              class="pager__link"
            >
              Next
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>

  <ng-template #statusContent let-modal>
    <div
      style="background-color: var(--app-bg); border-radius: 20px; scale: 1.1"
    >
      <div class="modal-body">
        <!-- header -->
        <div
          style="
            display: flex;
            justify-content: center;
            width: 100%;
            position: relative;
          "
        >
          <img
            (click)="selectGift = null; modal.dismiss('Cross click')"
            style="height: 17px; width: 17px; position: absolute; right: 0"
            src="/assets/icons/close.svg"
            alt=""
          />
        </div>
        <!-- header -->

        <div style="margin: 1em 0">
          <div
            style="
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
              gap: 2rem;
            "
          >
            <img
              *ngIf="selectGift"
              style="width: 50%"
              src="{{ backendImageUrl + selectGift.thumbnailId.toString() }}"
              alt=""
            />
            <div style="width: 50%">
              <div
                style="
                  display: grid;
                  width: 100%;
                  border: 1px solid var(--app-container);
                  border-radius: 10px;
                  align-items: center;
                  justify-content: center;
                "
              >
                <span
                  style="
                    text-align: center;
                    font-size: 10px;
                    opacity: 0.8;
                    font-weight: 500;
                    background: var(--app-bg);
                    margin-top: -8px;
                    margin-bottom: 5px;
                  "
                  >Name</span
                >
                <span style="text-align: center; margin-bottom: 5px">
                  {{ selectGift.title }}
                </span>
              </div>
            </div>
            <h5 *ngIf="selectGift?.active">
              Do you want to Deactivate this gift
            </h5>
            <h5 *ngIf="!selectGift?.active">Do you want to Active this gift</h5>
            <div
              style="
                width: 50%;
                display: flex;
                justify-content: space-between;
                gap: 1em;
              "
            >
              <div
                (click)="selectGift = null; modal.dismiss('Cross click')"
                class="modalButtonsCancel"
              >
                Cancel
              </div>
              <div
                *ngIf="selectGift?.active"
                (click)="modal.dismiss('Cross click'); action()"
                class="modalButtonsDeactivate"
              >
                Deactivate
              </div>
              <div
                *ngIf="!selectGift?.active"
                (click)="modal.dismiss('Cross click'); action()"
                class="modalButtonsDeactivate"
              >
                Activate
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
</div>
