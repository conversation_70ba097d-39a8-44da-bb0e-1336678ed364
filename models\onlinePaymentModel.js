const mongoose = require('mongoose');
const { MongooseFindByReference } = require('mongoose-find-by-reference');

const onlinePaymentSchema = new mongoose.Schema({
  creditsAmount: {
    type: Number,
    required: [true, 'Not possible online Payment without creditsAmount!']
  },
  realMoneyAmount: {
    type: Number,
    required: [true, 'Not possible online Payment without realMoneyAmount!']
  },
  realMoneyCurrency: {
    type: String,
    required: [true, 'Not possible online Payment without realMoneyCurrency!']
  },
  type: {
    type: String,
    enum: ['paypal'],
    required: [true, 'Not possible online Payment without type of online payment!']
  },
  active: {
    type: Boolean,
    default: true
  },
  centralBankTransaction:{
    type: mongoose.Schema.ObjectId,
    ref: 'CentralBank',
    required: [true, 'must have central Bank Transaction created before onlinePayment!']
  },
  centralBankTotalCreditsAmountBefore: {
    type: Number,
    default: 0
  },
  centralBankTotalCreditsAmountAfter: {
    type: Number,
    default: 0
  },
  transaction:{
    type: mongoose.Schema.ObjectId,
    ref: 'Transaction',
    required: [true, 'must have transaction created before online Payment!']
  },
  user:{
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: [true, 'must been created from a User!']
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

onlinePaymentSchema.pre(/^find/, function(next) {
  // this.populate({
  //   path: 'user',
  // });
  next();
})
onlinePaymentSchema.plugin(MongooseFindByReference);

const OnlinePayment = mongoose.model('OnlinePayment', onlinePaymentSchema);

module.exports = OnlinePayment;