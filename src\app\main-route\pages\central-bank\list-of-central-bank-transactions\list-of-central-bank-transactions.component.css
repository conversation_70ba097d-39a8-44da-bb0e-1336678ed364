.tiles {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  -moz-column-gap: 1rem;
  column-gap: 1rem;
  row-gap: 1rem;
  margin-top: 0.25rem;
}
@media (max-width: 647px) {
  .tiles {
    grid-template-columns: repeat(1, 1fr);
  }
  .tile {
    min-height: 170px !important;
  }
}

.tile {
  padding: 1rem;
  border-radius: 8px;
  background-color: #26364f4a;
  color: var(--c-gray-900);
  min-height: 168px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  transition: 0.25s ease;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.068), 0 6px 6px rgba(0, 0, 0, 0.068);
}
.tile:hover {
  transform: translateY(-5px);
}
.tile:focus-within {
  box-shadow: 0 0 0 2px var(--c-gray-800), 0 0 0 4px #48556a;
}

.tile a {
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: end;
  font-weight: 600;
}
.green {
  color: green;
}
.red {
  color: tomato;
}
.tile a .icon-button {
  color: inherit;
  border-color: inherit;
}
.tile a .icon-button:hover,
.tile a .icon-button:focus {
  background-color: transparent;
}
.tile a .icon-button:hover i,
.tile a .icon-button:focus i {
  transform: none;
}
.tile a:focus {
  box-shadow: none;
}
.tile a:after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.tile-header {
  color: #e3e3e3;
  font-size: 19px;
  font-weight: 600;
  margin: 5px;
}
.optionStyle {
  font-size: 16px;
  text-align: center;
  width: 100%;
  background-color: var(--app-bg);
  line-height: 2;
  border: none;
  color: white;
  font-weight: 600;
  height: 40px;
}
ngb-datepicker.bg-dark {
  color: #eee;
  background-color: #333;
}
ngb-datepicker ngb-dp-header,
ngb-datepicker ngb-dp-month-name {
  color: white !important;
  background-color: #333 !important;
}
.input-bar {
  height: 34px;
  display: flex;
  width: 100%;
  max-width: 450px;
  background-color: var(--button-bg);
  border-radius: 8px;
  box-shadow: 0 0 0 2px rgba(134, 140, 160, 0.02);
}
.input-bar input {
  width: 100%;
  height: 100%;
  border: none;
  background-color: var(--button-bg);
  border-radius: 8px;
  font-family: var(--body-font);
  font-size: 14px;
  font-weight: 500;
  padding: 0 16px 0 16px;
  background-size: 14px;
  background-repeat: no-repeat;
  background-position: 96%;
  color: #fff;
}
.infoWrapper {
  background: var(--app-bg);
  padding: 2px 6px;
  border-radius: 10px;
}
.infoCreditsWrapper {
  border: 1px solid var(--app-bg);
  padding: 2px 6px;
  border-radius: 10px;
  width: 100%;
}
p,
span,
div {
  color: white;
  font-size: 14px;
  font-weight: 600;
}
@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
