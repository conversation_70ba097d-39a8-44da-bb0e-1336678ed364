.topHeaderCell{
  margin-top: 40px !important;
}
.control {
  display: block;
  position: relative;
  padding-left: 30px;
  margin-bottom: 15px;
  cursor: pointer;
  font-size: 18px;
}
.control input {
  position: absolute;
  z-index: -1;
  opacity: 0;
}
.control__indicator {
  position: absolute;
  top: 0px;
  left: 0;
  height: 20px;
  width: 20px;
  background: var(--action-color-30);
  border-radius: 5px;
}
.control--radio .control__indicator {
  border-radius: 50%;
}
/* .control:hover input ~ .control__indicator,
  .control input:focus ~ .control__indicator {
    background: var(--action-color-50);
  } */
.control input:checked ~ .control__indicator {
  background: var(--action-color);
  border-radius: 5px;
}
/* .control:hover input:not([disabled]):checked ~ .control__indicator,
  .control input:checked:focus ~ .control__indicator {
    background:var(--action-color-50);
  } */
.control input:disabled ~ .control__indicator {
  background: #e6e6e6;
  opacity: 0.6;
  pointer-events: none;
  border-radius: 5px;
}
.control__indicator:after {
  content: "";
  position: absolute;
  display: none;
}
.control input:checked ~ .control__indicator:after {
  display: block;
}
.control--checkbox .control__indicator:after {
    left: 8px;
    top: 4px;
    width: 4px;
    height: 11px;
    border: 2px solid #fff;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    border-radius: 5px;
}
.control--checkbox input:disabled ~ .control__indicator:after {
  border-color: #7b7b7b;
}
.control--radio .control__indicator:after {
  left: 7px;
  top: 7px;
  height: 6px;
  width: 6px;
  border-radius: 50%;
  background: #fff;
}
.control--radio input:disabled ~ .control__indicator:after {
  background: #7b7b7b;
}
.addCategory {
  display: flex;
  width: 20%;
  border-bottom: 3px solid var(--app-bg);
  border-right: 3px solid var(--app-bg);
  border-left: 3px solid var(--app-bg);
  border-radius: 10px;
  overflow: hidden;
  padding: 5px 1em;
  line-height: 2;
  justify-content: center;
  background-color: #212d3b88;
}
.addCategory:hover {
  background-color: var(--app-bg);
}

.addEntireCategory {
  align-items: center;
  display: flex;
  height: fit-content;
  gap: 1em;
  border: 1px solid var(--app-bg);
  border-radius: 10px;
  padding: 5px 1em;
}
.addEntireCategory:hover {
  background-color: var(--app-bg);
}
.addEntireCategory:hover > div {
  background-color: var(--action-color-30) !important;
}
.plusButton {
  background-color: var(--app-bg);
  height: 35px;
  width: 35px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  cursor: pointer;
}

.plusButton:hover {
  background-color: var(--action-color-30) !important;
}
.cancelButton:hover {
  background-color: var(--action-color-50) !important;
  border-radius: 50%;
}
.modalButtonsCancel {
  color: white;
  font-weight: 600;
  border: 1px solid var(--action-color-50);
  cursor: pointer;
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 2;
  border-radius: 10px;
}
.modalButtonsDelete {
  color: white;
  font-weight: 600;
  background-color: var(--action-color);
  cursor: pointer;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 2;
  border-radius: 10px;
}
.modalButtonsDelete:hover {
  border: 1px solid var(--action-color-50);
  background-color: var(--action-color-50);
}
.modalButtonsCancel:hover {
  background-color: var(--action-color-30);
}
.creditsInput {
  border: none;
  flex: 1;
  outline: none;
  height: 100%;
  padding: 0 20px;
  font-size: 20px;
  background-color: var(--search-area-bg);
  color: var(--main-color);
  font-weight: 600;
  width: 100%;
  border-radius: 10px;
}
.creditsInput:placeholder {
  color: var(--main-color);
  opacity: 0.6;
}

p,
span,
div {
  color: white;
  font-size: 16px;
  font-weight: 600;
}

@media screen and (max-width: 980px) {
  p,
  span,
  div {
    color: white;
    font-size: 14px;
    font-weight: 600;
  }
}

@media screen and (max-width: 520px) {
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}

@media screen and (max-height: 980px) {
  :root {
    --font-size-xxxl: 35px;
    --font-size-xxl: 25px;
    --font-size-xl: 25px;
    --font-size-l: 20px;
    --font-size-m: 15px;
    --font-size-s: 10px;
    --font-size-xs: 5px;
  }
  p,
  span,
  div {
    color: white;
    font-size: 13px;
    font-weight: 600;
  }
}
