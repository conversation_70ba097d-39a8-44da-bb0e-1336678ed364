import { Component, OnInit } from '@angular/core';
import { Options, LabelType } from 'ngx-slider-v2';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/services/auth.service';
import { Router } from '@angular/router';
import { NotificationNativeService } from 'src/app/shared/services/notification-native.service';
import { ImageService } from 'src/app/shared/services/image.service';
import { environment } from 'src/environments/environment';
import { SocketService } from 'src/app/shared/services/socket.service';
const BACKEND_Image_URL = environment.imageUrl + '/post/getImage/';

@Component({
  selector: 'app-send-notification',
  templateUrl: './send-notification.component.html',
  styleUrls: ['./send-notification.component.css'],
})
export class SendNotificationComponent implements OnInit {

  modalReference = null;  

  allUsers = []
  allUsersId = []
  me: any;

  nativeTitle = ''
  socketTitle = ''
  description = ''
  directLink = '/notification-details'
  thumbnail = null;
  thumbnailFile: File = null; 
  thumbnailImageId = null;

  today = new Date()

  addDirectLink = false

  socket
  loading = false
  constructor(
    private socketService: SocketService,
    private router: Router,
    private toastrService: ToastrService,
    private imageService: ImageService,
    private authService: AuthService,
    private notificationNativeService: NotificationNativeService,
    private modalService: NgbModal) {
      this.socket = this.socketService.setupSocketConnection();
      this.authService.user.pipe().subscribe((appUser) => {
        this.me = appUser;
      });
    }

  openUserSearchModal(searchUser) {
    this.modalReference = this.modalService.open(searchUser, { centered: true });
  }

  ngOnInit(): void {}

  redirectTo(uri:string){
    this.router.navigateByUrl('/en/'+ uri)
      .then(() =>{
      });
  }
  checkboxChange(event){
    if(event?.checked == true){
      this.addDirectLink = true;
    }
    if(event?.checked == false){
      this.directLink = '/notification-details'
      this.addDirectLink = false;
    }
  }

  async removeUser(user){
      this.allUsers.splice(
        this.allUsers.indexOf(user._id), 1);
  } 

  async selectUser(user){
    console.log("user: ",user)

    if(this.allUsers.some(e => e._id === user._id)){
      this.toastrService.error('This user is already selected');
    }else{
      this.allUsersId = [user._id,...this.allUsersId]
      this.allUsers = [user,...this.allUsers]
    }
    this.modalReference.close();
  }

  selectThumbnail(event) {
    console.log("thumbnailFile")
    if(event.target.files[0]){
      this.thumbnailFile = event.target.files[0]
      var reader = new FileReader();
      reader.readAsDataURL(event.target.files[0])
      reader.onload = (_event) => {
        this.thumbnail = reader.result;
      }
  
    }

  }

  async submit(){
    this.loading = true
    // this.nativeTitle 
    // this.socketTitle 
    // this.description 
    // this.directLink
    // this.thumbnail 
    // this.thumbnailFile

    // console.log("this.nativeTitle: ",this.nativeTitle )
    // console.log("this.socketTitle: ",this.socketTitle)
    // console.log("this.description: ",this.description)
    // console.log("this.directLink: ",this.directLink)
    // console.log("this.thumbnail: ",this.thumbnail )
    // console.log("this.thumbnailFile: ",this.thumbnailFile )
    let array = []
    array.push(this.thumbnailFile)
    console.log("submit: ")
    // if(!this.thumbnailFile){
    //   this.toastrService.error('No File Selected');    
    //   return        
    // }
  //  this.imageService.uploadOriginalImage(
  //     array
  //   ).subscribe(  respond  => {
  //     console.log("uploadOriginalImage: ",respond)
  //           if( respond.status = "success"){
  //                 console.log("uploadOriginalImage: ",respond.data._id.toString())
  //                 this.thumbnail = `${BACKEND_Image_URL}${respond.data._id.toString()}`;
  //                 this.thumbnailImageId = respond.data._id
  //                 this.create()
  //           }
  //       },
  //       respond_error => {
  //           let error_message = respond_error.error.message;
  //           this.toastrService.error(error_message);            
  //       }
  //   );
    

  // let firstText = this.me.name + ' has liked ';
  // let extraText = ' your status';
  // if (event.post.isImageContent) {
  //   extraText = ' your image post';
  // }
  // if (event.post.isVideoContent) {
  //   extraText = ' your video post';
  // }
  // if (event.post.isShareLink) {
  //   extraText = ' link you have shared';
  // }
  // if (event.post.isPromotion) {
  //   extraText = ' promotion you have shared';
  // }

  // let text = firstText + extraText;
  // let directURL =
  //   '/post-details/' + event?.post?._id + '?tabSelected=likes';

  console.log("allUsersId: ",this.allUsersId)

        this.createNotification(
          this.me?._id,
          '630a2e5d4989aae851a657e4',
          this.allUsersId[0],
          this.nativeTitle,
          false,
          false,
          false,
          false,
          false,
          false,
          null,
          this.directLink
        );
        this.redirectTo('list-notifications')
        this.loading = false

  }

  create(){
    this.notificationNativeService.create({
      thumbnailId: this.thumbnailImageId,
      nativeTitle: this.nativeTitle,
      socketTitle: this.socketTitle,
      description: this.description, 
      directLink: this.directLink,
      createdBy: this.me?._id
    }).subscribe(
      async (result) => {
        if (result.status == 'success') {

          this.nativeTitle = ''
          this.socketTitle = ''
          this.description = ''
          this.directLink = '/notification-details'
          this.addDirectLink = false

          this.thumbnailFile  = null; 
          this.thumbnail = null;
      
          this.redirectTo('list-notifications')
        }
      },
      (respond_error) => {
        this.toastrService.error(
          respond_error?.error.message,
          respond_error?.name
        );
      }
    );
  }


  createNotification(
    userId,
    senderId,
    receiverId,
    text,
    isGiftTransaction,
    isPayPerViewTransaction,
    isPromotionSharingTransaction,
    isShareLink,
    isVideoContent,
    isPromotion,
    post,
    directURL
  ){
    this.socket.emit('createNotification', {
      userId: userId.toString(),
      from : senderId?.toString(),
      to : receiverId?.toString(),
      text : text,
      isGiftTransaction: isGiftTransaction,
      isPayPerViewTransaction: isPayPerViewTransaction,
      isPromotionSharingTransaction: isPromotionSharingTransaction,
      isShareLink: isShareLink,
      isVideoContent: isVideoContent,
      isPromotion: isPromotion,
      post: post?.toString(),
      directURL: directURL
    });
  }

  
}
