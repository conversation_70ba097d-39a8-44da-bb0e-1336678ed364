import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { User } from 'src/app/shared/models/user';
import { AuthService } from 'src/app/shared/services/auth.service';
import { MinistryOfFinanceService } from 'src/app/shared/services/ministry-of-finance.service';

@Component({
  selector: 'app-list-of-transcations-of-ministry-of-finance',
  templateUrl: './list-of-transcations-of-ministry-of-finance.component.html',
  styleUrls: ['./list-of-transcations-of-ministry-of-finance.component.css']
})
export class ListOfTranscationsOfMinistryOfFinanceComponent implements OnInit {

  timeout: any = null;
  resultSearch: any = null;
  searchText = '';

  public pageNo: number = 1;
  public pageNoTotal: number = 1;
  count: number = 0;
  resultsPerPage: number = 5;
  data: any[] = [];
  loading = false;

  me: User;

    constructor(
    private toastrService: ToastrService,
    private ministryOfFinanceService: MinistryOfFinanceService,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private modalService: NgbModal) {
      this.authService.user.pipe().subscribe((appUser) => {
        this.me = appUser;
      });
    }

    ngOnInit(): void {
      this.loading = true;
      this.activatedRoute.queryParams.subscribe((params) => {
        this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;
      });
      this.getAll();
    }

    public setPage(page: number) {
      this.data = [];
      this.loading = true;
      this.router
        .navigate([], {
          relativeTo: this.activatedRoute,
          queryParams: { page: page },
          queryParamsHandling: 'merge', // preserve the existing query params in the route
          skipLocationChange: false, // do trigger navigation
        })
        .finally(() => {
            if(this.searchText === ''){
              this.getAll();
            }else{
              this.getAllResultSearch()
            }
        });
    }
  
    getAll() {
      this.ministryOfFinanceService.getAll(this.pageNo, this.resultsPerPage).subscribe(
        async (result) => {
          if (result.status == 'success') {
            this.count = result.data.count;
            this.data = result.data.data;
            this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
            this.loading = false;
          }
        },
        (respond_error) => {
          this.toastrService.error(
            respond_error?.error.message,
            respond_error?.name
          );
        }
      );
    }
  
    resultsPerPageChanged(event){
      this.loading = true;
      this.count =  1
      this.pageNoTotal = 1
      this.data = []
      this.resultsPerPage = Number(event)
      this.setPage(1)
    }


    searchUser(searchText) {
      clearTimeout(this.timeout);
      var $this = this;
      this.timeout = setTimeout(function () {
        if (searchText.keyCode != 13) {
          $this.loading = true
          $this.searchSocket(searchText.target.value);
        }else{
          $this.loading = true
          $this.getAll()
        }
      }, 1000);
    } 
    public searchSocket(searchText) {
      this.searchText = searchText.toString();
      if(this.searchText == ''){
        this.loading = true
        this.setPage(1)
        return
      }
      this.data = []
      this.setPage(1)
    }
    getAllResultSearch() {
      if(this.searchText == '') return
      this.loading = true;
      this.count =  1
      this.pageNoTotal = 1
      this.data = []
      this.ministryOfFinanceService.search(this.searchText, this.me._id,this.pageNo,this.resultsPerPage).subscribe(
        (result) => {
          if (result.status == 'success') {
            this.count = result.data.count;
            this.data = result.data.data;
            this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;
            this.loading = false;
          }
        },
        (respond_error) => {
          this.loading = false;
          // this.toastrService.error(
          //   respond_error?.error.message,
          //   respond_error?.name
          // );
        }
      );
    }
    
}
